#datasource postgres
spring.datasource.url=jdbc:h2:mem:smith;DB_CLOSE_DELAY=-1;MODE=Postgresql;
spring.datasource.platform=h2

#spring.datasource.initialize=true
#spring.jpa.format-sql=false
#spring.jpa.show-sql=false
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=create
spring.jpa.hibernate.use-new-id-generator-mappings=false
spring.jpa.database-platform=h2
spring.jpa.properties.hibernate.default_schema=smith
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgresqlDialect