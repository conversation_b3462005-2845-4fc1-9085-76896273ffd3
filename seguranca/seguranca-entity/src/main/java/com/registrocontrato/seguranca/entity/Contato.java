package com.registrocontrato.seguranca.entity;

import java.util.Date;

import javax.persistence.*;
import javax.validation.constraints.AssertTrue;

import com.registrocontrato.infra.entity.AssuntoContato;
import com.registrocontrato.infra.entity.BaseEntity;

@Entity
@Table(schema = "seguranca")
@SequenceGenerator(name = "sequence", sequenceName = "seguranca.seq_contato", schema = "seguranca", allocationSize = 1)
public class Contato extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private String nome;

	private String email;

	private String telefone;

	private String mensagem;

	@AssertTrue(message = "Você precisa concordar com os nosso termos.")
	private Boolean concordar;

	@Temporal(TemporalType.TIMESTAMP)
	private Date dataCadastro = new Date();

	private AssuntoContato assuntoContato;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getTelefone() {
		return telefone;
	}

	public void setTelefone(String telefone) {
		this.telefone = telefone;
	}

	public String getMensagem() {
		return mensagem;
	}

	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}

	public Date getDataCadastro() {
		return dataCadastro;
	}

	public void setDataCadastro(Date dataCadastro) {
		this.dataCadastro = dataCadastro;
	}

	public Boolean getConcordar() {
		return concordar;
	}

	public void setConcordar(Boolean concordar) {
		this.concordar = concordar;
	}

	public AssuntoContato getAssuntoContato() {
		return assuntoContato;
	}

	public void setAssuntoContato(AssuntoContato assuntoContato) {
		this.assuntoContato = assuntoContato;
	}
}
