package com.registrocontrato.seguranca.entity;

import com.registrocontrato.infra.entity.BaseEntity;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "cookies", schema = "seguranca")
@Audited
public class Cookies extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private Date dataAceite;
    private String termosAceitos;

    public Date getDataAceite() {
        return dataAceite;
    }

    public void setDataAceite(Date dataAceite) {
        this.dataAceite = dataAceite;
    }

    public String getTermosAceitos() {
        return termosAceitos;
    }

    public void setTermosAceitos(String termosAceitos) {
        this.termosAceitos = termosAceitos;
    }

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
