<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Grupo</h4>

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Visualizar</h6>
                                    <f:metadata>
                                        <f:viewParam id="id" name="id" value="#{grupoBean.idToEdit}"/>
                                        <f:viewAction action="#{grupoBean.loadDetails()}"/>
                                    </f:metadata>

                                    <form jsf:id="form" jsf:prependId="false">
                                        <ui:include src="form-inputs.xhtml">
                                            <ui:param name="disabled" value="disabled"></ui:param>
                                        </ui:include>
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/grupo/list.xhtml"
                                                   class="btn btn-default">Voltar</a>
                                                <sec:authorize ifAnyGranted="CADASTRAR_GRUPO">
                                                    <a class="btn btn-primary btn-cons" title="Editar"
                                                       href="#{request.contextPath}/grupo/form-update.xhtml?id=#{grupoBean.entity.id}">
                                                        Editar </a>
                                                </sec:authorize>
                                                <sec:authorize ifAnyGranted="EXCLUIR_GRUPO">
                                                    <h:commandLink styleClass="btn btn-primary btn-cons" title="Excluir"
                                                                   immediate="true"
                                                                   onclick="return confirm('Confirma a exclusão?')"
                                                                   action="#{grupoBean.delete(grupoBean.entity.id)}">
                                                        Excluir
                                                    </h:commandLink>
                                                </sec:authorize>
                                                <input type="hidden" name="${_csrf.parameterName}"
                                                       value="${_csrf.token}"/>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>

</ui:composition>
