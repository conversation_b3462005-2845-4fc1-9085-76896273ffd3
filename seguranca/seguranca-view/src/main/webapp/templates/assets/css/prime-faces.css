.ui-state-default {
	background: transparent;
	color: #676a6c;
}

.alert {
	list-style: none;
}

#messages {
    padding-left: 0px;
}

.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
	background-image: none !important; 
   	background-color: #c9c9c9!important;
    border-color: #cccccc !important;
	color: #333;
}

.ui-widget-header {
    border: 1px solid #eeeeee;
    background: transparent;
    color: #333333;
    font-weight: bold;
}

.ui-widget {
	font-family: "Noto Sans", sans-serif !important;
	font-size: 1em !important;
}

.ui-tree .ui-treenode-label {
    display: inline-block;
    vertical-align: middle;
    padding: 10px 0px;
}

.ui-fileupload-buttonbar {
    background: #f5f5f5;
	border: none !important;
    padding: 15px 20px;
    margin: -2px;
    border-radius: 0 !important;
}

.ui-fileupload-content {
    padding: 15px 20px;
    margin: -2px;
    border: 2px solid #f5f5f5 !important;
    border-radius: 0 !important;
}

.ui-fileupload-choose, .ui-fileupload-upload, .ui-fileupload-cancel {
   	background-color: #ffffff !important;
    border-color: #cccccc !important;
    border-radius: 2px !important;
    padding: 6px 14px !important;
    outline: none !important;
    font-size: 14px !important;
    margin-left: 0px !important;
    background-image: none !important;
    color: #007bff !important;
    font-weight: 1;
}

.ui-fileupload-upload {
   	background-color: #ffffff !important;
    border-color: #cccccc !important;
    border-radius: 2px !important;
    padding: 6px 14px !important;
    outline: none !important;
    font-size: 14px !important;
    margin-left: 0px !important;
    background-image: none !important;
    color: #007bff !important;
    font-weight: 1;
}

.ui-icon-plusthick, .ui-icon-arrowreturnthick-1-n, .ui-icon-cancel {
	display: none;
}

.ui-button-text-icon-left .ui-button-text {
	padding: 2px !important;
}

.ui-tree-container .n1 {
    background-color: #c2d1e0;
}

.ui-tree .ui-treenode .n2{
    background-color: #e1eaf4;
}

.ui-tree .ui-treenode .n3{
    background-color: #fff;
}

.ui-paginator .ui-paginator-next .ui-icon, .ui-paginator .ui-paginator-prev .ui-icon,
	.ui-paginator .ui-paginator-first .ui-icon, .ui-paginator .ui-paginator-last .ui-icon
	{
	height: 20px;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control
	{
	color: #444;	
}

.ui-state-disabled {
	background-color: #e9ecef !important;
    opacity: 1;
}

.ui-messages-error-icon {
	background-image: none !important; 
}

.ui-inputfield {
	border: none !important;
	box-shadow: none !important;
    padding: 2px !important;	
    	color: black !important;
}

.ui-datatable-resizable table {
    table-layout: auto;
}

.table td, .table th {
	vertical-align: inherit;
}

.ui-inputfield {
	max-width: 100px;
}

.ui-selectonemenu .ui-selectonemenu-trigger {

    border-right: 0;
    border-top: 0;
    border-bottom: 0;
    cursor: pointer;
    width: 16px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0px;
    padding: 0 0px;
    border-left: 0;

}

.ui-selectonemenu-label {

	    max-width: 100%;

}

.ui-selectonemenu {

    min-width: 0px !important;

}

.ui-corner-right {
    padding-top: 6px !important;
}

.ui-widget select{
}

.ui-selectonemenu-panel .ui-selectonemenu-filter-container {
    position: relative;
    margin: 0;
    padding: .4em;
    display: inline-block;
    width:100%;
}

.ui-selectonemenu .ui-selectonemenu-label {

    display: block;
    border: 0;
    white-space: nowrap;
    overflow: hidden;
    font-weight: normal;
    font-family: inherit;
    width: 100%;
    text-align: left;

}

.ui-selectonemenu-panel .ui-selectonemenu-list-item {

    border: 0 none;
    margin: 1px 0;
    padding: 3px 5px;
    text-align: left;
    white-space: nowrap;
	font-family: inherit;
}

.ui-selectonemenu.ui-state-disabled .ui-selectonemenu-trigger, .ui-selectonemenu.ui-state-disabled .ui-selectonemenu-label {

    cursor: default;
	background-color: #e9ecef !important;
	opacity: 1;
}

.hasDatepicker {
    min-width: 100%;
}
