/* =============
   Form Advanced
============= */

/* Bootstrap tagsinput */
.bootstrap-tagsinput {
  box-shadow: none;
  padding: 3px 7px 5px;
  width: 100%;
  line-height: 1;
  border: 1px solid #e3e3e3;

  .label-info {
    background-color: $custom;
    display: inline-block;
    padding: 4px 8px;
    font-size: 13px;
    margin: 3px 1px;
    border-radius: 3px;
  }
}

/* File style */
.icon-span-filestyle {
    padding-right: 5px;
}

/* Select 2 */
.select2-container {
  .select2-selection--single {
    border: 1px solid #E3E3E3 !important;
    height: 38px !important;

    .select2-selection__rendered {
      line-height: 36px !important;
      padding-left: 12px !important;
    }

    .select2-selection__arrow {
      height: 34px;
      width: 34px;
      right: 3px;

      b{
        border-color: #999 transparent transparent transparent;
        border-width: 6px 6px 0 6px;
      }
    }
  }
}

.select2-container--open {
  .select2-selection--single {

    .select2-selection__arrow {

      b{
        border-color: transparent transparent #999 transparent !important;
        border-width: 0 6px 6px 6px !important;
      }
    }
  }
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: $custom;
}
.select2-results__option {
  padding: 6px 12px;
}

.select2-dropdown {
  border: 1px solid #e3e3e3 !important;
  padding-top: 5px;
  box-shadow: ( 0 2px 2px rgba(0, 0, 0, .15));
}
.select2-search {
  input{
    border: 1px solid #e3e3e3 !important;
  }
}

.select2-container .select2-selection--multiple {
  min-height: 38px !important;
  border: 1px solid #e3e3e3 !important;

  .select2-selection__rendered {
    padding: 2px 10px;
  }
  .select2-search__field {
    margin-top: 7px;
    border: 0 !important;
  }
  .select2-selection__choice {
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 1px;
    padding: 0 7px;
  }
}


/* Form validation */
.parsley-error {
  border-color: $danger !important;
}
.parsley-errors-list {
  display: none;
  margin: 0;
  padding: 0;
}
.parsley-errors-list.filled {
  display: none;
}
.parsley-errors-list > li {
  font-size: 12px;
  list-style: none;
  color: $danger;
  margin-top: 5px;
}


// Timepicker
.bootstrap-timepicker-widget table td input {
  border: 1px solid rgba($dark,0.3);
  width: 35px;
}


// Datepicker
.datepicker-dropdown {
  padding: 10px !important;
}

.datepicker td, .datepicker th {
    width: 30px;
    height: 30px;
}

.datepicker>div {
  display: block;
}

.datepicker table tr td.active:hover, .datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:active, .datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active, .datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled], .datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled],
.datepicker table tr td.selected, .datepicker table tr td.selected:hover,
.datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover{
  background-color: $custom !important;
  color: $white !important;
  background-image: none !important;
  text-shadow: none !important;
}

.datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover {
  background-color: $success !important;
  color: $white !important;
  background-image: none !important;
}

.datepicker-inline {
    border: 2px solid rgba($dark,0.1);
}

//Daterange Picker

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: $custom;
}
.daterangepicker .input-mini.active {
    border: 1px solid rgba($dark,0.3);
}
.daterangepicker .ranges li {
  border-radius: 2px;
  color: $dark;
  font-weight: 600;
  font-size: 12px;
  font-family: $font-secondary;
}
.daterangepicker select.hourselect, .daterangepicker select.minuteselect,
.daterangepicker select.secondselect, .daterangepicker select.ampmselect{
  border: 1px solid rgba($dark,0.3);
  padding: 2px;
  width: 60px;
}
.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
  background-color: $custom;
  border: 1px solid $custom;
  color: $white;
}


/* Summernote */
.note-editor {
  position: relative;

  .btn-default {
    background-color: transparent;
    border-color: transparent !important;
  }
  .btn-group-sm > .btn, .btn-sm {
    padding: 8px 12px !important;
  }
  .note-toolbar {
    background-color: $light;
    border-bottom: 1px solid $light;
    margin: 0;
  }
  .note-statusbar {
    background-color: $white;
    .note-resizebar {
      border-top: none;
      height: 15px;
      padding-top: 3px;
    }
  }
}
.note-editor.note-frame {
  border: 1px solid $light !important;
}

.note-popover {
  .popover {
    .popover-content {
      padding: 5px 0 10px 5px;
    }
  }

  .btn-default {
    background-color: transparent;
    border-color: transparent !important;
  }
  .btn-group-sm > .btn, .btn-sm {
    padding: 8px 12px !important;
  }
}

.note-toolbar {
  padding: 5px 0 10px 5px;
}