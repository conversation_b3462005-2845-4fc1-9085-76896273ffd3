<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf">

	<div class="row">
		<div class="col-lg-12">
			<div class="form-group form-group-default required">
				<label>Nome</label>
				<input jsf:id="nome" type="text"
					jsf:value="#{agenteBean.entity.nome}" required="true" jsf:required="true" maxlength="255"
					jsf:label="Nome" class="form-control" disabled="#{disabled}"/>
			</div>
		</div>
	</div>
</ui:composition>
