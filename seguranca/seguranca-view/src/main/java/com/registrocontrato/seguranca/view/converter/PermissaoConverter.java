package com.registrocontrato.seguranca.view.converter;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.registrocontrato.infra.entity.Sistema;
import com.registrocontrato.seguranca.entity.Permissao;

@FacesConverter(value = "permissaoConverter")
public class PermissaoConverter implements Converter {

	@Override
	public Object getAsObject(FacesContext context, UIComponent component, String value) {
		if (value != null) {
			String[] split = value.replace("Permissao[", "").replaceAll("]", "").split(",");
			Permissao permissao = new Permissao();
			permissao.setId(Long.parseLong(split[0].trim()));
			permissao.setNome(split[1].trim());
			permissao.setSistema(Sistema.valueOf(split[2].trim()));
			return permissao;
		}
		return null;
	}

	@Override
	public String getAsString(FacesContext context, UIComponent component, Object value) {
		if (value != null) {
			return value.toString();
		}
		return "";
	}

}
