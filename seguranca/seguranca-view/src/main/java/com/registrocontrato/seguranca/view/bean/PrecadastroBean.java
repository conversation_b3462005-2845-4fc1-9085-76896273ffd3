package com.registrocontrato.seguranca.view.bean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.seguranca.entity.PreCadastro;
import com.registrocontrato.seguranca.service.PreCadastroService;
import com.registrocontrato.seguranca.service.dto.PreCadastroDTO;

@Controller
@ViewScope
public class PrecadastroBean extends BaseCrud<PreCadastro, PreCadastroDTO> {

	private static final long serialVersionUID = 1L;

	@Autowired
	private PreCadastroService service;
	
	@Override
	public String save() {
		String retorno = super.save();
		if(retorno != null) {
			return "/public/pre-cadastro.xhtml?faces-redirect=true";
		}
		return retorno;
	}

	@Override
	public BaseService<PreCadastro, PreCadastroDTO> getService() {
		return service;
	}

}