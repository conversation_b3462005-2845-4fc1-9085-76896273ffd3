package com.registrocontrato.seguranca.view.converter;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.registrocontrato.infra.entity.BaseEntity;

@FacesConverter(value = "entityConverter")
public class EntityConverter implements Converter {
	
	protected final Log logger = LogFactory.getLog(getClass());

	@Override
	public Object getAsObject(FacesContext context, UIComponent component, String value) {
		if (value != null) {
			String clazz = "com.registrocontrato.registro.entity." + value.substring(0, value.indexOf("["));
			String id = value.substring(value.indexOf("=") + 1, value.indexOf("]")).trim();
			try {
				BaseEntity entity = (BaseEntity) Class.forName(clazz).newInstance();
				entity.setId(Long.parseLong(id));
				return entity;
			} catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
				logger.error(e);
			}
		}
		return null;
	}

	@Override
	public String getAsString(FacesContext context, UIComponent component, Object value) {
		if (value != null) {
			return value.toString();
		}
		return "";
	}

}
