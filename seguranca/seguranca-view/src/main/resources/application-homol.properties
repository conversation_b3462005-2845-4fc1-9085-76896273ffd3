#datasource 
spring.datasource.jndi-name=java:jboss/datasources/SegurancaDS

#jpa
spring.jpa.hibernate.dialect=PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=false
spring.jpa.format-sql=false

#cas
cas.server=${CAS_AMBIENTE}/auth
cas.local.login=${CAS_AMBIENTE}/seguranca/login/cas

#jsf
jsf.PROJECT_STAGE=Development

#Certificados
certificados.clientes=/storage/certificados/

#log
logging.level=INFO
logging.config=classpath:/log4j2.xml

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=desenv
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

file.dir=/data/contratos/

# metabase 
METABASE_SITE_URL=https://homol.analytics.placecon.com.br:443
METABASE_SECRET_KEY=9da444c07417d0592077c77aa829fd71a58887a2c09f26dc4ae18d6eca93dceb

sistema=SEGURANCA
