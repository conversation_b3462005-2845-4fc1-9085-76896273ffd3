-- <PERSON><PERSON><PERSON><PERSON><PERSON>
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTE<PERSON>) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','<PERSON><PERSON><PERSON><PERSON><PERSON>_ADMINISTRACAO','<PERSON><PERSON><PERSON><PERSON><PERSON>');
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTEMA) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','R<PERSON><PERSON><PERSON><PERSON>_FINANCEIRA','RELATOR<PERSON>');
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTEMA) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','<PERSON><PERSON><PERSON><PERSON><PERSON>_DETRAN','REL<PERSON>ORIO');
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTEMA) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','<PERSON><PERSON><PERSON><PERSON><PERSON>_COBRANCA','<PERSON><PERSON><PERSON><PERSON><PERSON>');
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTEMA) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','RELATORIO_MARCAS','RELATORIO');
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTEMA) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','RELATORIO_REGISTROS_EFETUADOS','RELATORIO');
INSERT INTO SEGURANCA.PERMISSAO (ID,ATIVO,NOME,SISTEMA) values (NEXTVAL('SEGURANCA.SEQ_PERMISSAO'),'S','RELATORIO_USUARIO_POR_FINANCEIRA','RELATORIO');


INSERT INTO SEGURANCA.PERMISSOES_GRUPO (PERMISSOES_ID, GRUPO_ID) 
SELECT P.ID, (SELECT G.ID FROM SEGURANCA.GRUPO G WHERE G.NOME = 'Administrador') 
FROM SEGURANCA.PERMISSAO P 
WHERE P.SISTEMA = 'RELATORIO' AND P.ID NOT IN (SELECT PG.PERMISSOES_ID FROM SEGURANCA.PERMISSOES_GRUPO PG);

INSERT INTO SEGURANCA.PERMISSOES_GRUPO (PERMISSOES_ID, GRUPO_ID) 
SELECT P.ID, (SELECT G.ID FROM SEGURANCA.GRUPO G WHERE G.NOME = 'Administrador Financeira') 
FROM SEGURANCA.PERMISSAO P 
WHERE P.SISTEMA = 'RELATORIO' AND P.NOME = 'RELATORIO_FINANCEIRA';

INSERT INTO SEGURANCA.PERMISSOES_GRUPO (PERMISSOES_ID, GRUPO_ID) 
SELECT P.ID, (SELECT G.ID FROM SEGURANCA.GRUPO G WHERE G.NOME = 'DETRAN') 
FROM SEGURANCA.PERMISSAO P 
WHERE P.SISTEMA = 'RELATORIO' AND P.NOME = 'RELATORIO_DETRAN';