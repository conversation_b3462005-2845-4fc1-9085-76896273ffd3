/*=====================
         Menu
  =====================*/

// Tobar
.topbar {
  .topbar-left {
    float: left;
    position: relative;
    width: 190px;
    z-index: 999;
  }
}

//Logo
.logo {
  color: $white !important;
  font-size: 20px;
  padding: 0;
  font-weight: 700;
  letter-spacing: .05em;
  line-height: 70px;
  text-transform: uppercase;
  display: block;

  img {
    height: 26px;
  }
  h1 {
    height: 50px;
    margin: 0 auto;
    text-align: center;
  }
  span {
    color: $custom;
  }
}

// Navbar
.navbar-default {
  background-color: $custom;
  border-radius: 0;
  z-index: 99;
  border: none;
  margin-bottom: 0;
  .navbar-nav > .open > a {
    background-color: rgba($muted, 0.2);
    color: $white;
    &:focus {
      background-color: rgba($muted, 0.2);
      color: $white;
    }
    &:hover {
      background-color: rgba($muted, 0.2);
      color: $white;
    }
  }
  .label {
    position: absolute;
    top: 12px;
    right: 7px;
  }
}

.navbar-nav {
  margin: 0;
}

.navbar-default .navbar-nav.top-navbar-items > li > a {
  line-height: 70px;
  padding: 0 15px;
}
.navbar-default .navbar-nav>li>a {
    color: darken($white,15%);

  &:hover {
    color: $white;
  }
}

.top-navbar-items-right {
  margin-right: -15px;

  a.menu-right-item {
    padding: 0 15px !important;

    i {
      line-height: 70px;
      font-size: 26px;
    }
  }
}

// Mobile Button
.button-menu-mobile {
  background: transparent;
  border: none;
  color: $white;
  font-size: 21px;
  line-height: 70px;
  padding: 0 15px;
  outline: none !important;
}

// User Profile
.profile {
  line-height: 70px !important;

  img {
    height: 36px;
    width: 36px;
  }
}

// Dropdown
.dropdown-menu-lg {
  width: 300px !important;
  .list-group {
    margin-bottom: 0;
  }
  .list-group-item {
    border: none;
    border-bottom: 2px solid $light;
    padding: 14px 20px;
    border-radius: 0 !important;
  }
  .media-heading {
    margin-bottom: 0;
  }
  .media-body {
    p {
      color: $muted;
      line-height: 16px;
    }
  }
}

// Notification
.notification-list {
  em {
    width: 36px;
    color: $white;
    text-align: center;
    height: 36px;
    line-height: 36px;
    border-radius: 50%;
    margin-top: 2px;
    font-size: 16px;
  }

  .media-left {
    float: left;
    display: inherit;
  }

  .media-body {
    display: inherit;
    width: auto;
    overflow: hidden;
    margin-left: 50px;

    h5 {
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
      width: 100%;
      overflow: hidden;
      line-height: 22px;
      font-size: 13px;
      font-weight: 700;
    }
  }
}

.notifi-title {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 11px 20px 15px;
  color: $dark;
}


// Search
.app-search {
  position: relative;
  margin: 18px 15px 15px 5px;

  a {
    position: absolute;
    top: 8px;
    right: 16px;
    color: $white;
  }

  .form-control,
  .form-control:focus {
    font-size: 13px;
    color: $white;
    padding-left: 20px;
    padding-right: 40px;
    background-color: darken($custom,7%);
    border: none;
    box-shadow: none;
    border-radius: 30px;
    height: 36px;
    font-weight: 600;
    width: 180px;
  }

  input::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: normal;
  }
  input:-moz-placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: normal;
  }
  input::-moz-placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: normal;
  }
  input:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-weight: normal;
  }
}

// Left bar
.scrollbar-wrapper {
  height: 100%;
  position: relative;
}

.sidebar-navigation {
  position: absolute;
  background-color: $white;
  width: 240px;
  top: 70px;
  -moz-transition: margin-left 0.3s;
  -o-transition: margin-left 0.3s;
  -webkit-transition: margin-left 0.3s;
  transition: margin-left 0.3s;
  background-repeat: repeat;
  z-index: 99;
}

// User detail
.user-details {
  min-height: 80px;
  padding: 20px;
  position: relative;

  img {
    position: relative;
    z-index: 9999;
    height: 48px;
    width: 48px;
  }

  .user-info {
    color: $dark;
    margin-left: 60px;
    position: relative;
    z-index: 99999;

    p {
      margin-bottom: 0;
      font-size: 13px;
    }

    a {
      color: $dark;
      display: block;
      font-weight: 600;
      padding-top: 5px;
    }
  }
}


// Page right content
#page-right-content {
  padding: 100px 5px 50px 5px;
  margin-top: -70px;
  min-height: 950px;
  position: relative !important;
  -moz-transition: margin-left 0.3s;
  -o-transition: margin-left 0.3s;
  -webkit-transition: margin-left 0.3s;
  transition: margin-left 0.3s;
}
// Footer
.footer {
  border-top: 2px solid $light;
  bottom: 0;
  left: 0;
  padding: 14px 20px;
  position: absolute;
  right: 0;
}

// Responsive menu
@media (max-width: 420px) {
  .dropdown-menu-lg {
    width: 240px !important;
  }
}

@media (max-width: 767px) {
  .logo-lg {
    display: none;
  }
  .logo-sm {
    display: inline-block !important;
  }
  .top-menu-item-xs {
    display: inline-block !important;
    float: left;
  }
  .topbar {
    width: 100%;
    .topbar-left {
      width: 70px;
    }
  }
  .navbar-nav .open .dropdown-menu {
    background-color: $white;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
    left: auto;
    position: absolute;
    right: 0;
  }

  .footer {
    text-align: center;
  }
}

@media (max-width: 991px) {
  .sidebar-navigation {
    margin-left: -2200px;
    z-index: 3;
  }
  body.nav-collapse .sidebar-navigation {
    margin-left: 0;
    box-shadow: 7px 8px 9px rgba(0, 0, 0, 0.11);
    top: 0;
    z-index: 999;
    position: fixed;
    bottom: 0;
  }
  .scrollbar-wrapper {
    overflow: auto;
  }
  #page-right-content {
    margin-left: 0;
  }
  body.nav-collapse #page-right-content{
    margin-left: 0;
  }
}

@media (max-width: 1024px) {
  #page-wrapper {
    width: 100% !important;
  }
  .topbar {
    width: 100%;
    .topbar-left {
      padding-left: 15px;
    }
  }
}


.btn-mobile-view {
  height: 30px;
  width: 30px;
  line-height: 32px;
  background-color: $custom;
  padding: 0;
  border-radius: 3px;
  float: right;
  z-index: 9999;
  position: relative;
  top: 10px;
  color: $white;
}


// Menu
.navigation-menu {
    list-style: none;
    margin: 0;
    padding: 0;

    li.active > a {
      color: $white !important;
      border-color: lighten($custom,30%) !important;
    }
    li.active li.active > a {
      color: $custom !important;
    }
    li.active li.active li.active > a {
      color: $custom !important;
    }
  }

  .navigation-menu > li {
    float: left;
    display: block;
    position: relative;
  }

  .navigation-menu > li > a {
    display: block;
    color: rgba($white,0.7);
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    line-height: 20px;
    padding-left: 15px;
    padding-right: 15px;
    margin: 0 3px;

    &:hover {
      color: $white;
    }
    &:focus {
      color: $white;
    }
    &:active {
      color: $white;
    }

    i {
      margin-right: 5px;
    }
  }

  .navigation-menu > li > a:hover, .navigation-menu > li > a:focus {
    background-color: transparent;
  }

  .navbar-toggle {
    border: 0;
    position: relative;
    width: 70px;
    height: 70px;
    padding: 0;
    margin: 0;
    cursor: pointer;

    &:hover {
      background-color: transparent;

      span {
        background-color: $white;
      }
    }

    &:focus {
      background-color: transparent;

      span {
        background-color: $white;
      }
    }

    .lines {
      width: 25px;
      display: block;
      position: relative;
      margin: 18px auto 10px auto;
      height: 18px;
    }

    span {
      height: 2px;
      width: 100%;
      background-color: $white;
      display: block;
      margin-bottom: 5px;
      -webkit-transition: -webkit-transform .5s ease;
      transition: -webkit-transform .5s ease;
      transition: transform .5s ease;
    }
  }

  .navbar-toggle.open {
    span {
      position: absolute;

      &:first-child {
        top: 6px;
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
      }

      &:nth-child(2) {
        visibility: hidden;
      }
      &:last-child {
        width: 100%;
        top: 6px;
        -webkit-transform: rotate(-45deg);
        transform: rotate(-45deg);
      }
    }
  }



  /*
  Responsive Menu
*/
@media (min-width: 1025px) {

  #topnav .navigation-menu > li > a {
    padding-top: 25px;
    padding-bottom: 23px;
    border-bottom: 2px solid transparent;
  }
  .navbar-toggle {
    display: none !important;
  }

  #topnav {
    .navigation-menu > li.last-elements .submenu {
      left: auto;
      right: 0;
    }
    .navigation-menu > li.last-elements .submenu > li.has-submenu .submenu {
      left: auto;
      right: 100%;
      margin-left: 0;
      margin-right: 10px;
    }

    .navigation-menu > li:hover a {
      color: $white;

      i {
        color: $white;
      }
    }
    .navigation-menu > li .submenu {
      position: absolute;
      top: 100%;
      left: 0;
      z-index: 1000;
      border: 1px solid #e7e7e7;
      padding: 15px 0;
      list-style: none;
      min-width: 200px;
      visibility: hidden;
      opacity: 0;
      margin-top: 10px;
      -webkit-transition: all .2s ease;
      transition: all .2s ease;
      background-color: $white;
      box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1);
    }
    .navigation-menu > li .submenu.megamenu {
      white-space: nowrap;
      width: auto;
    }
    .navigation-menu > li .submenu.megamenu > li {
      overflow: hidden;
      width: 200px;
      display: inline-block;
      vertical-align: top;
    }
    .navigation-menu > li .submenu > li.has-submenu > a:after {
      content: "\e649";
      font-family: "themify";
      position: absolute;
      right: 20px;
      font-size: 9px;
      top: 14px;
    }
    .navigation-menu > li .submenu > li .submenu {
      left: 100%;
      top: 0;
      margin-left: 10px;
      margin-top: -1px;
    }
    .navigation-menu > li .submenu li {
      position: relative;
    }
    .navigation-menu > li .submenu li ul {
      list-style: none;
      padding-left: 0;
      margin: 0;
    }
    .navigation-menu > li .submenu li a {
      display: block;
      padding: 8px 25px;
      clear: both;
      white-space: nowrap;
      color: $dark;
    }
    .navigation-menu > li .submenu li a:hover {
      color: $custom;
    }
    .navigation-menu > li .submenu li span {
      display: block;
      padding: 8px 25px;
      clear: both;
      line-height: 1.42857143;
      white-space: nowrap;
      font-size: 10px;
      text-transform: uppercase;
      letter-spacing: 2px;
      font-weight: 500;
      color: #949ba1;
    }

  }

  #topnav #navigation {
    display: block !important;
  }

}
@media (max-width: 1024px) {
  .wrapper {
    margin-top: 60px;
  }
  .container {
    width: auto;
  }

  #topnav {
    min-height: 60px;
    .navigation-menu {
      float: none;
      max-height: 400px;
    }
    .navigation-menu > li {
      float: none;
    }
    .navigation-menu > li > a {
      color: $dark;
      padding: 15px;

      i {
        display: inline-block;
        margin-right: 10px;
        margin-bottom: 0;
      }
    }
    .navigation-menu li.active > a {
      color: $custom !important;

      i {
        color: $custom;
      }
    }
    .navigation-menu > li > a:after {
      position: absolute;
      right: 15px;
    }
    .navigation-menu > li .submenu {
      display: none;
      list-style: none;
      padding-left: 20px;
      margin: 0;
    }
    .navigation-menu > li .submenu li a {
      display: block;
      position: relative;
      padding: 7px 20px;
      color: $dark;
    }
    .navigation-menu > li .submenu li a:hover {
      color: $custom;
    }
    .navigation-menu > li .submenu li.has-submenu > a:after {
      content: "\e64b";
      font-family: "themify";
      position: absolute;
      right: 30px;
      font-size: 12px;
    }
    .navigation-menu > li .submenu.open {
      display: block;
    }
    .navigation-menu > li .submenu .submenu {
      display: none;
      list-style: none;
    }
    .navigation-menu > li .submenu .submenu.open {
      display: block;
    }
    .navigation-menu > li .submenu.megamenu > li > ul {
      list-style: none;
      padding-left: 0;
    }
    .navigation-menu > li .submenu.megamenu > li > ul > li > span {
      display: block;
      position: relative;
      padding: 15px;
      text-transform: uppercase;
      font-size: 11px;
      letter-spacing: 2px;
    }
    .navigation-menu > li.has-submenu.open > a {
      color: $custom;
    }

    .navbar-header {
      float: left;
    }
  }

  #navigation {
    position: absolute;
    top: 70px;
    left: 0;
    width: 100%;
    display: none;
    height: auto;
    padding-bottom: 0;
    overflow: auto;
    border-top: 1px solid #e7e7e7;
    border-bottom: 1px solid #e7e7e7;
    background-color: $white;
  }
  #navigation.open {
    display: block;
    overflow-y: auto;
  }

}

@media (min-width: 768px) {
  #topnav .navigation-menu > li.has-submenu:hover > .submenu {
    visibility: visible;
    opacity: 1;
    margin-top: 0;
  }
  #topnav .navigation-menu > li.has-submenu:hover > .submenu > li.has-submenu:hover > .submenu {
    visibility: visible;
    opacity: 1;
    margin-left: 0;
    margin-right: 0;
  }
  .navbar-toggle {
    display: block;
  }
}

