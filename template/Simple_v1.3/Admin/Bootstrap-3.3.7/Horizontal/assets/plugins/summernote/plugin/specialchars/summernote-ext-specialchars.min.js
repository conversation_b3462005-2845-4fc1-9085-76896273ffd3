/*! Summernote v0.8.2 | (c) 2013-2015 <PERSON> and other contributors | MIT license */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof module&&module.exports?module.exports=a(require("jquery")):a(window.jQuery)}(function(a){a.extend(a.summernote.plugins,{specialchars:function(b){var c,d,e,f=this,g=a.summernote.ui,h=b.layoutInfo.editor,i=b.options,j=i.langInfo,k={UP:38,DOWN:40,LEFT:37,RIGHT:39,ENTER:13},l=15,m=35,n=0,o=["&quot;","&amp;","&lt;","&gt;","&iexcl;","&cent;","&pound;","&curren;","&yen;","&brvbar;","&sect;","&uml;","&copy;","&ordf;","&laquo;","&not;","&reg;","&macr;","&deg;","&plusmn;","&sup2;","&sup3;","&acute;","&micro;","&para;","&middot;","&cedil;","&sup1;","&ordm;","&raquo;","&frac14;","&frac12;","&frac34;","&iquest;","&times;","&divide;","&fnof;","&circ;","&tilde;","&ndash;","&mdash;","&lsquo;","&rsquo;","&sbquo;","&ldquo;","&rdquo;","&bdquo;","&dagger;","&Dagger;","&bull;","&hellip;","&permil;","&prime;","&Prime;","&lsaquo;","&rsaquo;","&oline;","&frasl;","&euro;","&image;","&weierp;","&real;","&trade;","&alefsym;","&larr;","&uarr;","&rarr;","&darr;","&harr;","&crarr;","&lArr;","&uArr;","&rArr;","&dArr;","&hArr;","&forall;","&part;","&exist;","&empty;","&nabla;","&isin;","&notin;","&ni;","&prod;","&sum;","&minus;","&lowast;","&radic;","&prop;","&infin;","&ang;","&and;","&or;","&cap;","&cup;","&int;","&there4;","&sim;","&cong;","&asymp;","&ne;","&equiv;","&le;","&ge;","&sub;","&sup;","&nsub;","&sube;","&supe;","&oplus;","&otimes;","&perp;","&sdot;","&lceil;","&rceil;","&lfloor;","&rfloor;","&loz;","&spades;","&clubs;","&hearts;","&diams;"];b.memo("button.specialCharacter",function(){return g.button({contents:'<i class="fa fa-font fa-flip-vertical">',tooltip:j.specialChar.specialChar,click:function(){f.show()}}).render()}),this.makeSpecialCharSetTable=function(){var b=a("<table/>");return a.each(o,function(c,d){var e=a("<td/>").addClass("note-specialchar-node"),f=c%l===0?a("<tr/>"):b.find("tr").last(),h=g.button({callback:function(a){a.html(d),a.attr("title",d),a.attr("data-value",encodeURIComponent(d)),a.css({width:m,"margin-right":"2px","margin-bottom":"2px"})}}).render();e.append(h),f.append(e),c%l===0&&b.append(f)}),n=b.find("tr").length,e=l,b},this.initialize=function(){var b=i.dialogsInBody?a(document.body):h,c='<div class="form-group row-fluid">'+this.makeSpecialCharSetTable()[0].outerHTML+"</div>";this.$dialog=g.dialog({title:j.specialChar.select,body:c}).render().appendTo(b)},this.show=function(){var c=b.invoke("editor.getSelectedText");b.invoke("editor.saveRange"),this.showSpecialCharDialog(c).then(function(c){b.invoke("editor.restoreRange");var d=a("<span></span>").html(c)[0];d&&b.invoke("editor.insertNode",d)}).fail(function(){b.invoke("editor.restoreRange")})},this.showSpecialCharDialog=function(b){return a.Deferred(function(h){function i(a){a&&(a.find("button").addClass("active"),t=a)}function j(a){a.find("button").removeClass("active"),t=null}function m(b,c){var d=null;return a.each(s,function(a,e){var f=Math.ceil((a+1)/l),g=(a+1)%l===0?l:(a+1)%l;return f===b&&g===c?(d=e,!1):void 0}),a(d)}function o(a){var b,f=s.length%e;k.LEFT===a?c>1?c-=1:1===d&&1===c?(c=f,d=n):(c=e,d-=1):k.RIGHT===a?d===n&&f===c?(c=1,d=1):e>c?c+=1:(c=1,d+=1):k.UP===a?1===d&&c>f?d=n-1:d-=1:k.DOWN===a&&(d+=1),d===n&&c>f?d=1:d>n?d=1:1>d&&(d=n),b=m(d,c),b&&(j(t),i(b))}function p(){t&&(h.resolve(decodeURIComponent(t.find("button").attr("data-value"))),r.modal("hide"))}function q(a){a.preventDefault();var b=a.keyCode;if(void 0!==b&&null!==b){if(u.indexOf(b)>-1){if(null===t)return i(s.eq(0)),c=1,void(d=1);o(b)}else b===v&&p();return!1}}var r=f.$dialog,s=r.find(".note-specialchar-node"),t=null,u=[k.UP,k.DOWN,k.LEFT,k.RIGHT],v=k.ENTER;if(j(s),b)for(var w=0;w<s.length;w++){var x=a(s[w]);x.text()===b&&(i(x),d=Math.ceil((w+1)/l),c=(w+1)%l)}g.onDialogShown(f.$dialog,function(){a(document).on("keydown",q),f.$dialog.find("button").tooltip(),s.on("click",function(b){b.preventDefault(),h.resolve(decodeURIComponent(a(b.currentTarget).find("button").attr("data-value"))),g.hideDialog(f.$dialog)})}),g.onDialogHidden(f.$dialog,function(){s.off("click"),f.$dialog.find("button").tooltip("destroy"),a(document).off("keydown",q),"pending"===h.state()&&h.reject()}),g.showDialog(f.$dialog)})}}})});