

.card-box {
  background-color: $white;
  border: 1px solid rgba($dark,0.2);
  padding: 20px;
  margin-bottom: 20px;
}

.header-title {
  margin-top: 0;
  font-weight: bold;
  border-bottom: 1px solid darken($light,10%);
  padding-bottom: 12px;
}

.font-normal {
  font-weight: normal;
}
.line-h-24 {
  line-height: 24px;
}


.m-0 {
  margin: 0 !important;
}
.m-t-0 {
  margin-top: 0 !important;
}
.m-t-10 {
  margin-top: 10px !important;
}
.m-l-10 {
  margin-left: 10px !important;
}
.m-r-10 {
    margin-right: 10px !important;
}
.m-t-30 {
  margin-top: 30px !important;
}
.m-b-0 {
  margin-bottom: 0 !important;
}
.m-b-5 {
  margin-bottom: 5px !important;
}
.m-b-20 {
    margin-bottom: 20px !important;
}
.m-b-30 {
    margin-bottom: 30px !important;
}
.p-0 {
  padding: 0 !important;
}
.p-20 {
  padding: 20px;
}
.p-b-0 {
  padding-bottom: 0 !important;
}
.p-t-50 {
  padding-top: 50px !important;
}

.font-13 {
  font-size: 13px;
}
.thumb-sm {
  height: 32px;
  width: 32px;
}
.thumb-xl {
    height: 120px;
    width: 120px;
}
.center-page {
  float: none !important;
  margin: 0 auto;
}


/* Text colors */
.text-custom {
  color: $custom;
}

.text-white {
  color: $white;
}

.text-danger {
  color: $danger;
}

.text-muted {
  color: $muted;
}

.text-primary {
  color: $primary;
}

.text-warning {
  color: $warning;
}

.text-success {
  color: $success;
}

.text-info {
  color: $info;
}

.text-dark {
  color: $dark !important;
}

/* Background colors */

.bg-custom {
  background-color: $custom !important;
}

.bg-primary {
  background-color: $primary !important;
}

.bg-success {
  background-color: $success !important;
}

.bg-info {
  background-color: $info !important;
}

.bg-warning {
  background-color: $warning !important;
}

.bg-danger {
  background-color: $danger !important;
}

.bg-muted {
  background-color: $muted !important;
}

.bg-white {
  background-color: $white !important;
}


/* Labels */
.label {
  font-weight: 600;
  letter-spacing: 0.05em;
  padding: .3em .6em .3em;
}

.label-white {
  background-color: rgba($white,0.7);
  color: $dark !important;
}

.label-custom {
  background-color: $custom;
}

.label-primary {
  background-color: $primary;
}

.label-success {
  background-color: $success;
}

.label-info {
  background-color: $info;
}

.label-warning {
  background-color: $warning;
}

.label-danger {
  background-color: $danger;
}

.label-dark {
  background-color: $dark;
}

/* Badge */
.badge {
  text-transform: uppercase;
  font-weight: 600;
  padding: 3px 5px;
  font-size: 12px;
  margin-top: 1px;
  background-color: $custom;
}

.badge-xs {
  font-size: 9px;
}

.badge-xs, .badge-sm {
  -webkit-transform: translate(0, -2px);
  -ms-transform: translate(0, -2px);
  -o-transform: translate(0, -2px);
  transform: translate(0, -2px);
}

.badge-white {
  background-color: rgba($white,0.7);
  color: $dark !important;
}

.badge-primary {
  background-color: $primary;
}

.badge-success {
  background-color: $success;
}

.badge-info {
  background-color: $info;
}

.badge-warning {
  background-color: $warning;
}

.badge-danger {
  background-color: $danger;
}

.badge-dark {
  background-color: $dark;
}


/* Pagination/ Pager */

.pagination > li:first-child > a,
.pagination > li:first-child > span {
   border-bottom-left-radius: 3px;
   border-top-left-radius: 3px;
}

.pagination > li:last-child > a,
.pagination > li:last-child > span {
   border-bottom-right-radius: 3px;
   border-top-right-radius: 3px;
}

.pagination > li > a,
.pagination > li > span {
   color: $dark;
}

.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
   background-color: $light;
}

.pagination-split li {
   margin-left: 5px;
   display: inline-block;
   float: left;
}

.pagination-split li:first-child {
   margin-left: 0;
}

.pagination-split li a {
   -moz-border-radius: 3px;
   -webkit-border-radius: 3px;
   border-radius: 3px;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
   background-color: $custom;
   border-color: $custom;
}

.pager li > a, .pager li > span {
   -moz-border-radius: 3px;
   -webkit-border-radius: 3px;
   border-radius: 3px;
   color: $dark;
}


.list-group-item.active, .list-group-item.active:focus,
.list-group-item.active:hover {
  background-color: $custom;
  border-color: $custom;
}
.list-group-item.active .list-group-item-text,
.list-group-item.active:focus .list-group-item-text,
.list-group-item.active:hover .list-group-item-text {
  color: rgba($white,0.7);
}


/* =================
   Popover / Tooltips
==================== */
/* Popover */
.popover {
  font-family: inherit;
  border: none;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  background-clip: padding-box;
  box-shadow: 0 0 28px rgba(0, 0, 0, 0.15);

  .popover-title {
    background-color: transparent;
    color: $custom;
    padding: 12px 15px;
    font-size: 15px;
  }

  .arrow {
    border-color: transparent !important;
  }
}

/* Tooltips */
.tooltip {

  .tooltip-inner {
    padding: 4px 10px;
    border-radius: 2px;
    background-color: darken($dark,12%);
  }
}

.tooltip.left .tooltip-arrow {
  border-left-color: darken($dark,12%);
}

.tooltip.top .tooltip-arrow {
  border-top-color: darken($dark,12%);
}

.tooltip.bottom .tooltip-arrow {
  border-bottom-color: darken($dark,12%);
}

.tooltip.right .tooltip-arrow {
  border-right-color: darken($dark,12%);
}


//Button Demo
.button-list {
  margin-left: -8px;
  margin-bottom: -12px;
  .btn {
    margin-bottom: 12px;
    margin-left: 8px;
  }
}

/* Demo only */
.icon-list-demo {

  div {
    cursor: pointer;
    line-height: 45px;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;

    p {
      margin-bottom: 0;
      line-height: inherit;
    }
  }

  i {
    text-align: center;
    vertical-align: middle;
    font-size: 24px;
    border: 1px solid darken($light,5%);
    width: 50px;
    height: 50px;
    line-height: 50px;
    margin-right: 12px;
    color: rgba(43, 61, 81, 0.7);
    border-radius: 3px;
    display: inline-block;
    transition: all 0.2s;
  }

  .col-md-4 {
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -moz-border-radius: 3px;
    background-clip: padding-box;
    margin-bottom: 10px;

    &:hover i {
      color: $custom;
    }
  }
}

.switchery-demo .switchery {
    margin-bottom: 10px;
    margin-right: 5px;
}
