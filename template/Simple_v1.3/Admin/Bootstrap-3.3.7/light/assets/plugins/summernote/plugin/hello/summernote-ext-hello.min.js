/*! Summernote v0.8.2 | (c) 2013-2015 <PERSON> and other contributors | MIT license */
!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof module&&module.exports?module.exports=a(require("jquery")):a(window.jQuery)}(function(a){a.extend(a.summernote.plugins,{hello:function(b){var c=this,d=a.summernote.ui;b.memo("button.hello",function(){var a=d.button({contents:'<i class="fa fa-child"/> Hello',tooltip:"hello",click:function(){c.$panel.show(),c.$panel.hide(500),b.invoke("editor.insertText","hello")}}),e=a.render();return e}),this.events={"summernote.init":function(a,b){console.log("summernote initialized",a,b)},"summernote.keyup":function(a,b){console.log("summernote keyup",a,b)}},this.initialize=function(){this.$panel=a('<div class="hello-panel"/>').css({position:"absolute",width:100,height:100,left:"50%",top:"50%",background:"red"}).hide(),this.$panel.appendTo("body")},this.destroy=function(){this.$panel.remove(),this.$panel=null}}})});