(function ($) {
  $.extend($.summernote.lang, {
    'ru-RU': {
      font: {
        bold: 'Полужирный',
        italic: 'Кур<PERSON>ив',
        underline: 'Подчёркнутый',
        clear: 'Убрать стили шрифта',
        height: 'Высота линии',
        name: 'Шриф<PERSON>',
        strikethrough: 'Зачёркнутый',
        subscript: 'Нижний индекс',
        superscript: 'Верхний индекс',
        size: 'Размер шрифта'
      },
      image: {
        image: 'Картинка',
        insert: 'Вставить картинку',
        resizeFull: 'Восстановить размер',
        resizeHalf: 'Уменьшить до 50%',
        resizeQuarter: 'Уменьшить до 25%',
        floatLeft: 'Расположить слева',
        floatRight: 'Расположить справа',
        floatNone: 'Расположение по-умолчанию',
        shapeRounded: 'Форма: Закругленная',
        shapeCircle: 'Форма: Круг',
        shapeThumbnail: 'Форма: Миниатюра',
        shapeNone: 'Форма: Нет',
        dragImageHere: 'Перетащите сюда картинку',
        dropImage: 'Перетащите картинку',
        selectFromFiles: 'Выбрать из файлов',
        url: 'URL картинки',
        remove: 'Удалить картинку'
      },
      video: {
        video: 'Видео',
        videoLink: 'Ссылка на видео',
        insert: 'Вставить видео',
        url: 'URL видео',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion или Youku)'
      },
      link: {
        link: 'Ссылка',
        insert: 'Вставить ссылку',
        unlink: 'Убрать ссылку',
        edit: 'Редактировать',
        textToDisplay: 'Отображаемый текст',
        url: 'URL для перехода',
        openInNewWindow: 'Открывать в новом окне'
      },
      table: {
        table: 'Таблица'
      },
      hr: {
        insert: 'Вставить горизонтальную линию'
      },
      style: {
        style: 'Стиль',
        p: 'Нормальный',
        blockquote: 'Цитата',
        pre: 'Код',
        h1: 'Заголовок 1',
        h2: 'Заголовок 2',
        h3: 'Заголовок 3',
        h4: 'Заголовок 4',
        h5: 'Заголовок 5',
        h6: 'Заголовок 6'
      },
      lists: {
        unordered: 'Маркированный список',
        ordered: 'Нумерованный список'
      },
      options: {
        help: 'Помощь',
        fullscreen: 'На весь экран',
        codeview: 'Исходный код'
      },
      paragraph: {
        paragraph: 'Параграф',
        outdent: 'Уменьшить отступ',
        indent: 'Увеличить отступ',
        left: 'Выровнять по левому краю',
        center: 'Выровнять по центру',
        right: 'Выровнять по правому краю',
        justify: 'Растянуть по ширине'
      },
      color: {
        recent: 'Последний цвет',
        more: 'Еще цвета',
        background: 'Цвет фона',
        foreground: 'Цвет шрифта',
        transparent: 'Прозрачный',
        setTransparent: 'Сделать прозрачным',
        reset: 'Сброс',
        resetToDefault: 'Восстановить умолчания'
      },
      shortcut: {
        shortcuts: 'Сочетания клавиш',
        close: 'Закрыть',
        textFormatting: 'Форматирование текста',
        action: 'Действие',
        paragraphFormatting: 'Форматирование параграфа',
        documentStyle: 'Стиль документа',
        extraKeys: 'Дополнительные комбинации'
      },
      history: {
        undo: 'Отменить',
        redo: 'Повтор'
      }
    }
  });
})(jQuery);
