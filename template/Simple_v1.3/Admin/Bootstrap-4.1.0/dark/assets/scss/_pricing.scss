/* =============
   Pricing
============= */
.pricing-column{
  position: relative;
  margin-bottom: 40px;

  .inner-box {
    position: relative;
    padding: 0 0 50px;
    background-color: $light;
  }

  .plan-header {
    position: relative;
    padding: 0 0 25px;
  }
  .plan-title {
    font-size: 16px;
    padding: 20px 20px 16px 20px;
    font-family: $font-secondary;
    margin: -2px -2px 25px -2px;
    color: $white;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-color: $primary;
  }
  .plan-price {
    font-size: 48px;
    font-family: $font-secondary;
    margin-bottom: 10px;
    color: $dark;
  }
  .plan-duration {
    font-size: 13px;
    color: $muted;
  }

  .plan-stats {
    position: relative;
    padding: 30px 20px 15px;

    li {
      margin-bottom: 15px;
      line-height: 24px;
    }
  }
}

.ribbon {
  position: absolute;
  left: 5px;
  top: -5px;
  z-index: 1;
  overflow: hidden;
  width: 75px;
  height: 75px;
  text-align: right;
  font-family: $font-secondary;

  span {
    font-size: 10px;
    font-weight: bold;
    color: $white;
    text-transform: uppercase;
    text-align: center;
    line-height: 20px;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    width: 100px;
    display: block;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
    background: $custom;
    background: linear-gradient($success 0%, $success 100%);
    position: absolute;
    top: 19px;
    letter-spacing: 1px;
    left: -21px;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 100%;
      z-index: -1;
      border-left: 3px solid darken($success,10%);
      border-right: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-top: 3px solid darken($success,10%);
    }
    &:after {
      content: "";
      position: absolute;
      right: 0;
      top: 100%;
      z-index: -1;
      border-left: 3px solid transparent;
      border-right: 3px solid darken($success,10%);
      border-bottom: 3px solid transparent;
      border-top: 3px solid darken($success,10%);
    }
  }
}