(function ($) {
  $.extend($.summernote.lang, {
    'hr-HR': {
      font: {
        bold: 'Podebljano',
        italic: 'Kurz<PERSON>',
        underline: 'Podvu<PERSON><PERSON>',
        clear: 'Ukloni stilove fonta',
        height: 'Visina linije',
        strikethrough: 'Precrtano',
        size: 'Veličina fonta'
      },
      image: {
        image: 'Slika',
        insert: 'Ubaci sliku',
        resizeFull: 'Puna veličina',
        resizeHalf: 'Umanji na 50%',
        resizeQuarter: 'Umanji na 25%',
        floatLeft: 'Poravnaj lijevo',
        floatRight: 'Poravna<PERSON> desno',
        floatNone: 'Bez poravnanja',
        dragImageHere: 'Povuci sliku ovdje',
        selectFromFiles: 'Izaberi iz datoteke',
        url: 'Adresa slike',
        remove: 'Ukloni sliku'
      },
      video: {
        video: 'Video',
        videoLink: 'Veza na video',
        insert: '<PERSON>baci video',
        url: 'URL video',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion ili Youku)'
      },
      link: {
        link: 'Veza',
        insert: 'Ubaci vezu',
        unlink: 'Ukloni vezu',
        edit: 'Uredi',
        textToDisplay: 'Tekst za prikaz',
        url: 'Internet adresa',
        openInNewWindow: 'Otvori u novom prozoru'
      },
      table: {
        table: 'Tablica'
      },
      hr: {
        insert: 'Ubaci horizontalnu liniju'
      },
      style: {
        style: 'Stil',
        p: 'pni',
        blockquote: 'Citat',
        pre: 'Kôd',
        h1: 'Naslov 1',
        h2: 'Naslov 2',
        h3: 'Naslov 3',
        h4: 'Naslov 4',
        h5: 'Naslov 5',
        h6: 'Naslov 6'
      },
      lists: {
        unordered: 'Obična lista',
        ordered: 'Numerirana lista'
      },
      options: {
        help: 'Pomoć',
        fullscreen: 'Preko cijelog ekrana',
        codeview: 'Izvorni kôd'
      },
      paragraph: {
        paragraph: 'Paragraf',
        outdent: 'Smanji uvlačenje',
        indent: 'Povećaj uvlačenje',
        left: 'Poravnaj lijevo',
        center: 'Centrirano',
        right: 'Poravnaj desno',
        justify: 'Poravnaj obostrano'
      },
      color: {
        recent: 'Posljednja boja',
        more: 'Više boja',
        background: 'Boja pozadine',
        foreground: 'Boja teksta',
        transparent: 'Prozirna',
        setTransparent: 'Prozirna',
        reset: 'Poništi',
        resetToDefault: 'Podrazumijevana'
      },
      shortcut: {
        shortcuts: 'Prečice s tipkovnice',
        close: 'Zatvori',
        textFormatting: 'Formatiranje teksta',
        action: 'Akcija',
        paragraphFormatting: 'Formatiranje paragrafa',
        documentStyle: 'Stil dokumenta',
        extraKeys: 'Dodatne kombinacije'
      },
      history: {
        undo: 'Poništi',
        redo: 'Ponovi'
      }
    }
  });
})(jQuery);
