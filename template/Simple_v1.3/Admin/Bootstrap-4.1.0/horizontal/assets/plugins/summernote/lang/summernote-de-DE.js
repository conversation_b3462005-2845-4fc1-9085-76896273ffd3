(function ($) {
  $.extend($.summernote.lang, {
    'de-DE': {
      font: {
        bold: 'Fe<PERSON>',
        italic: 'Kursiv',
        underline: 'Un<PERSON>treichen',
        clear: '<PERSON>ur<PERSON>set<PERSON>',
        height: '<PERSON><PERSON><PERSON>h<PERSON><PERSON>',
        strikethrough: 'Durchgestric<PERSON>',
        size: 'Schriftgröße'
      },
      image: {
        image: '<PERSON><PERSON>',
        insert: '<PERSON>ik einfügen',
        resizeFull: 'Originalgröße',
        resizeHalf: 'Größe 1/2',
        resizeQuarter: 'Größe 1/4',
        floatLeft: 'Linksbündig',
        floatRight: 'Rechtsbündig',
        floatNone: 'Kein Textfluss',
        shapeRounded: 'Rahmen: Abgerundet',
        shapeCircle: '<PERSON>hm<PERSON>: Kreisförmig',
        shapeThumbnail: 'Rahmen: Thumbnail',
        shapeNone: 'Kein Rahmen',
        dragImageHere: '<PERSON><PERSON>hen Sie ein Bild mit der Maus hierher',
        selectFromFiles: '<PERSON><PERSON><PERSON>en Sie eine Datei aus',
        maximumFileSize: 'Maximale Dateigröße',
        maximumFileSizeError: 'Maximale Dateigröße überschritten',
        url: 'Grafik URL',
        remove: 'Grafik entfernen'
      },
      video: {
        video: 'Video',
        videoLink: 'Video Link',
        insert: 'Video einfügen',
        url: 'Video URL?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion oder Youku)'
      },
      link: {
        link: 'Link',
        insert: 'Link einfügen',
        unlink: 'Link entfernen',
        edit: 'Editieren',
        textToDisplay: 'Anzeigetext',
        url: 'Ziel des Links?',
        openInNewWindow: 'In einem neuen Fenster öffnen'
      },
      table: {
        table: 'Tabelle'
      },
      hr: {
        insert: 'Eine horizontale Linie einfügen'
      },
      style: {
        style: 'Stil',
        p: 'Normal',
        blockquote: 'Zitat',
        pre: 'Quellcode',
        h1: 'Überschrift 1',
        h2: 'Überschrift 2',
        h3: 'Überschrift 3',
        h4: 'Überschrift 4',
        h5: 'Überschrift 5',
        h6: 'Überschrift 6'
      },
      lists: {
        unordered: 'Aufzählung',
        ordered: 'Nummerierung'
      },
      options: {
        help: 'Hilfe',
        fullscreen: 'Vollbild',
        codeview: 'HTML-Code anzeigen'
      },
      paragraph: {
        paragraph: 'Absatz',
        outdent: 'Einzug vergrößern',
        indent: 'Einzug verkleinern',
        left: 'Links ausrichten',
        center: 'Zentriert ausrichten',
        right: 'Rechts ausrichten',
        justify: 'Blocksatz'
      },
      color: {
        recent: 'Letzte Farbe',
        more: 'Mehr Farben',
        background: 'Hintergrundfarbe',
        foreground: 'Schriftfarbe',
        transparent: 'Transparenz',
        setTransparent: 'Transparenz setzen',
        reset: 'Zurücksetzen',
        resetToDefault: 'Auf Standard zurücksetzen'
      },
      shortcut: {
        shortcuts: 'Tastenkürzel',
        close: 'Schließen',
        textFormatting: 'Textformatierung',
        action: 'Aktion',
        paragraphFormatting: 'Absatzformatierung',
        documentStyle: 'Dokumentenstil'
      },
      history: {
        undo: 'Rückgängig',
        redo: 'Wiederholen'
      }

    }
  });
})(jQuery);
