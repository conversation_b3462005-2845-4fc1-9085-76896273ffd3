<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>SimpleAdmin - Responsive Admin Dashboard Template</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta content="A fully featured admin theme which can be used to build CRM, CMS, etc." name="description" />
        <meta content="Coderthemes" name="author" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />

        <!-- App favicon -->
        <link rel="shortcut icon" href="assets/images/favicon.ico">

        <!-- App css -->
        <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/style.css" rel="stylesheet" type="text/css" />

        <script src="assets/js/modernizr.min.js"></script>

    </head>

    <body>

        <!-- Navigation Bar-->
        <header id="topnav">
            <div class="topbar-main">
                <div class="container-fluid">

                    <!-- Logo container-->
                    <div class="logo">
                        <!-- Text Logo -->
                        <!--<a href="index.html" class="logo">-->
                            <!--<span class="logo-small"><i class="mdi mdi-radar"></i></span>-->
                            <!--<span class="logo-large"><i class="mdi mdi-radar"></i> Simple</span>-->
                        <!--</a>-->
                        <!-- Image Logo -->
                        <a href="index.html" class="logo">
                            <img src="assets/images/logo_sm.png" alt="" height="26" class="logo-small">
                            <img src="assets/images/logo.png" alt="" height="26" class="logo-large">
                        </a>

                    </div>
                    <!-- End Logo container-->


                    <div class="navbar-custom">
                        <div id="navigation">
                            <!-- Navigation Menu-->
                            <ul class="navigation-menu">
                                <li class="has-submenu">
                                    <a href="index.html">
                                        <span><i class="ti-home"></i></span><span> Dashboard </span> </a>
                                </li>

                                <li class="has-submenu">
                                    <a href="#"> <span><i class="ti-files"></i></span><span> Pages </span> </a>
                                    <ul class="submenu">
                                        <li><a href="pages-login.html">Login</a></li>
                                        <li><a href="pages-register.html">Register</a></li>
                                        <li><a href="pages-forget-password.html">Forget Password</a></li>
                                        <li><a href="pages-lock-screen.html">Lock-screen</a></li>
                                        <li><a href="pages-blank.html">Blank page</a></li>
                                        <li><a href="pages-404.html">Error 404</a></li>
                                        <li><a href="pages-confirm-mail.html">Confirm Mail</a></li>
                                        <li><a href="pages-session-expired.html">Session Expired</a></li>
                                    </ul>
                                </li>

                                <li class="has-submenu">
                                    <a href="#"><span><i class="ti-spray"></i></span><span> Other </span> </a>
                                    <ul class="submenu">
                                        <li>
                                            <a href="ui-elements.html">UI Elements</a>
                                        </li>

                                        <li class="has-submenu">
                                            <a href="#">Components</a>
                                            <ul class="submenu">
                                                <li><a href="components-range-slider.html">Range Slider</a></li>
                                                <li><a href="components-alerts.html">Alerts</a></li>
                                                <li><a href="components-icons.html">Icons</a></li>
                                                <li><a href="components-widgets.html">Widgets</a></li>
                                            </ul>
                                        </li>

                                        <li>
                                            <a href="typography.html"> Typography </a>
                                        </li>

                                        <li class="has-submenu">
                                            <a href="#"> Forms </a>
                                            <ul class="submenu">
                                                <li><a href="forms-general.html">General Elements</a></li>
                                                <li><a href="forms-advanced.html">Advanced Form</a></li>
                                            </ul>
                                        </li>

                                        <li class="has-submenu">
                                            <a href="#"> Tables </a>
                                            <ul class="submenu">
                                                <li><a href="tables-basic.html">Basic tables</a></li>
                                                <li><a href="tables-advanced.html">Advanced tables</a></li>
                                            </ul>
                                        </li>

                                        <li>
                                            <a href="charts.html"> Charts </a>
                                        </li>

                                        <li>
                                            <a href="maps.html"> Maps </a>
                                        </li>

                                    </ul>
                                </li>

                                <li class="has-submenu">
                                    <a href="#"> <span><i class="ti-widget"></i></span><span> Extra Pages </span> </a>
                                    <ul class="submenu">
                                        <li><a href="extras-timeline.html">Timeline</a></li>
                                        <li><a href="extras-invoice.html">Invoice</a></li>
                                        <li><a href="extras-profile.html">Profile</a></li>
                                        <li><a href="extras-calendar.html">Calendar</a></li>
                                        <li><a href="extras-faqs.html">FAQs</a></li>
                                        <li><a href="extras-pricing.html">Pricing</a></li>
                                        <li><a href="extras-contacts.html">Contacts</a></li>
                                    </ul>
                                </li>

                            </ul>
                            <!-- End navigation menu -->
                        </div> <!-- end #navigation -->
                    </div> <!-- end navbar-custom -->


                    <div class="menu-extras topbar-custom">

                        <ul class="list-unstyled topbar-right-menu float-right mb-0">

                            <li class="menu-item">
                                <!-- Mobile menu toggle-->
                                <a class="navbar-toggle nav-link">
                                    <div class="lines">
                                        <span></span>
                                        <span></span>
                                        <span></span>
                                    </div>
                                </a>
                                <!-- End mobile menu toggle-->
                            </li>
                            <li class="dropdown notification-list hide-phone">
                                <a class="nav-link dropdown-toggle waves-effect waves-light nav-user" data-toggle="dropdown" href="#" role="button"
                                   aria-haspopup="false" aria-expanded="false">
                                    <i class="mdi mdi-earth"></i> English  <i class="mdi mdi-chevron-down"></i>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right">

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item">
                                        Spanish
                                    </a>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item">
                                        Italian
                                    </a>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item">
                                        French
                                    </a>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item">
                                        Russian
                                    </a>

                                </div>
                            </li>

                            <li class="dropdown notification-list">
                                <a class="nav-link dropdown-toggle arrow-none waves-light waves-effect" data-toggle="dropdown" href="#" role="button"
                                   aria-haspopup="false" aria-expanded="false">
                                    <i class="mdi mdi-bell noti-icon"></i>
                                    <span class="badge badge-danger badge-pill noti-icon-badge">4</span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right dropdown-lg">

                                    <!-- item-->
                                    <div class="dropdown-item noti-title">
                                        <h6 class="m-0"><span class="float-right"><a href="" class="text-dark"><small>Clear All</small></a> </span>Notification</h6>
                                    </div>

                                    <div class="slimscroll" style="max-height: 220px;">
                                        <!-- item-->
                                        <a href="javascript:void(0);" class="dropdown-item notify-item">
                                            <div class="notify-icon bg-success"><i class="mdi mdi-comment-account-outline"></i></div>
                                            <p class="notify-details">Caleb Flakelar commented on Admin<small class="text-muted">1 min ago</small></p>
                                        </a>

                                        <!-- item-->
                                        <a href="javascript:void(0);" class="dropdown-item notify-item">
                                            <div class="notify-icon bg-info"><i class="mdi mdi-account-plus"></i></div>
                                            <p class="notify-details">New user registered.<small class="text-muted">5 hours ago</small></p>
                                        </a>

                                        <!-- item-->
                                        <a href="javascript:void(0);" class="dropdown-item notify-item">
                                            <div class="notify-icon bg-danger"><i class="mdi mdi-heart"></i></div>
                                            <p class="notify-details">Carlos Crouch liked <b>Admin</b><small class="text-muted">3 days ago</small></p>
                                        </a>

                                        <!-- item-->
                                        <a href="javascript:void(0);" class="dropdown-item notify-item">
                                            <div class="notify-icon bg-warning"><i class="mdi mdi-comment-account-outline"></i></div>
                                            <p class="notify-details">Caleb Flakelar commented on Admin<small class="text-muted">4 days ago</small></p>
                                        </a>

                                        <!-- item-->
                                        <a href="javascript:void(0);" class="dropdown-item notify-item">
                                            <div class="notify-icon bg-custom"><i class="mdi mdi-heart"></i></div>
                                            <p class="notify-details">Carlos Crouch liked <b>Admin</b><small class="text-muted">13 days ago</small></p>
                                        </a>
                                    </div>

                                    <!-- All-->
                                    <a href="javascript:void(0);" class="dropdown-item text-center text-primary notify-item notify-all">
                                        View all <i class="fi-arrow-right"></i>
                                    </a>

                                </div>
                            </li>

                            <li class="dropdown notification-list">
                                <a class="nav-link dropdown-toggle waves-effect waves-light nav-user" data-toggle="dropdown" href="#" role="button"
                                   aria-haspopup="false" aria-expanded="false">
                                    <img src="assets/images/users/avatar-1.jpg" alt="user" class="rounded-circle"> <span class="ml-1 pro-user-name">Anderson <i class="mdi mdi-chevron-down"></i> </span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right profile-dropdown ">
                                    <!-- item-->
                                    <div class="dropdown-item noti-title">
                                        <h6 class="text-overflow m-0">Welcome !</h6>
                                    </div>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item notify-item">
                                        <i class="ti-user"></i> <span>My Account</span>
                                    </a>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item notify-item">
                                        <i class="ti-settings"></i> <span>Settings</span>
                                    </a>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item notify-item">
                                        <i class="ti-lock"></i> <span>Lock Screen</span>
                                    </a>

                                    <!-- item-->
                                    <a href="javascript:void(0);" class="dropdown-item notify-item">
                                        <i class="ti-power-off"></i> <span>Logout</span>
                                    </a>

                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- end menu-extras -->

                    <div class="clearfix"></div>

                </div> <!-- end container -->
            </div>
            <!-- end topbar-main -->

        </header>
        <!-- End Navigation Bar-->


        <div class="wrapper">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title m-t-0 m-b-20">Form Elements</h4>
                    </div>
                </div>


                <div class="row">
                    <div class="col-md-6">
                        <form class="form-horizontal" role="form">
                            <div class="form-group">
                                <label class="col-md-2 col-form-label">Text</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" value="Some text value...">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 col-form-label" for="example-email">Email</label>
                                <div class="col-md-10">
                                    <input type="email" id="example-email" name="example-email" class="form-control" placeholder="Email">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 col-form-label">Password</label>
                                <div class="col-md-10">
                                    <input type="password" class="form-control" value="password">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-md-2 col-form-label">Placeholder</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" placeholder="placeholder">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 col-form-label">Text area</label>
                                <div class="col-md-10">
                                    <textarea class="form-control" rows="5"></textarea>
                                </div>
                            </div>



                        </form>
                    </div>

                    <div class="col-md-6">
                        <form class="form-horizontal" role="form">

                            <div class="form-group">
                                <label class="col-md-2 col-form-label">Readonly</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" readonly="" value="Readonly value">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 col-form-label">Disabled</label>
                                <div class="col-md-10">
                                    <input type="text" class="form-control" disabled="" value="Disabled value">
                                </div>
                            </div>


                            <div class="form-group">
                                <label class="col-sm-2 col-form-label">Static control</label>
                                <div class="col-sm-10">
                                    <p class="form-control-static"><EMAIL></p>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 col-form-label">Helping text</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" placeholder="Helping text">
                                    <span class="help-block"><small>A block of help text that breaks onto a new line and may extend beyond one line.</small></span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-2 col-form-label">Input Select</label>
                                <div class="col-sm-10">
                                    <select class="form-control">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                        <option>4</option>
                                        <option>5</option>
                                    </select>
                                    <h6 class="font-13 mt-3">Multiple select</h6>
                                    <select multiple="" class="form-control">
                                        <option>1</option>
                                        <option>2</option>
                                        <option>3</option>
                                        <option>4</option>
                                        <option>5</option>
                                    </select>
                                </div>
                            </div>

                        </form>
                    </div>

                </div>
                <!-- end row -->

                <div class="row m-b-20 p-t-50">
                    <div class="col-md-6">
                        <h6 class="m-t-0 header-title font-14"><b>Input States</b></h6>
                        <p class="text-muted m-b-30 font-13">
                            Bootstrap includes validation styles for error, warning, and success states on form controls.
                        </p>

                        <form class="form-horizontal" role="form">

                            <div class="form-group has-success row">
                                <label class="col-md-3 col-form-label" for="state-success">Success</label>
                                <div class="col-md-6">
                                    <input type="text" id="state-success" name="state-success" class="form-control" placeholder="...">
                                </div>
                            </div>


                            <div class="form-group has-warning row">
                                <label class="col-md-3 col-form-label" for="state-warning">Warning</label>
                                <div class="col-md-6">
                                    <input type="text" id="state-warning" name="state-warning" class="form-control" placeholder="...">
                                </div>
                            </div>

                            <div class="form-group has-error row">
                                <label class="col-md-3 col-form-label" for="state-danger">Danger</label>
                                <div class="col-md-6">
                                    <input type="text" id="state-danger" name="state-danger" class="form-control" placeholder="...">
                                </div>
                            </div>

                        </form>
                    </div>


                    <div class="col-md-6">
                        <h6 class="m-t-0 header-title font-14"><b>Input Sizes</b></h6>
                        <p class="text-muted m-b-30 font-13">
                            Set heights using classes like <code>.input-lg</code>, and set widths using grid column classes like <code>.col-lg-*</code>.
                        </p>

                        <form role="form" class="form-horizontal">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label" for="example-input-small">Small</label>
                                <div class="col-sm-6">
                                    <input type="text" id="example-input-small" name="example-input-small" class="form-control input-sm" placeholder=".input-sm">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label" for="example-input-normal">Normal</label>
                                <div class="col-sm-6">
                                    <input type="text" id="example-input-normal" name="example-input-normal" class="form-control" placeholder="Normal">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label" for="example-input-large">Large</label>
                                <div class="col-sm-6">
                                    <input type="text" id="example-input-large" name="example-input-large" class="form-control input-lg" placeholder=".input-lg">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Grid Sizes</label>
                                <div class="col-sm-4">
                                    <input type="text" class="form-control" placeholder=".col-sm-4">
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-5 offset-sm-3">
                                    <input type="text" class="form-control" placeholder=".col-sm-5">
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-6 offset-sm-3">
                                    <input type="text" class="form-control" placeholder=".col-sm-6">
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-9 offset-sm-3">
                                    <input type="text" class="form-control" placeholder=".col-sm-9">
                                </div>
                            </div>
                        </form>
                    </div> <!-- end col -->
                </div>
                <!-- end row -->


                <h4 class="p-t-50 font-14 header-title"><b>Input groups</b></h4>
                <p class="text-muted m-b-30 font-13">
                    Extend form controls by adding text or buttons before, after, or on both sides of any text-based <code>&lt;input&gt;</code>. Use <code>.input-group</code> with an <code>.input-group-addon</code> or <code>.input-group-btn</code> to prepend or append elements to a single <code>.form-control</code>.
                </p>

                <div class="row">
                    <div class="col-md-4">
                        <form role="form">
                            <div class="form-group">
                                <label class="col-form-label" for="example-input1-group1">Static</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-user"></i></span>
                                    </div>
                                    <input type="text" id="example-input1-group1" name="example-input1-group1" class="form-control" placeholder="Username">
                                </div>

                                <div class="input-group m-t-10">
                                    <input type="email" id="example-input2-group1" name="example-input2-group1" class="form-control" placeholder="Email">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><i class="fa fa-envelope-o"></i></span>
                                    </div>
                                </div>
                                <div class="input-group m-t-10">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fa fa-dollar"></i></span>
                                    </div>
                                    <input type="text" id="example-input3-group1" name="example-input3-group1" class="form-control" placeholder="..">
                                    <div class="input-group-append">
                                        <span class="input-group-text">.00</span>
                                    </div>
                                </div>
                            </div> <!-- form-group -->

                        </form>
                    </div>

                    <div class="col-md-4">
                        <form role="form">
                            <div class="form-group">
                                <label class="col-form-label" for="example-input1-group2">Buttons</label>
                                <div class="input-group">
                                    <span class="input-group-prepend">
                                    <button type="button" class="btn btn-primary"><i class="fa fa-search"></i></button>
                                    </span>
                                    <input type="text" id="example-input1-group2" name="example-input1-group2" class="form-control" placeholder="Search">
                                </div>
                                <div class="input-group m-t-10">
                                    <input type="email" id="example-input2-group2" name="example-input2-group2" class="form-control" placeholder="Email">
                                    <span class="input-group-append">
                                    <button type="button" class="btn btn-primary">Submit</button>
                                    </span>
                                </div>
                                <div class="input-group m-t-10">
                                    <span class="input-group-prepend">
                                    <button type="button" class="btn btn-primary"><i class="fa fa-facebook"></i></button>
                                    </span>
                                    <input type="text" id="example-input3-group2" name="example-input3-group2" class="form-control" placeholder="Search">
                                    <span class="input-group-append">
                                    <button type="button" class="btn btn-primary"><i class="fa fa-twitter"></i></button>
                                    </span>
                                </div>
                            </div> <!-- form-group -->

                        </form>
                    </div>

                    <div class="col-md-4">
                        <form role="form">
                            <div class="form-group">
                                <label class="col-form-label" for="example-input1-group3">Dropdowns</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" style="overflow: hidden; position: relative;">Action <span class="caret"></span></button>
                                        <ul class="dropdown-menu">
                                            <li><a href="javascript:void(0)" class="dropdown-item">Action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Another action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Separated link</a></li>
                                        </ul>
                                    </div>
                                    <input type="text" id="example-input1-group3" name="example-input1-group3" class="form-control" placeholder="Username">
                                </div>
                                <div class="input-group m-t-10">
                                    <input type="email" id="example-input2-group3" name="example-input2-group3" class="form-control" placeholder="Email">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" style="overflow: hidden; position: relative;">Action <span class="caret"></span></button>
                                        <ul class="dropdown-menu dropdown-menu-right">
                                            <li><a href="javascript:void(0)" class="dropdown-item">Action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Another action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Separated link</a></li>
                                        </ul>
                                    </div>
                                </div>


                                <div class="input-group m-t-10">
                                    <div class="input-group-prepend">
                                        <button type="button" class="btn btn-primary" style="overflow: hidden; position: relative;">Action</button>
                                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" style="overflow: hidden; position: relative;"><span class="caret"></span></button>
                                        <ul class="dropdown-menu">
                                            <li><a href="javascript:void(0)" class="dropdown-item">Action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Another action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Separated link</a></li>
                                        </ul>
                                    </div>
                                    <input type="text" id="example-input3-group3" name="example-input3-group3" class="form-control" placeholder="..">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" style="overflow: hidden; position: relative;"><span class="caret"></span></button>
                                        <ul class="dropdown-menu dropdown-menu-right">
                                            <li><a href="javascript:void(0)" class="dropdown-item">Action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Another action</a></li>
                                            <li><a href="javascript:void(0)" class="dropdown-item">Separated link</a></li>
                                        </ul>
                                    </div>

                                </div>
                            </div> <!-- form-group -->

                        </form>
                    </div>
                </div>
                <!-- end row -->


                <div class="row p-t-50">
                    <div class="col-md-6 m-b-20">
                        <h4 class="m-b-20 font-14 header-title"><b>Basic example</b></h4>

                        <form role="form">
                            <div class="form-group">
                                <label for="exampleInputEmail1">Email address</label>
                                <input type="email" class="form-control" id="exampleInputEmail1" placeholder="Enter email">
                            </div>
                            <div class="form-group">
                                <label for="exampleInputPassword1">Password</label>
                                <input type="password" class="form-control" id="exampleInputPassword1" placeholder="Password">
                            </div>
                            <div class="form-group">
                                <div class="checkbox checkbox-primary">
                                    <input id="checkbox1" type="checkbox">
                                    <label for="checkbox1">
                                        Remember me
                                    </label>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>
                    </div>

                    <div class="col-md-6 m-b-20">
                        <h4 class="m-b-20 font-14 header-title"><b>Horizontal form</b></h4>

                        <form class="form-horizontal m-t-10" role="form">
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-3 col-form-label">Email</label>
                                <div class="col-sm-9">
                                    <input type="email" class="form-control" id="inputEmail3" placeholder="Email">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-3 col-form-label">Password</label>
                                <div class="col-sm-9">
                                    <input type="password" class="form-control" id="inputPassword3" placeholder="Password">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword4" class="col-sm-3 col-form-label">Re Password</label>
                                <div class="col-sm-9">
                                    <input type="password" class="form-control" id="inputPassword4" placeholder="Retype Password">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <div class="checkbox checkbox-info">
                                        <input id="checkbox222" type="checkbox">
                                        <label for="checkbox222">
                                            Check me out !
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group m-b-0">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <button type="submit" class="btn btn-info">Sign in</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- end row -->


                <h4 class="p-t-50 header-title">Checkbox and Radio</h4>

                <div class="row">
                    <div class="col-md-4">
                        <div class="m-b-20">

                            <h5 class="font-14 mt-3">Basic</h5>

                            <p class="text-muted font-13 m-b-15">
                                Supports bootstrap brand colors: <code>.checkbox-primary</code>, <code>.checkbox-info</code> etc.
                            </p>

                            <div class="checkbox">
                                <input id="checkbox0" type="checkbox">
                                <label for="checkbox0">
                                    Default
                                </label>
                            </div>
                            <div class="checkbox checkbox-custom">
                                <input id="checkbox111" type="checkbox">
                                <label for="checkbox111">
                                    Custom
                                </label>
                            </div>
                            <div class="checkbox checkbox-primary">
                                <input id="checkbox2" type="checkbox" checked>
                                <label for="checkbox2">
                                    Primary
                                </label>
                            </div>
                            <div class="checkbox checkbox-success">
                                <input id="checkbox3" type="checkbox">
                                <label for="checkbox3">
                                    Success
                                </label>
                            </div>
                            <div class="checkbox checkbox-info">
                                <input id="checkbox4" type="checkbox">
                                <label for="checkbox4">
                                    Info
                                </label>
                            </div>
                            <div class="checkbox checkbox-warning">
                                <input id="checkbox5" type="checkbox" checked>
                                <label for="checkbox5">
                                    Warning
                                </label>
                            </div>
                            <div class="checkbox checkbox-danger">
                                <input id="checkbox6" type="checkbox" checked>
                                <label for="checkbox6">
                                    Danger
                                </label>
                            </div>
                            <div class="checkbox checkbox-dark">
                                <input id="checkbox6c" type="checkbox">
                                <label for="checkbox6c">
                                    Dark
                                </label>
                            </div>



                            <p class="text-muted font-13 m-b-15 mt-4">Checkboxes without label text <code>.checkbox-single</code></p>
                            <div class="checkbox checkbox-single">
                                <input type="checkbox" id="singleCheckbox1" value="option1" aria-label="Single checkbox One">
                                <label></label>
                            </div>
                            <div class="checkbox checkbox-primary checkbox-single">
                                <input type="checkbox" id="singleCheckbox2" value="option2" checked aria-label="Single checkbox Two">
                                <label></label>
                            </div>


                            <p class="text-muted font-13 m-b-15 mt-4">Inline checkboxes</p>
                            <div class="checkbox form-check-inline">
                                <input type="checkbox" id="inlineCheckbox1" value="option1">
                                <label for="inlineCheckbox1"> Inline One </label>
                            </div>
                            <div class="checkbox checkbox-success form-check-inline">
                                <input type="checkbox" id="inlineCheckbox2" value="option1" checked>
                                <label for="inlineCheckbox2"> Inline Two </label>
                            </div>
                            <div class="checkbox checkbox-pink form-check-inline">
                                <input type="checkbox" id="inlineCheckbox3" value="option1">
                                <label for="inlineCheckbox3"> Inline Three </label>
                            </div>
                        </div>
                    </div><!-- end col -->

                    <div class="col-md-4">
                        <div class="m-b-20">

                            <h5 class="font-14 mt-3">Circled</h5>

                            <p class="text-muted font-13 m-b-15">
                                <code>.checkbox-circle</code> for roundness.
                            </p>

                            <div class="checkbox checkbox-custom checkbox-circle">
                                <input id="checkbox08" type="checkbox" checked>
                                <label for="checkbox08">
                                    Custom
                                </label>
                            </div>

                            <div class="checkbox checkbox-circle">
                                <input id="checkbox7" type="checkbox">
                                <label for="checkbox7">
                                    Simply Rounded
                                </label>
                            </div>
                            <div class="checkbox checkbox-info checkbox-circle">
                                <input id="checkbox8" type="checkbox" checked>
                                <label for="checkbox8">
                                    Info
                                </label>
                            </div>
                            <div class="checkbox checkbox-primary checkbox-circle">
                                <input id="checkbox-9" type="checkbox">
                                <label for="checkbox-9">
                                    Primary
                                </label>
                            </div>
                            <div class="checkbox checkbox-success checkbox-circle">
                                <input id="checkbox-10" type="checkbox" checked>
                                <label for="checkbox-10">
                                    Success
                                </label>
                            </div>
                            <div class="checkbox checkbox-warning checkbox-circle">
                                <input id="checkbox-11" type="checkbox">
                                <label for="checkbox-11">
                                    Warning
                                </label>
                            </div>
                            <div class="checkbox checkbox-danger checkbox-circle">
                                <input id="checkbox-12" type="checkbox" checked>
                                <label for="checkbox-12">
                                    Danger
                                </label>
                            </div>

                            <div class="checkbox checkbox-dark checkbox-circle">
                                <input id="checkbox-15" type="checkbox" checked>
                                <label for="checkbox-15">
                                    Dark
                                </label>
                            </div>


                            <p class="text-muted font-13 m-b-15 mt-4">Checkboxes without label text <code>.checkbox-single</code></p>
                            <div class="checkbox checkbox-single checkbox-circle">
                                <input type="checkbox" id="singleCheckbox11" value="option1" aria-label="Single checkbox One">
                                <label></label>
                            </div>
                            <div class="checkbox checkbox-primary checkbox-single checkbox-circle">
                                <input type="checkbox" id="singleCheckbox21" value="option2" checked aria-label="Single checkbox Two">
                                <label></label>
                            </div>


                            <p class="text-muted font-13 m-b-15 mt-4">Inline checkboxes</p>
                            <div class="checkbox form-check-inline checkbox-circle">
                                <input type="checkbox" id="inlineCheckbox11" value="option1">
                                <label for="inlineCheckbox11"> Inline One </label>
                            </div>
                            <div class="checkbox checkbox-success form-check-inline checkbox-circle">
                                <input type="checkbox" id="inlineCheckbox21" value="option1" checked>
                                <label for="inlineCheckbox21"> Inline Two </label>
                            </div>
                            <div class="checkbox checkbox-primary form-check-inline checkbox-circle">
                                <input type="checkbox" id="inlineCheckbox31" value="option1">
                                <label for="inlineCheckbox31"> Inline Three </label>
                            </div>

                        </div>
                    </div><!-- end col -->

                    <div class="col-md-4">
                        <div class="m-b-20">

                            <h5 class="font-14 mt-3">Disabled</h5>

                            <p class="text-muted font-13 m-b-15">
                                Disabled state also supported.
                            </p>

                            <div class="checkbox">
                                <input id="checkbox9" type="checkbox" disabled>
                                <label for="checkbox9">
                                    Can't check this
                                </label>
                            </div>
                            <div class="checkbox checkbox-custom">
                                <input id="checkbox10" type="checkbox" disabled checked>
                                <label for="checkbox10">
                                    This too
                                </label>
                            </div>
                            <div class="checkbox checkbox-warning checkbox-circle">
                                <input id="checkbox110" type="checkbox" disabled checked>
                                <label for="checkbox110">
                                    And this
                                </label>
                            </div>
                            <div class="checkbox checkbox-info">
                                <input id="checkbox12" type="checkbox" disabled checked>
                                <label for="checkbox12">
                                    Can't check this
                                </label>
                            </div>
                            <div class="checkbox checkbox-primary">
                                <input id="checkbox13" type="checkbox" disabled checked>
                                <label for="checkbox13">
                                    This too
                                </label>
                            </div>
                            <div class="checkbox checkbox-danger checkbox-circle">
                                <input id="checkbox14" type="checkbox" disabled checked>
                                <label for="checkbox14">
                                    And this
                                </label>
                            </div>

                        </div>
                    </div><!-- end col -->

                </div>
                <!-- end row -->

                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="m-b-20">

                            <h5 class="font-14 mt-3">Basic</h5>

                            <p class="text-muted font-13 m-b-15">
                                Supports bootstrap brand colors: <code>.radio-primary</code>, <code>.radio-danger</code> etc.
                            </p>

                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="radio">
                                        <input type="radio" name="radio" id="radio1" value="option1" checked>
                                        <label for="radio1">
                                            Default
                                        </label>
                                    </div>
                                    <div class="radio radio-custom">
                                        <input type="radio" name="radio" id="radio03" value="option3">
                                        <label for="radio03">
                                            Custom
                                        </label>
                                    </div>
                                    <div class="radio radio-primary">
                                        <input type="radio" name="radio" id="radio3" value="option3">
                                        <label for="radio3">
                                            Primary
                                        </label>
                                    </div>
                                    <div class="radio radio-success">
                                        <input type="radio" name="radio" id="radio4" value="option4">
                                        <label for="radio4">
                                            Success
                                        </label>
                                    </div>
                                    <div class="radio radio-info">
                                        <input type="radio" name="radio" id="radio5" value="option5">
                                        <label for="radio5">
                                            Info
                                        </label>
                                    </div>
                                    <div class="radio radio-danger">
                                        <input type="radio" name="radio" id="radio6" value="option6">
                                        <label for="radio6">
                                            Danger
                                        </label>
                                    </div>
                                    <div class="radio radio-warning">
                                        <input type="radio" name="radio" id="radio7" value="option7">
                                        <label for="radio7">
                                            Warning
                                        </label>
                                    </div>

                                </div><!-- end col -->

                                <div class="col-sm-6">
                                    <div class="radio">
                                        <input type="radio" name="radio1" id="radio11" value="option1" checked>
                                        <label for="radio11">
                                            Default
                                        </label>
                                    </div>
                                    <div class="radio radio-primary">
                                        <input type="radio" name="radio3" id="radio13" value="option3">
                                        <label for="radio13">
                                            Primary
                                        </label>
                                    </div>
                                    <div class="radio radio-success">
                                        <input type="radio" name="radio4" id="radio14" value="option4" checked>
                                        <label for="radio14">
                                            Success
                                        </label>
                                    </div>
                                    <div class="radio radio-info">
                                        <input type="radio" name="radio5" id="radio15" value="option5" checked>
                                        <label for="radio15">
                                            Info
                                        </label>
                                    </div>
                                    <div class="radio radio-danger">
                                        <input type="radio" name="radio6" id="radio16" value="option6">
                                        <label for="radio16">
                                            Danger
                                        </label>
                                    </div>
                                    <div class="radio radio-warning">
                                        <input type="radio" name="radio7" id="radio17" value="option7" checked>
                                        <label for="radio17">
                                            Warning
                                        </label>
                                    </div>

                                </div><!-- end col -->

                            </div>
                            <!-- end row -->


                            <p class="text-muted font-13 m-b-15 mt-4">Radios without label text <code>.radio-single</code></p>
                            <div class="radio radio-single">
                                <input type="radio" id="singleRadio1" value="option1.1" name="radioSingle1" aria-label="Single radio One">
                                <label></label>
                            </div>
                            <div class="radio radio-success radio-single">
                                <input type="radio" id="singleRadio2" value="option2.1" name="radioSingle1" checked aria-label="Single radio Two">
                                <label></label>
                            </div>


                            <p class="text-muted font-13 m-b-15 mt-4">Inline radios</p>
                            <div class="radio radio-info form-check-inline">
                                <input type="radio" id="inlineRadio1" value="option1" name="radioInline" checked>
                                <label for="inlineRadio1"> Inline One </label>
                            </div>
                            <div class="radio form-check-inline">
                                <input type="radio" id="inlineRadio2" value="option2" name="radioInline" checked>
                                <label for="inlineRadio2"> Inline Two </label>
                            </div>
                        </div>
                    </div><!-- end col -->

                    <div class="col-lg-4 col-md-6">
                        <div class="m-b-20">

                            <h5 class="font-14 mt-3">Disabled</h5>

                            <p class="text-muted font-13 m-b-15">
                                Disabled state also supported.
                            </p>

                            <div class="radio radio-danger">
                                <input type="radio" name="radio31" id="radio51" value="option1" checked disabled>
                                <label for="radio51">
                                    Next
                                </label>
                            </div>
                            <div class="radio">
                                <input type="radio" name="radio41" id="radio61" value="option2" checked disabled>
                                <label for="radio61">
                                    One
                                </label>
                            </div>
                            <div class="radio radio-info">
                                <input type="radio" name="radio61" id="radio81" value="option4" checked disabled>
                                <label for="radio81">
                                    One
                                </label>
                            </div>

                        </div>
                    </div><!-- end col -->
                </div>
                <!-- end row -->

            </div> <!-- end container -->

            <!-- Footer -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="pull-right hide-phone">
                                Project Completed <strong class="text-custom">57%</strong>.
                            </div>
                            <div>
                                <strong>Simple Admin</strong> - Copyright © 2017 - 2018
                            </div>

                        </div>
                    </div>
                </div>
            </footer>
            <!-- End Footer -->

        </div>
        <!-- end wrapper -->





        <!-- jQuery  -->
        <script src="assets/js/jquery.min.js"></script>
        <script src="assets/js/popper.min.js"></script>
        <script src="assets/js/bootstrap.min.js"></script>
        <script src="assets/js/jquery.slimscroll.js"></script>

        <!-- App js -->
        <script src="assets/js/jquery.core.js"></script>
        <script src="assets/js/jquery.app.js"></script>

    </body>
</html>