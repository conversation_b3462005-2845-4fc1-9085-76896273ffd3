package com.registrocontrato.job.task.notificacao;

import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.StatusProcessamento;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.job.entity.CurrentJob;
import com.registrocontrato.job.entity.SituacaoExecucaoJob;
import com.registrocontrato.job.service.JobService;
import com.registrocontrato.job.task.PlaceTask;
import com.registrocontrato.registro.entity.RemessaChassi;
import com.registrocontrato.registro.service.RemessaChassiService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.List;

@Component
public class NotificarRemessaChassiParalizadaTask extends PlaceTask implements Serializable {

    private static final long serialVersionUID = 1L;

    private final RemessaChassiService remessaChassiService;

    private final String tituloEmail = "[Atenção] Remessas de chassi atrasadas";

    private final String templateEmail = "/email/templateEmail.xhtml";

    public NotificarRemessaChassiParalizadaTask(EnviaEmail enviaEmail, JobService jobService, RemessaChassiService remessaChassiService) {
        super(enviaEmail, jobService);
        this.remessaChassiService = remessaChassiService;
    }

    @Scheduled(cron = "0 15 8-20 * * *", zone = TIME_ZONE)
    @Async("jobExecutor")
    public void run() {
        start();
    }

    @Override
    public void execute() {
        LocalDateTime l1 = LocalDateTime.now();
        List<RemessaChassi> remessa = remessaChassiService.getArquivosInativos1Hora();
        if (!PlaceconUtil.isListaVaziaOuNula(remessa)) {
            StringBuilder builder = new StringBuilder();
            montarDados(builder, remessa);
            log.info(String.valueOf(builder));
            enviarNotificacaoJob(tituloEmail, builder.toString(), templateEmail, null);
            LocalDateTime l2 = LocalDateTime.now();
            getJobService().registrarLogJob(CurrentJob.REMESSA_CHASSI_INATIVO, l1, l2, SituacaoExecucaoJob.SUCESSO, getEmailContato());
        }
    }

    private void montarDados(StringBuilder builder, List<RemessaChassi> remessaChasis) {

        if (remessaChasis.size() > 1) {
            builder.append("Foram encontradas <b>" + remessaChasis.size() + " remessas</b> atrasadas há mais de 20 minutos:  <br/>");
        } else {
            builder.append("Foi encontrada <b>1 remessa</b> atrasada há mais de 20 minutos: </br>");
        }

        String conteudo;
        StringBuilder builderAndamento = new StringBuilder();
        StringBuilder builderAguardando = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy - HH:mm:ss");
        for (RemessaChassi r : remessaChasis) {
            String date = dateFormat.format(r.getDataTransacao());
            conteudo = String.format("<ul>" +
                            "<li>Arquivo <b>%s</b> : </li>" +
                            "<ul>" +
                            "<li> Código: <b>%d</b> </li>" +
                            "<li>Data de transação: <b>%s</b> </li>" +
                            "</ul>" +
                            "</ul>",
                    r.getNome(), r.getId(), date, r.getStatus());
            if (StatusProcessamento.AGUARDANDO.equals(r.getStatus())) {
                builderAguardando.append(conteudo);
            }
            if (StatusProcessamento.EM_PROCESSAMENTO.equals(r.getStatus())) {
                builderAndamento.append(conteudo);
            }
        }

        if (builderAguardando.length() > 0) {
            builder.append("<h4>Arquivos aguardando processamento:</h4>");
            builder.append(builderAguardando);
        }
        if (builderAndamento.length() > 0) {
            builder.append("<h4>Arquivos em processamento:</h4>");
            builder.append(builderAndamento);
        }
    }

    @Override
    public CurrentJob getJob() {
        return CurrentJob.REMESSA_CHASSI_INATIVO;
    }

}

