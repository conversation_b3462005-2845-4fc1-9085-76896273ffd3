package com.registrocontrato.job.ws.fipe.rest;

import com.fasterxml.jackson.annotation.JsonProperty;

public class VeiculoFipe {
	@JsonProperty("fipe_marca")
	private String fipeMarca;
	private String name;
	private String marca;
	private String key;
	private Integer id;
	@JsonProperty("fipe_name")
	private String fipeName;
	public String getFipeMarca() {
		return fipeMarca;
	}
	public void setFipeMarca(String fipeMarca) {
		this.fipeMarca = fipeMarca;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMarca() {
		return marca;
	}
	public void setMarca(String marca) {
		this.marca = marca;
	}
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getFipeName() {
		return fipeName;
	}
	public void setFipeName(String fipeName) {
		this.fipeName = fipeName;
	}
	
	
	
	
	
	
}
