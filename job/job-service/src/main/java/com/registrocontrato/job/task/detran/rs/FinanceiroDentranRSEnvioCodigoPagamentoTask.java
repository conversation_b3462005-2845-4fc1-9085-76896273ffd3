package com.registrocontrato.job.task.detran.rs;

import com.registrocontrato.infra.email.Email;
import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.job.entity.CurrentJob;
import com.registrocontrato.job.entity.SituacaoExecucaoJob;
import com.registrocontrato.job.service.JobService;
import com.registrocontrato.job.task.PlaceTask;
import com.registrocontrato.registro.entity.DocumentoArrecadacao;
import com.registrocontrato.registro.repository.DocumentoArrecadacaoRepository;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.mail.internet.InternetAddress;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

import static com.registrocontrato.infra.util.PlaceconUtil.getDataFormatada;

@Component
public class FinanceiroDentranRSEnvioCodigoPagamentoTask extends PlaceTask implements Serializable {

    private static final long serialVersionUID = 345390064696776798L;

    private final DocumentoArrecadacaoRepository documentoArrecadacaoRepository;

    public FinanceiroDentranRSEnvioCodigoPagamentoTask(EnviaEmail enviaEmail,
                                                       JobService jobService,
                                                       DocumentoArrecadacaoRepository documentoArrecadacaoRepository) {
        super(enviaEmail, jobService);
        this.documentoArrecadacaoRepository = documentoArrecadacaoRepository;
    }

    @Scheduled(cron = "0 0/30 7-19 * * *", zone = TIME_ZONE)
    @Async("jobExecutor")
    public void run() {
        start();
    }

    @Override
    public void execute() {
        LocalDateTime l1 = LocalDateTime.now();
        log.info("ROTINA DETRAN/RS FINANCEIRO - ENVIO CODIGO DE PAGAMENTO");
        List<DocumentoArrecadacao> docs = documentoArrecadacaoRepository
                .recuperaDocumentosNaoPagoAndNaoNotificado(Uf.RS);
        if (!PlaceconUtil.isListaVaziaOuNula(docs)) {
            StringBuilder builder = new StringBuilder();
            montarDados(builder, docs);
            sendMail(builder.toString());
            marcarNotificados(docs);
            LocalDateTime l2 = LocalDateTime.now();
            getJobService().registrarLogJob(getJob(), l1, l2, SituacaoExecucaoJob.SUCESSO, builder.toString());
        }
    }

    private void marcarNotificados(List<DocumentoArrecadacao> docs) {
        docs.forEach(doc -> {
            log.info("NOTIFICAÇÃO DE EMAIL: " + doc.getVeiculo().getNumeroChassi());
            doc.setNotificado(SimNao.S);
            documentoArrecadacaoRepository.save(doc);
            log.info("Documento Arrecadação " + doc.getId() + " notificado.");
        });
    }

    private void montarDados(StringBuilder builder, List<DocumentoArrecadacao> docArrecadacao) {
        builder.append("As seguintes cobranças do Detran/RS estão aguardando o pagamento:<br/>");
        builder.append("Quantidade: " + docArrecadacao.size() + "<br/>");
        for (DocumentoArrecadacao c : docArrecadacao) {
            String conteudo = String.format(
                    "&ensp;Documento : <br/>&ensp;  Contrato : %s <br/>&ensp;  Chassi : %s <br/>&ensp;  Código de barras : %s <br/>",
                    c.getVeiculo().getContrato().getNumeroContrato(),
                    c.getVeiculo().getNumeroChassi(),
                    c.getLinhaDigitavel());
            builder.append(conteudo);
        }
    }

    private String sendMail(String mensagem) {
        try {
            String titulo = "Notificação de Pendências de Pagamento - DETRAN RS " + getDataFormatada(new Date(), "hh:mm");
            HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
            List<InternetAddress> emails = new ArrayList<>();
            emails.add(new InternetAddress(getEmailFinanceiro()));
            hash.put(Email.TIPO_PARA, emails);
            Map<String, String> params = new HashMap<String, String>();
            params.put("CONTEUDO", mensagem);
            Email email = new Email(getEnviaEmail());
            email.setHtml(true);
            email.enviarEmail(titulo, params, hash, "/email/detran-financeiro.xhtml");
            return email.getTexto();
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }

    @Override
    public CurrentJob getJob() {
        return CurrentJob.ROTINA_FINANCEIRA_RS_ENVIO_CODIGO_PAGAMENTO;

    }
}
