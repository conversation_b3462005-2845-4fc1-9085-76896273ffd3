package com.registrocontrato.job.service;

import java.math.BigDecimal;
import java.time.LocalDate;

public class ContadorRelatorioProjecao {

	private Integer totalRegistrosAcumulado = 0;
	private Integer qtdContabilizado = 0;
	private BigDecimal valorContabilizado = BigDecimal.ZERO;
	private BigDecimal totalJaArrecadado = BigDecimal.ZERO;
	private BigDecimal totalEsperado = BigDecimal.ZERO;
	private BigDecimal ultimoValor = BigDecimal.ZERO;
	LocalDate lHoje = LocalDate.now();
	
	
	
	public Integer getTotalRegistrosAcumulado() {
		return totalRegistrosAcumulado;
	}
	public void setTotalRegistrosAcumulado(Integer totalRegistrosAcumulado) {
		this.totalRegistrosAcumulado = totalRegistrosAcumulado;
	}
	public Integer getQtdContabilizado() {
		return qtdContabilizado;
	}
	public void setQtdContabilizado(Integer qtdContabilizado) {
		this.qtdContabilizado = qtdContabilizado;
	}
	public BigDecimal getValorContabilizado() {
		return valorContabilizado;
	}
	public void setValorContabilizado(BigDecimal valorContabilizado) {
		this.valorContabilizado = valorContabilizado;
	}
	public BigDecimal getTotalJaArrecadado() {
		return totalJaArrecadado;
	}
	public void setTotalJaArrecadado(BigDecimal totalJaArrecadado) {
		this.totalJaArrecadado = totalJaArrecadado;
	}
	public BigDecimal getTotalEsperado() {
		return totalEsperado;
	}
	public void setTotalEsperado(BigDecimal totalEsperado) {
		this.totalEsperado = totalEsperado;
	}
	public BigDecimal getUltimoValor() {
		return ultimoValor;
	}
	public void setUltimoValor(BigDecimal ultimoValor) {
		this.ultimoValor = ultimoValor;
	}
	
	public LocalDate getlHoje() {
		return lHoje;
	}
	
	public void setlHoje(LocalDate lHoje) {
		this.lHoje = lHoje;
	}
	
}
