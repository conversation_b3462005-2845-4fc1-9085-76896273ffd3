package com.registrocontrato.job.service;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.job.repository.RelatorioProjecaoRepository;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.CupomDesconto;
import com.registrocontrato.registro.entity.FaixaDesconto;
import com.registrocontrato.registro.entity.RelatorioProjecao;
import com.registrocontrato.registro.enums.TipoCobranca;

@Service
@Transactional
public class RelatorioProjecaoService {
	
	@PersistenceContext
	private EntityManager entityManager;
	
	@Autowired
	private RelatorioProjecaoRepository repository;

	public void projetarMes(LocalDate dataProjecao) throws ServiceException{
		//se for dia primeiro recalcula todo o mes anterior
		if (dataProjecao.getDayOfMonth() == 1) {
			projetar(dataProjecao.minusDays(1));
		}
		
		//exclusao dos dados do mes em questão
		projetar(dataProjecao);
	}

	private void projetar(LocalDate dataProcecao) throws ServiceException {
		int dia = 1;
		
		LocalDate lAux;
		LocalDate lFiltro = LocalDate.of(dataProcecao.getYear(), dataProcecao.getMonthValue(), 1);
		LocalDate lUltimo = LocalDate.of(lFiltro.getYear(), lFiltro.getMonthValue(), 1).plusMonths(1).minusDays(1);
		LocalDate lHoje = LocalDate.now();
		
		if (lUltimo.isAfter(LocalDate.of(lHoje.getYear(), lHoje.getMonth(), 1).plusMonths(1).minusDays(1))) {
			return;
		}
		
		while (dia <= lUltimo.getDayOfMonth()) {
			lAux = LocalDate.of(lFiltro.getYear(), lFiltro.getMonthValue(), dia);
			calculateDia(lAux, lUltimo, lHoje);
			dia++;
		}
	}
	
	
	private RelatorioProjecao findProjecao(Date data, Cobranca cob) {
		RelatorioProjecao r = repository.findByDataAndIdFinanceiraAndUf(data, cob.getFinanceira().getId(), cob.getEstado());
		return r == null ? new RelatorioProjecao() : r;
	}
	
	
	private void calculateDia(LocalDate lDia, LocalDate lUltimoDiaFiltro, LocalDate lHoje) throws ServiceException{
		
		Date data = java.sql.Date.valueOf(lDia);
		RelatorioProjecao projecao = null;
		if (lDia.getDayOfMonth() <= lUltimoDiaFiltro.getDayOfMonth() && !lDia.isAfter(lHoje)) {
			List<Cobranca>cobrancas = new ArrayList<Cobranca>();
			for (Uf uf : Uf.listar()) {
				Credenciamento credenciamento = getCredenciamentoPeriodo(uf, data);
				if (credenciamento == null) {
					continue;
				}

				List<Financeira> financeiras = findFinanceirasContratosNoPeriodo(uf, data, data);
				
				for(Financeira f : financeiras) {
					cobrancas.add(calcularCobranca(uf, data, data, credenciamento, f));
				}
			}
			
			int totalRegistros = 0;
			for (Cobranca cob : cobrancas) {
				totalRegistros = cob.getQuantidadeRegistros().intValue();
				projecao = findProjecao(data, cob);
				projecao.setValorCredenciada(cob.getValorCredenciada());
				projecao.setValorDetran(cob.getValorDetran());
				projecao.setTotalContratos(totalRegistros);
				projecao.setTotalRegistros(totalRegistros);
				projecao.setTotalRegistrosAditivo(cob.getQuantidadeAditivo());
				projecao.setData(data);
				projecao.setUf(cob.getEstado());
				projecao.setIdFinanceira(cob.getFinanceira().getId());
				if ((lDia.getDayOfWeek() != DayOfWeek.SATURDAY && lDia.getDayOfWeek() != DayOfWeek.SUNDAY) || totalRegistros > 0) {
					projecao.setFinalSemana(lDia.getDayOfWeek() != DayOfWeek.SATURDAY && lDia.getDayOfWeek() != DayOfWeek.SUNDAY ? false : true);
					repository.save(projecao);
				}
				else if (projecao.getId() != null) {
					repository.delete(projecao);
				}
			}
		}
	}
	
	private List<Financeira> findFinanceirasContratosNoPeriodo(Uf uf, Date dataInicio, Date dataFim) {
		dataInicio = PlaceconUtil.minDateTime(dataInicio);
		dataFim = PlaceconUtil.maxDateTime(dataFim);
		return repository.findFinanceirasContratosNoPeriodo(uf, dataInicio, dataFim);
	}
	
	private Credenciamento getCredenciamentoPeriodo(Uf uf, Date data) {
		AuditReader reader = AuditReaderFactory.get(entityManager);
		return (Credenciamento) reader.createQuery()
				.forRevisionsOfEntity(Credenciamento.class, false, true)
				.add(AuditEntity.property("dataInicio").le(data))
				.add(AuditEntity.property("dataFim").ge(data))
				.add(AuditEntity.revisionProperty("timestamp").le(data.getTime()))
				.add(AuditEntity.property("uf").eq(uf))
				.addOrder(AuditEntity.revisionNumber().desc())
				.getResultList().get(0);
	}
	
	public Cobranca calcularCobranca(Uf uf, Date dataInicio, Date dataFim, Credenciamento credenciamento, Financeira financeira) throws ServiceException {
		dataInicio = PlaceconUtil.minDateTime(dataInicio);
		dataFim = PlaceconUtil.maxDateTime(dataFim);
		
		Cobranca c = new Cobranca(uf, financeira, dataInicio, dataFim, credenciamento);
		calcular(c, dataInicio, dataFim);
		
		if (credenciamento.isPossivelDesconto()) {
			//aplicacao de desconto
			applyCupomDesconto(uf, dataInicio, dataFim, financeira, c);
		}
	
		return c;
	}
	
	private Cobranca calcular(Cobranca cobranca, Date dataInicio, Date dataFim) throws ServiceException {
		
		Long quantidadeRegistros = 0l;
		Long quantidadeRegistrosAditivo = 0l;
		
		if (cobranca.getCredenciamento().getTipoCobranca() == TipoCobranca.CONTRATO) {
			quantidadeRegistros = repository.countContratosByFinanceiraAndUfRegistro(cobranca.getFinanceira(), cobranca.getEstado(), dataInicio, dataFim);
			quantidadeRegistrosAditivo = repository.countContratosByFinanceiraAndUfRegistroAditivo(cobranca.getFinanceira(), cobranca.getEstado(), dataInicio, dataFim);
		} else {
			quantidadeRegistros = repository.countVeiculosByFinanceiraAndUfRegistro(cobranca.getFinanceira(), cobranca.getEstado(), dataInicio, dataFim);
			quantidadeRegistrosAditivo = repository.countVeiculosByFinanceiraAndUfRegistroAditivo(cobranca.getFinanceira(), cobranca.getEstado(), dataInicio, dataFim);
		}
		
		BigDecimal valorCredenciada = cobranca.getCredenciamento().getValorCredenciada();
		BigDecimal valorDETRAN = cobranca.getCredenciamento().getValorDETRAN();
		BigDecimal valorAditivoDETRAN = cobranca.getCredenciamento().getValorAditivo();
		
		if (valorAditivoDETRAN == null) 
			valorAditivoDETRAN = BigDecimal.ZERO;
		
		if (valorCredenciada == null) {
			valorCredenciada = BigDecimal.ZERO;
		}
		if (valorDETRAN == null) {
			valorDETRAN = BigDecimal.ZERO;
		}
		
		if (cobranca.getQuantidadeRegistros() == null) {
			cobranca.setQuantidadeRegistros(0l);
		}
		
		cobranca.setValorCredenciada(valorCredenciada.multiply(new BigDecimal(cobranca.getQuantidadeRegistros())));
		cobranca.setValorCobranca(cobranca.getValorDetran().add(cobranca.getValorCredenciada()));
		
		cobranca.setQuantidadeRegistros(quantidadeRegistros);
		cobranca.setQuantidadePrincipal(quantidadeRegistros - quantidadeRegistrosAditivo);
		cobranca.setQuantidadeAditivo(quantidadeRegistrosAditivo);
		
		cobranca.setValorDetranPrincipal(valorDETRAN.multiply(new BigDecimal(cobranca.getQuantidadePrincipal())));
		cobranca.setValorDetranAditivo(valorAditivoDETRAN.multiply(new BigDecimal(cobranca.getQuantidadeAditivo())));
		cobranca.setValorDetran(cobranca.getValorDetranPrincipal().add(cobranca.getValorDetranAditivo()));

		cobranca.setValorCredenciada(valorCredenciada.multiply(new BigDecimal(cobranca.getQuantidadeRegistros())));
		//para fins de relatorio o valor da fatura que interessa é apenas o valor da PLACE
		cobranca.setValorCobranca(valorCredenciada.multiply(new BigDecimal(cobranca.getQuantidadeRegistros())));
		
		return cobranca;
	}

	private void applyCupomDesconto(Uf uf, Date dataInicio, Date dataFim, Financeira financeira, Cobranca c) {
		List<CupomDesconto>cupons = findCuponsDisponiveis(financeira,uf, dataInicio, dataFim);
		if (!PlaceconUtil.isListaVaziaOuNula(cupons)) {
			CupomDesconto cupom = cupons.get(0);
			c.setCupomDesconto(cupom);
			int indice = 0;
			BigDecimal valorDesconto = new BigDecimal(Long.valueOf(0l));
			for (FaixaDesconto faixa : cupom.getFaixasDesconto()) {
				//verifica se eh a ultima faixa de desconto
				if (faixa.getQuantidadeFinal() == null) {
					valorDesconto = faixa.getPercentual();
				} else {
					long qtdInicial = cupom.getQuantidadeInicial(indice);
					long qtdFinal = qtdInicial + faixa.getQuantidadeFinal();
					if (qtdInicial <= c.getQuantidadeRegistros() && c.getQuantidadeRegistros() <= qtdFinal) {
						valorDesconto = faixa.getPercentual();
						break;
					}
				}
				indice++;
			}
			if (valorDesconto.compareTo(BigDecimal.ZERO) > 0) {
				BigDecimal valor = valorDesconto.multiply(new BigDecimal(c.getQuantidadeRegistros()));
				c.setValorDesconto(valor);
				c.setValorCobranca(c.getValorCobranca().subtract(c.getValorDesconto()));
			}
		}
	}
	
	private List<CupomDesconto>findCuponsDisponiveis(Financeira f, Uf uf, Date dataInicio, Date dataFim){
		List<CupomDesconto> lista = repository.findCuponsDisponiveis(f, uf, dataInicio, dataFim);
		if (!PlaceconUtil.isListaVaziaOuNula(lista)) {
			for (CupomDesconto c : lista) {
				Collections.sort(c.getFaixasDesconto(), (a,b)->{
					return a.getIndice() < b.getIndice() ? 1 : 0;
				});
			}
		}
		return lista;
	}
}
