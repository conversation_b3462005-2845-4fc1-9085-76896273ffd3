<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cobranca-pi" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="14ff3603-4c3d-4ad5-9219-f0ccbe49198c">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="logo" class="java.io.InputStream"/>
	<parameter name="lote" class="java.lang.String"/>
	<parameter name="valor" class="java.lang.String"/>
	<parameter name="mesReferencia" class="java.lang.String"/>
	<parameter name="dataVencimento" class="java.lang.String"/>
	<parameter name="dataCriaca" class="java.lang.String"/>
	<parameter name="linha" class="java.lang.String"/>
	<parameter name="nossoNumero" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="73" splitType="Stretch">
			<image>
				<reportElement x="0" y="3" width="293" height="68" uuid="3d8b8979-e384-4684-9999-41d97b30d041"/>
				<imageExpression><![CDATA[$P{logo}]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<detail>
		<band height="125" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="555" height="20" uuid="de4215b6-fb4f-413a-a9a3-abb55c513507"/>
				<textFieldExpression><![CDATA["Período de Referência: " + $P{mesReferencia}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="20" width="555" height="20" uuid="dd9e7a0c-41b3-49b6-8ada-1d6c84565001"/>
				<textFieldExpression><![CDATA["Lote: " + $P{lote}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="40" width="555" height="20" uuid="4f7b9830-7253-4a03-8b47-38f8cac41f7e"/>
				<textFieldExpression><![CDATA["Nosso Número: " + $P{nossoNumero}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="80" width="555" height="20" uuid="dcdf9b65-893c-481c-b4b1-3073ebec0461"/>
				<textFieldExpression><![CDATA["Data de Vencimento: " + $P{dataVencimento}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="60" width="555" height="20" uuid="171aae36-2f4e-493a-baa0-f767c3aabfcf"/>
				<textFieldExpression><![CDATA["Valor: " + $P{valor}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="100" width="555" height="20" uuid="3a49dda3-49e7-45a5-97ad-2a697496d559"/>
				<textFieldExpression><![CDATA["Linha Digitável: " + $P{linha}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
