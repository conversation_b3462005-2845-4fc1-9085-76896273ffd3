package com.registrocontrato.commons.ws.dto.b3.send;

import com.registrocontrato.commons.ws.validator.MunicipioDenatran;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Schema(name = "municipio")
public class MunicipioDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @MunicipioDenatran
    @NotNull(message = "Código do Município da Liberação do Crédito: campo obrigatório")
    @Schema(description = "Código do Município no DENATRAN", required = true)
    private Integer numeroIdentificador;

    @Schema(description = "Nome do Município")
    private String nome;

    public Integer getNumeroIdentificador() {
        return numeroIdentificador;
    }

    public void setNumeroIdentificador(Integer numeroIdentificador) {
        this.numeroIdentificador = numeroIdentificador;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

}
