package com.registrocontrato.commons.ws.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class TipoRestricaoValidator implements ConstraintValidator<TipoRestricao, String> {

	@Override
	public void initialize(TipoRestricao constraintAnnotation) {

	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		try {
			if(value != null) {
				com.registrocontrato.registro.enums.TipoRestricao.valueOf(value.toUpperCase());
			}
		} catch (IllegalArgumentException e) {
			return false;
		}
		return true;
	}

}
