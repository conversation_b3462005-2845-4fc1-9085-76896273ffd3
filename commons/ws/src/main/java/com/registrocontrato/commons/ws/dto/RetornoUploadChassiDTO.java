package com.registrocontrato.commons.ws.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(name = "retornoUploadChassi")
public class RetornoUploadChassiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "${api.retorno.chassi}")
    private String chassi;

    @Schema(description = "${api.retorno.operacao}")
    private String operacao;

    @Schema(description = "${api.retorno.codResposta}")
    private String codResposta;

    @Schema(description = "${api.retorno.msgResposta}")
    private String msgResposta;

    @Schema(description = "${api.retorno.cnpjAgente}")
    private String cnpjAgente;

    @Schema(description = "${api.retorno.hashOperacao}")
    private String hashOperacao;

    public String getChassi() {
        return chassi;
    }

    public void setChassi(String chassi) {
        this.chassi = chassi;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getCodResposta() {
        return codResposta;
    }

    public void setCodResposta(String codResposta) {
        this.codResposta = codResposta;
    }

    public String getMsgResposta() {
        return msgResposta;
    }

    public void setMsgResposta(String msgResposta) {
        this.msgResposta = msgResposta;
    }

    public String getCnpjAgente() {
        return cnpjAgente;
    }

    public void setCnpjAgente(String cnpjAgente) {
        this.cnpjAgente = cnpjAgente;
    }

    public String getHashOperacao() {
        return hashOperacao;
    }

    public void setHashOperacao(String hashOperacao) {
        this.hashOperacao = hashOperacao;
    }

}
