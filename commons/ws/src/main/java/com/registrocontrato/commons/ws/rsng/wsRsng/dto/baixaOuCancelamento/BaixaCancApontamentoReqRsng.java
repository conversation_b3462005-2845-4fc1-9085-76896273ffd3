package com.registrocontrato.commons.ws.rsng.wsRsng.dto.baixaOuCancelamento;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BaixaCancApontamentoReqRsng {

    @JsonProperty("data")
    private DadosDaBaixa dadosDaBaixa;

    public BaixaCancApontamentoReqRsng(DadosDaBaixa dadosDaBaixa) {
        this.dadosDaBaixa = dadosDaBaixa;
    }

    public static class DadosDaBaixa {

        @JsonProperty("dadosValidacao")
        private DadosValidacao dadosValidacao;

        public DadosDaBaixa(DadosValidacao dadosValidacao) {
            this.dadosValidacao = dadosValidacao;
        }

        public static class DadosValidacao {

            @JsonProperty("numChassiVeiculo")
            private String chassi;

            @JsonProperty("numDocumentoFinanciado")
            private String cpfCnpjDevedor;

            @JsonProperty("numApontamento")
            private String gravame;

            public String getChassi() {
                return chassi;
            }

            public void setChassi(String chassi) {
                this.chassi = chassi;
            }

            public String getCpfCnpjDevedor() {
                return cpfCnpjDevedor;
            }

            public void setCpfCnpjDevedor(String cpfCnpjDevedor) {
                this.cpfCnpjDevedor = cpfCnpjDevedor;
            }

            public String getGravame() {
                return gravame;
            }

            public void setGravame(String gravame) {
                this.gravame = gravame;
            }
        }

        public DadosValidacao getDadosValidacao() {
            return dadosValidacao;
        }

        public void setDadosValidacao(DadosValidacao dadosValidacao) {
            this.dadosValidacao = dadosValidacao;
        }
    }

    public DadosDaBaixa getDadosDaBaixa() {
        return dadosDaBaixa;
    }

    public void setDadosDaBaixa(DadosDaBaixa dadosDaBaixa) {
        this.dadosDaBaixa = dadosDaBaixa;
    }
}
