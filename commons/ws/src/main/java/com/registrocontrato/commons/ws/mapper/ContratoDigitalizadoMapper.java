package com.registrocontrato.commons.ws.mapper;

import com.registrocontrato.commons.ws.dto.ContratoDigitalizadoDTO;
import com.registrocontrato.registro.entity.ContratoDigitalizado;
import org.springframework.stereotype.Component;

@Component
public class ContratoDigitalizadoMapper {

    public ContratoDigitalizadoDTO convertToDTO(ContratoDigitalizado contrato) {
        ContratoDigitalizadoDTO dto = new ContratoDigitalizadoDTO();
        dto.setChassi(contrato.getChassi());
        dto.setDataContrato(contrato.getDataContrato());
        dto.setDataCadastro(contrato.getDataCadastro());
        dto.setDataProcessamento(contrato.getDataProcessamento());
        dto.setId(contrato.getId());
        dto.setEnviado(contrato.getEnviado());
        dto.setNumeroContrato(contrato.getNumeroContrato());
        dto.setNumeroRegistroContrato(contrato.getNumeroRegistroContrato());
        dto.setReferenciaArquivo(contrato.getReferenciaArquivo());
        dto.setObservacao(contrato.getObservacao());
        dto.setDocumentoDevedor(contrato.getDocumentoDevedor());
        dto.setEstado(contrato.getEstado());
        dto.setGravame(contrato.getGravame());
        dto.setDataRegistroDETRAN(contrato.getDataRegistroDETRAN());
        return dto;
    }
}
