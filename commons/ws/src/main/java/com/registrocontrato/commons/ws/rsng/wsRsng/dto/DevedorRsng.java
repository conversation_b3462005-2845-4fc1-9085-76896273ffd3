package com.registrocontrato.commons.ws.rsng.wsRsng.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DevedorRsng {

    @JsonProperty("nome")
    private String nome;

    @JsonProperty("indTipoDocumento")
    private Integer indicadorTipoDocumento;

    @JsonProperty("numDocumento")
    private String cpfCnpjDevedor;

    @JsonProperty("nomeEndereco")
    private String endereco;

    @JsonProperty("numEndereco")
    private String numEndereco;

    @JsonProperty("descComplementoEndereco")
    private String complementoEndereco;

    @JsonProperty("nomeBairroEndereco")
    private String bairro;

    @JsonProperty("siglaUfEndereco")
    private String uf;

    @JsonProperty("codMunicipioEndereco")
    private Integer codMunicipio;

    @JsonProperty("numCepEndereco")
    private String cep;

    @JsonProperty("numDddTelefone")
    private String ddd;

    @JsonProperty("numTelefone")
    private String telefone;

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getIndicadorTipoDocumento() {
        return indicadorTipoDocumento;
    }

    public void setIndicadorTipoDocumento(Integer indicadorTipoDocumento) {
        this.indicadorTipoDocumento = indicadorTipoDocumento;
    }

    public String getCpfCnpjDevedor() {
        return cpfCnpjDevedor;
    }

    public void setCpfCnpjDevedor(String cpfCnpjDevedor) {
        this.cpfCnpjDevedor = cpfCnpjDevedor;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getNumEndereco() {
        return numEndereco;
    }

    public void setNumEndereco(String numEndereco) {
        this.numEndereco = numEndereco;
    }

    public String getComplementoEndereco() {
        return complementoEndereco;
    }

    public void setComplementoEndereco(String complementoEndereco) {
        this.complementoEndereco = complementoEndereco;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getUf() {
        return uf;
    }

    public void setUf(String uf) {
        this.uf = uf;
    }

    public Integer getCodMunicipio() {
        return codMunicipio;
    }

    public void setCodMunicipio(Integer codMunicipio) {
        this.codMunicipio = codMunicipio;
    }

    public String getCep() {
        return cep;
    }

    public void setCep(String cep) {
        this.cep = cep;
    }

    public String getDdd() {
        return ddd;
    }

    public void setDdd(String ddd) {
        this.ddd = ddd;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }
}
