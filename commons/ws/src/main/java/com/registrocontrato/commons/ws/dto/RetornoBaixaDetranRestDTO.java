package com.registrocontrato.commons.ws.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.RandomStringUtils;

import java.io.Serializable;

@Schema(name = "retornoBaixaDetran")
public class RetornoBaixaDetranRestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "${api.retorno.numeroRegistroEletronico}")
    private Long numeroRegistroEletronico;
    @Schema(description = "${api.retorno.codResposta}")
    private String codResposta;

    @Schema(description = "${api.retorno.msgResposta}")
    private String msgResposta;

    @Schema(description = "${api.retorno.operacao}")
    private String operacao;

    @Schema(description = "${api.retorno.hashOperacao}")
    private String hashOperacao;

    public RetornoBaixaDetranRestDTO(Long numeroRegistroEletronico, String operacao) {
        this.numeroRegistroEletronico = numeroRegistroEletronico;
        this.operacao = operacao;
        this.hashOperacao = RandomStringUtils.randomAlphabetic(20);
    }


    public Long getNumeroRegistroEletronico() {
        return numeroRegistroEletronico;
    }

    public void setNumeroRegistroEletronico(Long numeroRegistroEletronico) {
        this.numeroRegistroEletronico = numeroRegistroEletronico;
    }

    public String getCodResposta() {
        return codResposta;
    }

    public void setCodResposta(String codResposta) {
        this.codResposta = codResposta;
    }

    public String getMsgResposta() {
        return msgResposta;
    }

    public void setMsgResposta(String msgResposta) {
        this.msgResposta = msgResposta;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    public String getOperacao() {
        return operacao;
    }

    public void setOperacao(String operacao) {
        this.operacao = operacao;
    }

    public String getHashOperacao() {
        return hashOperacao;
    }

    public void setHashOperacao(String hashOperacao) {
        this.hashOperacao = hashOperacao;
    }


}
