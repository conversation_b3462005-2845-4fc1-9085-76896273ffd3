package com.registrocontrato.commons.ws.validator;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IndiceFinanceiroValidator.class)
public @interface IndiceFinanceiro {

	String message() default "valor inválido. Valores válidos: PREFIXADO, POSFIXADO, VALORBEM, IGP, IGPM, IPC, OUTRO";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}