package com.registrocontrato.commons.ws.rsng.wsRsng.dto.inclusao;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.ContratoRsngDTO;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.DevedorRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.FinanceiraRsng;
import com.registrocontrato.commons.ws.rsng.wsRsng.dto.VeiculoRsngDTO;

public class InclusaoApontamentoReqRsng {

    @JsonProperty("data")
    DadosDaInclusao dadosDaInclusao;

    public InclusaoApontamentoReqRsng(DadosDaInclusao dadosDaInclusao) {
        this.dadosDaInclusao = dadosDaInclusao;
    }

    public static class DadosDaInclusao {

        @JsonProperty("veiculo")
        private VeiculoRsngDTO veiculoRsng;

        @JsonProperty("credor")
        private FinanceiraRsng financeiraRsng;

        @JsonProperty("financiado")
        private DevedorRsng devedorRsng;

        @JsonProperty("contrato")
        private ContratoRsngDTO.ContratoPrincipal contratoPrincipalRsng;

        public VeiculoRsngDTO getVeiculoRsng() {
            return veiculoRsng;
        }

        public void setVeiculoRsng(VeiculoRsngDTO veiculoRsngDTO) {
            this.veiculoRsng = veiculoRsngDTO;
        }

        public FinanceiraRsng getFinanceiraRsng() {
            return financeiraRsng;
        }

        public void setFinanceiraRsng(FinanceiraRsng financeiraRsng) {
            this.financeiraRsng = financeiraRsng;
        }

        public DevedorRsng getDevedorRsng() {
            return devedorRsng;
        }

        public void setDevedorRsng(DevedorRsng devedorRsng) {
            this.devedorRsng = devedorRsng;
        }

        public ContratoRsngDTO.ContratoPrincipal getContratoPrincipalRsng() {
            return contratoPrincipalRsng;
        }

        public void setContratoPrincipalRsng(ContratoRsngDTO.ContratoPrincipal contratoPrincipalRsng) {
            this.contratoPrincipalRsng = contratoPrincipalRsng;
        }
    }

    public DadosDaInclusao getDadosDaInclusao() {
        return dadosDaInclusao;
    }

    public void setDadosDaInclusao(DadosDaInclusao dadosDaInclusao) {
        this.dadosDaInclusao = dadosDaInclusao;
    }
}
