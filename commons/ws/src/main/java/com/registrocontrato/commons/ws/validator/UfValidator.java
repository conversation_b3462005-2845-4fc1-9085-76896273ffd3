package com.registrocontrato.commons.ws.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class UfValidator implements ConstraintValidator<Uf, String> {

	@Override
	public void initialize(Uf constraintAnnotation) {

	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		try {
			if(value != null) {
				com.registrocontrato.infra.entity.Uf.valueOf(value.toUpperCase());
			}
		} catch (IllegalArgumentException e) {
			return false;
		}
		return true;
	}

}
