package com.registrocontrato.infra.bean;

import com.registrocontrato.infra.exception.ServiceException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.primefaces.model.file.UploadedFile;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIViewRoot;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.faces.context.Flash;
import java.io.IOException;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public abstract class BaseBean implements Serializable {

    private static final long serialVersionUID = 1L;

    protected final Log logger = LogFactory.getLog(getClass());

    public String montaURLComContextPath(String url) {
        return getExternalContext().getRequestContextPath() + url;
    }

    protected ExternalContext getExternalContext() {
        return getCurrentInstance().getExternalContext();
    }

    protected FacesContext getCurrentInstance() {
        return FacesContext.getCurrentInstance();
    }

    protected void validacaoArquivosDefault(UploadedFile file, List<String> tipos) throws ServiceException {
        String[] arquivo = file.getFileName().split("\\.");
        String extensao = arquivo[arquivo.length - 1].toLowerCase();
        boolean formatoRecusado = true;

        for (String tipo : tipos) {
            if (extensao.equals(tipo.toLowerCase())) {
                formatoRecusado = false;
                break;
            }
        }
        if (formatoRecusado)
            throw new ServiceException("Arquivo no formato inválido!");

        String conteudo = new String(file.getContent());
        if (conteudo.contains("<script"))
            throw new ServiceException("Arquivo suspeito!");
    }

    protected void addMessageError(String message) {
        getExternalContext().getFlash().setKeepMessages(true);
        getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, message, ""));
    }

    protected void addMessageInfo(String message) {
        getExternalContext().getFlash().setKeepMessages(true);
        getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
    }

    protected void addMessageInfo(String client, String message) {
        getExternalContext().getFlash().setKeepMessages(true);
        getCurrentInstance().addMessage(client, new FacesMessage(FacesMessage.SEVERITY_INFO, message, ""));
    }

    protected void addMessageWarn(String message) {
        getExternalContext().getFlash().setKeepMessages(true);
        getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_WARN, message, ""));
    }

    protected void addMessageSuccess() {
        getExternalContext().getFlash().setKeepMessages(true);
        getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, "Salvo com sucesso", ""));
    }

    protected void unauthorized() {
        try {
            getExternalContext().redirect(getExternalContext().getRequestContextPath() + "/error/403.xhtml");
            getCurrentInstance().responseComplete();
        } catch (IOException e) {
            logger.error(e);
        }
    }

    public String getUsername() {
        if (SecurityContextHolder.getContext().getAuthentication() == null) {
            return "";
        }

        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getUsername();
        }
        return principal.toString();
    }

    public String getIpClient() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest().getRemoteAddr();
    }

    public Collection<? extends GrantedAuthority> getRoles() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getAuthorities();
        }
        return null;
    }

    protected UIViewRoot getViewRoot() {
        return getCurrentInstance().getViewRoot();
    }

    protected Flash getFlash() {
        return getExternalContext().getFlash();
    }

    protected void putSession(String name, Object object) {
        getExternalContext().getSessionMap().put(name, object);
    }

    protected Object getSessionValue(String chave) {
        return getExternalContext().getSessionMap().get(chave);
    }

    protected void removeSession(String name) {
        getExternalContext().getSessionMap().remove(name);
    }

}
