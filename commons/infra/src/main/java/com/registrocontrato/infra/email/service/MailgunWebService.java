package com.registrocontrato.infra.email.service;

import com.registrocontrato.infra.email.base.MailgunBaseCommunication;
import com.registrocontrato.infra.email.dto.DetailsCompleteResponse;
import com.registrocontrato.infra.email.dto.SendMessageResponse;
import com.registrocontrato.infra.email.entity.RespostaMailgun;
import com.registrocontrato.infra.exception.MailgunException;
import com.registrocontrato.infra.util.PlaceconUtil;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.mail.Address;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MailgunWebService extends MailgunBaseCommunication {

    @Value("${spring.profiles.active:desenv}")
    private String profile;

    private List<String> addressList = new ArrayList<>();

    private final RespostaMailgunService respostaMailgunService;

    private final RespostaDetailsService respostaDetailsService;

    public MailgunWebService(RespostaMailgunService respostaMailgunService, RespostaDetailsService respostaDetailsService) {
        this.respostaMailgunService = respostaMailgunService;
        this.respostaDetailsService = respostaDetailsService;
    }

    public void enviarEmail(MimeMessage msg) throws MailgunException {
        RestTemplate restTemplate = buildRestTemplate();

        try {
            File tempFile = File.createTempFile(RandomStringUtils.randomAlphanumeric(6), ".mime");
            LinkedMultiValueMap<String, Object> map = criarBodySchema(msg, tempFile);
            HttpEntity<LinkedMultiValueMap<String, Object>> httpEntity = new HttpEntity<>(map, getHeaders());

            ResponseEntity<String> resposta = restTemplate.exchange(
                    "https://api.mailgun.net/v3/" + DOMAIN + "/messages.mime",
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            tempFile.delete();

            List<RespostaMailgun> respostaMailguns = PlaceconUtil.jsonParaObject(resposta.getBody(), SendMessageResponse.class)
                    .map(response -> respostaMailgunService.salvarRespostaMailgun(this.addressList, response))
                    .orElseThrow(() -> new MailgunException("Falha ao salvar resposta do email."));

            Thread.sleep(5000);

            respostaMailguns.forEach(respostaMailgun -> detalharEmail(restTemplate, respostaMailgun));

        } catch (HttpClientErrorException clientError) {
            logger.error("MAILGUN {}", clientError.getMessage());
            throw new MailgunException("MAILGUN " + clientError.getMessage());
        } catch (InterruptedException | IOException e) {
            logger.error("ERRO MAILGUN {}", e.getMessage());
            throw new MailgunException("MAILGUN " + e);
        }
    }


    public void detalharEmail(RestTemplate restTemplate, RespostaMailgun respostaMailgun) {
        String url = UriComponentsBuilder
                .fromUriString("https://api.mailgun.net/v3/" + DOMAIN + "/events")
                .queryParam("message-id", respostaMailgun.getIdEmail())
                .build().toUriString();

        try {
            ResponseEntity<String> resposta = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(getHeaders()),
                    String.class
            );

            PlaceconUtil.jsonParaObject(resposta.getBody(), DetailsCompleteResponse.class)
                    .map(detailsComplete ->
                            detailsComplete.getItens().stream()
                                    .map(respostaDetailsService::detalharRespostaFromConsultaApi)
                                    .collect(Collectors.toList())
                    );
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            logger.error("Erro no detalhamento do Mailgun {}", e.getMessage());
        }
    }

    private LinkedMultiValueMap<String, Object> criarBodySchema(MimeMessage msg, File tempFile) {
        LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();

        try {
            msg.writeTo(Files.newOutputStream(tempFile.toPath()));
            map.add("message", new FileSystemResource(tempFile));
            map.add("o:tag", this.profile);

            StringBuilder emails = new StringBuilder();
            Address[] dest = msg.getRecipients(MimeMessage.RecipientType.TO);
            Address[] destCopia = msg.getRecipients(MimeMessage.RecipientType.CC);
            Address[] destCopiaOculta = msg.getRecipients(MimeMessage.RecipientType.BCC);
            if (dest != null && dest.length > 0)
                ordenarEmails(emails, msg.getRecipients(Message.RecipientType.TO));

            if (destCopia != null && destCopia.length > 0)
                ordenarEmails(emails, msg.getRecipients(Message.RecipientType.CC));

            if (destCopiaOculta != null && destCopiaOculta.length > 0)
                ordenarEmails(emails, msg.getRecipients(Message.RecipientType.BCC));

            map.add("to", emails.toString());
        } catch (MessagingException | IOException e) {
            logger.error("Falha ao contruir o body schema!", e.getMessage());
            throw new MailgunException("Falha ao contruir o body schema!");
        }
        return map;
    }

    private void ordenarEmails(StringBuilder emails, Address[] addresses) {
        for (int i = 0; i < addresses.length; i++) {
            emails.append(addresses[i].toString());
            addressList.add(addresses[i].toString());
            emails.append("; ");
        }
    }
}
