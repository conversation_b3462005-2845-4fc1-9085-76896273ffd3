package com.registrocontrato.infra.email.dto.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MailgunWebhookDTO {

    @JsonProperty("signature")
    SignatureMailgunDTO signature;

    @JsonProperty("event-data")
    EventDataMailgunDTO eventData;

    public SignatureMailgunDTO getSignature() {
        return signature;
    }

    public void setSignature(SignatureMailgunDTO signature) {
        this.signature = signature;
    }

    public EventDataMailgunDTO getEventData() {
        return eventData;
    }

    public void setEventData(EventDataMailgunDTO eventData) {
        this.eventData = eventData;
    }
}
