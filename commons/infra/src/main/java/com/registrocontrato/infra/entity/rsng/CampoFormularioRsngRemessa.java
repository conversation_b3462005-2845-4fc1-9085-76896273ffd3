package com.registrocontrato.infra.entity.rsng;

import com.registrocontrato.infra.entity.TipoCampoFormularioRemessa;

import java.util.Arrays;
import java.util.List;

public enum CampoFormularioRsngRemessa {

    chassi("Chassi", Arrays.asList(TipoCampoFormularioRemessa.Texto), 21), //
    identificadorRemarcacao("Remarcado", Arrays.asList(TipoCampoFormularioRemessa.SimNao2, TipoCampoFormularioRemessa.SimNao1), 1), //
    ufRegistroGravame("UF do Registro / Gravame", Arrays.asList(TipoCampoFormularioRemessa.Texto), 2), //
    ufPlacaAtual("UF da Placa", Arrays.asList(TipoCampoFormularioRemessa.Texto), 2), //
    placa("Placa", Arrays.asList(TipoCampoFormularioRemessa.Texto), 7), //
    renavam("RENAVAM", Arrays.asList(TipoCampoFormularioRemessa.Numerico), 11), //
    anoFabricacao("Ano de Fabricação", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Numerico2), 4), //
    anoModelo("Ano do Modelo", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Numerico2), 4), //
    marca("Marca", Arrays.asList(TipoCampoFormularioRemessa.Texto), 25),
    modelo("Modelo", Arrays.asList(TipoCampoFormularioRemessa.Texto), 25),
    tipoVeiculo("Tipo Veículo", Arrays.asList(TipoCampoFormularioRemessa.TipoVeiculo), 10),
    cnpjAgenteFinanceiro("CNPJ do Agente Financeiro", Arrays.asList(TipoCampoFormularioRemessa.Texto), 14), //
    numeroContrato("Número do Contrato", Arrays.asList(TipoCampoFormularioRemessa.Texto), 20), //
    dataContrato("Data do Contrato", Arrays.asList(TipoCampoFormularioRemessa.Data1, TipoCampoFormularioRemessa.Data2, TipoCampoFormularioRemessa.Data3, TipoCampoFormularioRemessa.Data4, TipoCampoFormularioRemessa.Data5, TipoCampoFormularioRemessa.Data6)), //
    qtdMeses("Quantidade de Meses", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Numerico2), 3), //
    gravame("GRAVAME", Arrays.asList(TipoCampoFormularioRemessa.Numerico), 8), //
    tipoRestricao("Tipo de Restrição", Arrays.asList(TipoCampoFormularioRemessa.TipoRestricao, TipoCampoFormularioRemessa.TipoRestricao2), 2), //
    valorTaxaoJurosMes("Taxa de Juros ao Mês", Arrays.asList(TipoCampoFormularioRemessa.Taxa1, TipoCampoFormularioRemessa.Taxa2, TipoCampoFormularioRemessa.Taxa3, TipoCampoFormularioRemessa.Taxa4, TipoCampoFormularioRemessa.Taxa5, TipoCampoFormularioRemessa.Taxa6, TipoCampoFormularioRemessa.Taxa7)), //
    valorTaxaJurosAno("Valor da Taxa de Juros ao Ano", Arrays.asList(TipoCampoFormularioRemessa.Taxa1, TipoCampoFormularioRemessa.Taxa2, TipoCampoFormularioRemessa.Taxa3, TipoCampoFormularioRemessa.Taxa4, TipoCampoFormularioRemessa.Taxa5, TipoCampoFormularioRemessa.Taxa6, TipoCampoFormularioRemessa.Taxa7)), //
    indicadorTaxaMulta("Indicador de Multa", Arrays.asList(TipoCampoFormularioRemessa.SimNao1), 3), //
    valorTaxaMulta("Valor da Taxa de Multa", Arrays.asList(TipoCampoFormularioRemessa.Taxa1, TipoCampoFormularioRemessa.Taxa2, TipoCampoFormularioRemessa.Taxa3, TipoCampoFormularioRemessa.Taxa4, TipoCampoFormularioRemessa.Taxa5, TipoCampoFormularioRemessa.Taxa6, TipoCampoFormularioRemessa.Taxa7)), //
    indicadorTaxaMora("Indicador da Taxa de Mora", Arrays.asList(TipoCampoFormularioRemessa.SimNao1), 3), //
    valorTaxaMora("Valor da Taxa de Mora", Arrays.asList(TipoCampoFormularioRemessa.Taxa1, TipoCampoFormularioRemessa.Taxa2, TipoCampoFormularioRemessa.Taxa3, TipoCampoFormularioRemessa.Taxa4, TipoCampoFormularioRemessa.Taxa5, TipoCampoFormularioRemessa.Taxa6, TipoCampoFormularioRemessa.Taxa7)), //
    valorTaxaContrato("Valor da Taxa de Contrato", Arrays.asList(TipoCampoFormularioRemessa.Monetario1, TipoCampoFormularioRemessa.Monetario2, TipoCampoFormularioRemessa.Monetario3, TipoCampoFormularioRemessa.Monetario4)), //
    valorIOF("Valor do IOF", Arrays.asList(TipoCampoFormularioRemessa.Monetario1, TipoCampoFormularioRemessa.Monetario2, TipoCampoFormularioRemessa.Monetario3, TipoCampoFormularioRemessa.Monetario4)), //
    siglaIndiceFinanceiro("Índice de Correção Monetária", Arrays.asList(TipoCampoFormularioRemessa.IndiceFinanceiro)), //
    indicadorComissao("Indicador de Comissão", Arrays.asList(TipoCampoFormularioRemessa.SimNao1), 3), //
    percentualComissao("Percentual de Comissão", Arrays.asList(TipoCampoFormularioRemessa.Taxa1, TipoCampoFormularioRemessa.Taxa2, TipoCampoFormularioRemessa.Taxa3, TipoCampoFormularioRemessa.Taxa4, TipoCampoFormularioRemessa.Taxa5, TipoCampoFormularioRemessa.Taxa6, TipoCampoFormularioRemessa.Taxa7)), //
    indicadorPenalidade("Indicador de Penalidade", Arrays.asList(TipoCampoFormularioRemessa.SimNao1), 3), //
    descricaoPenalidade("Descrição da Penalidade", Arrays.asList(TipoCampoFormularioRemessa.Texto), 50), //
    valorCredito("Valor do Crédito", Arrays.asList(TipoCampoFormularioRemessa.Monetario1, TipoCampoFormularioRemessa.Monetario2, TipoCampoFormularioRemessa.Monetario3, TipoCampoFormularioRemessa.Monetario4)), //
    valorTotalDivida("Valor Total da Dívida", Arrays.asList(TipoCampoFormularioRemessa.Monetario1, TipoCampoFormularioRemessa.Monetario2, TipoCampoFormularioRemessa.Monetario3, TipoCampoFormularioRemessa.Monetario4)), //
    valorParcela("Valor da Parcela", Arrays.asList(TipoCampoFormularioRemessa.Monetario1, TipoCampoFormularioRemessa.Monetario2, TipoCampoFormularioRemessa.Monetario3, TipoCampoFormularioRemessa.Monetario4)), //
    dataVencimentoPrimeiraParcela("Data de Vencimento da Primeira Parcela", Arrays.asList(TipoCampoFormularioRemessa.Data1, TipoCampoFormularioRemessa.Data2, TipoCampoFormularioRemessa.Data3, TipoCampoFormularioRemessa.Data4, TipoCampoFormularioRemessa.Data5, TipoCampoFormularioRemessa.Data6)), //
    dataVencimentoUltimaParcela("Data de Vencimento da Última Parcela", Arrays.asList(TipoCampoFormularioRemessa.Data1, TipoCampoFormularioRemessa.Data2, TipoCampoFormularioRemessa.Data3, TipoCampoFormularioRemessa.Data4, TipoCampoFormularioRemessa.Data5, TipoCampoFormularioRemessa.Data6)), //
    dataLiberacaoCredito("Data da Liberação de Crédito", Arrays.asList(TipoCampoFormularioRemessa.Data1, TipoCampoFormularioRemessa.Data2, TipoCampoFormularioRemessa.Data3, TipoCampoFormularioRemessa.Data4, TipoCampoFormularioRemessa.Data5, TipoCampoFormularioRemessa.Data6)), //
    ufFinanciamento("UF de Financiamento", Arrays.asList(TipoCampoFormularioRemessa.Texto), 2),
    codigoMunicipioLiberacaoCredito("Código do Município da Liberação do Crédito", Arrays.asList(TipoCampoFormularioRemessa.Texto), 4),
    numeroGrupoConsorcio("Número do Grupo do Consórcio", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 6),
    numeroCotaConsorcio("Número da Cota do Consórcio", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 6),
    cpfCnpjDevedorFinanciado("Documento do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 14), //
    nomeDevedor("Nome do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Texto), 40), //
    devedorEndereco("Endereço do Devedor / Logradouro", Arrays.asList(TipoCampoFormularioRemessa.Texto), 30), //
    devedorEnderecoNumero("Número do Endereço do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 5), //
    devedorEnderecoComplemento("Complemento do Endereço do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Texto), 20), //
    devedorEnderecoBairro("Bairro do Endereço do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Texto), 20), //
    devedorEnceredoCodigoMunicipio("Código do Município do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 4), //
    devedorEnderecoUf("UF do Endereço do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Texto), 2), //
    devedorEnderecoCep("CEP do Endereço do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 8), //
    devedorTelefoneDDD("DDD do Telefone do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 2), //
    devedorTelefone("Telefone do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Numerico, TipoCampoFormularioRemessa.Texto), 9), //
    devedorMunicipio("Município do Devedor", Arrays.asList(TipoCampoFormularioRemessa.Texto), 30), //
    comentario("Observação / Comentário", Arrays.asList(TipoCampoFormularioRemessa.Texto), 50),
    email("Email", Arrays.asList(TipoCampoFormularioRemessa.Texto), 50), //
    ignorar1("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar2("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar3("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar4("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar5("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar6("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar7("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar8("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar9("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar10("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar11("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar12("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar13("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar14("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10),
    ignorar15("Ignorar", Arrays.asList(TipoCampoFormularioRemessa.Texto), 10);

    private String descricao;

    private Integer tamanhoPadrao;

    private List<TipoCampoFormularioRemessa> tiposSuportados;

    private CampoFormularioRsngRemessa(String descricao, List<TipoCampoFormularioRemessa> tiposSuportados) {
        this.descricao = descricao;
        this.tiposSuportados = tiposSuportados;
    }

    private CampoFormularioRsngRemessa(String descricao, List<TipoCampoFormularioRemessa> tiposSuportados, Integer tamanhoPadrao) {
        this.descricao = descricao;
        this.tiposSuportados = tiposSuportados;
        this.tamanhoPadrao = tamanhoPadrao;
    }

    public String getDescricao() {
        return descricao;
    }

    public List<TipoCampoFormularioRemessa> getTiposSuportados() {
        return tiposSuportados;
    }

    public Integer getTamanhoPadrao() {
        return tamanhoPadrao;
    }

}
