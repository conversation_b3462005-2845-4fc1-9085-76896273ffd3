package com.registrocontrato.infra.entity;

import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;

@Entity
@Audited
@Table(schema = "registro")
public class TemplateValidacao extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String descricao;

    private Integer posicaoAux;

    private Integer linhaInicial;

    private Integer linhaFinal;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, mappedBy = "templateValidacao", orphanRemoval = true)
    private List<CampoTemplateValidacao> campos;

    @Enumerated(EnumType.STRING)
    private SimNao ativo;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private Financeira financeira;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Integer getPosicaoAux() {
        return posicaoAux;
    }

    public void setPosicaoAux(Integer posicaoAux) {
        this.posicaoAux = posicaoAux;
    }

    public Integer getLinhaInicial() {
        return linhaInicial;
    }

    public void setLinhaInicial(Integer linhaInicial) {
        this.linhaInicial = linhaInicial;
    }

    public Integer getLinhaFinal() {
        return linhaFinal;
    }

    public void setLinhaFinal(Integer linhaFinal) {
        this.linhaFinal = linhaFinal;
    }

    public List<CampoTemplateValidacao> getCampos() {
        return campos;
    }

    public void setCampos(List<CampoTemplateValidacao> campos) {
        this.campos = campos;
    }

    public SimNao getAtivo() {
        return ativo;
    }

    public void setAtivo(SimNao ativo) {
        this.ativo = ativo;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }
}
