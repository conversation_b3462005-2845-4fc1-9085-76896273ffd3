package com.registrocontrato.infra.application;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.web.session.InvalidSessionStrategy;

public class InvalidSession implements InvalidSessionStrategy{
	
	private String invalidSessionUrl;
	
	public InvalidSession(String invalidSessionUrl) {
		this.invalidSessionUrl = invalidSessionUrl;
	}

	@Override
	public void onInvalidSessionDetected(HttpServletRequest request, HttpServletResponse response)throws IOException, ServletException {
		String facesRequest = request.getHeader("Faces-Request");
		String contextPath = request.getContextPath();
		String redirectUrl = contextPath + invalidSessionUrl;
        if (facesRequest != null && facesRequest.equals("partial/ajax")) {
        	String ajaxRedirectXml = createAjaxRedirectXml(redirectUrl);
        	response.setContentType("text/xml");
            response.getWriter().write(ajaxRedirectXml);
    	} else {
             response.sendRedirect(redirectUrl);
        }
	}
	
	private String createAjaxRedirectXml(String redirectUrl) {
        return new StringBuilder()
                        .append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>")
                        .append("<partial-response><redirect url=\"")
                        .append(redirectUrl)
                        .append("\"></redirect></partial-response>")
                        .toString();
    }

}
