package com.registrocontrato.infra.email.entity;

public enum StatusEmail {

    ACEITO("Email aceito", "accepted"),
    ENTREGUE("Email entregue", "delivered"),
    RECLAMADO("Email reclamado", "complained"),
    CLICADO("Email aberto em smartphone", "clicked"),
    ABERTO("Email aberto", "opened"),
    REJEITADO("Email rejeitado", "rejected"),
    SALVO("Email salvo", "stored"),
    FALHA_TEMPORARIA("Falha temporária na entrega", "temporary_fail"),
    FALHA_PERMANENTE("Falha permanente na entrega", "permanent_fail");

    private String descricao;

    private String event;

    StatusEmail(String descricao, String event) {
        this.descricao = descricao;
        this.event = event;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getEvent() {
        return event;
    }

    public static StatusEmail getStatusByEvent(String event) {
        for (StatusEmail status : StatusEmail.values()) {
            if (status.getEvent().equals(event)) {
                return status;
            }
        }
        return null;
    }
}
