package com.registrocontrato.infra.email;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.MessagingException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Email {

    private static Logger log = LoggerFactory.getLogger(Email.class);

    private String assunto;

    private String texto;

    private List<MimeBodyPart> anexosEmail = new ArrayList<MimeBodyPart>();

    private boolean html;

    public static final char REPONDER_PARA = 'R';

    public static final char TIPO_OCULTO = 'O';

    public static final char TIPO_PARA = 'P';

    public static final char TIPO_COPIA = 'C';

    private Map<Character, List<InternetAddress>> destino = new HashMap<>();

    private EnviaEmail enviaEmail;

    public Email(EnviaEmail enviaEmail) {
        this.enviaEmail = enviaEmail;
    }

    public void enviarEmail(String assunto, Map<String, String> parametros, HashMap<Character, List<InternetAddress>> destinos, String caminho) throws IOException {

        setAssunto(assunto);
        setHtml(true);
        setTexto(adicionaHTMAoCorpoDoEmail(parametros, caminho));
        setDestino(destinos);

        if (this.destino.isEmpty()) {
            throw new IllegalArgumentException("Nenhum destinatário adicionado");
        }
        enviaEmail.enviar(this);
    }

    public void enviarEmail(String assunto, HashMap<Character, List<InternetAddress>> destinos, String mensagem) throws Exception {
        setAssunto(assunto);
        setTexto(mensagem);
        setDestino(destinos);

        if (this.destino.isEmpty()) {
            throw new IllegalArgumentException("Nenhum destinatário adicionado");
        }
        enviaEmail.enviar(this);
    }

    private String adicionaHTMAoCorpoDoEmail(Map<String, String> params, String nomeArquivo) throws IOException {

        StringBuffer saida = new StringBuffer();
        if (params != null) {
            params.put("DIR", enviaEmail.getRealPath());
        }

        try (FileInputStream fileInputStream = new FileInputStream(enviaEmail.getRealPath() + nomeArquivo);
             InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, StandardCharsets.UTF_8);
             BufferedReader x22 = new BufferedReader(inputStreamReader)) {

            String linha = x22.readLine();
            String chave = "";
            String linhaTemp = "";
            String linhaTemp2 = "";
            String link = "";
            while (linha != null) {
                linha:
                while (linha.indexOf("$") != -1) {
                    linhaTemp = linha;
                    linha = linhaTemp.substring(0, linhaTemp.indexOf("$"));
                    if (linhaTemp.indexOf("|") > 0) {
                        chave = linhaTemp.substring(linhaTemp.indexOf("$") + 1, linhaTemp.indexOf("|"));
                        if (params != null && params.containsKey(chave)) {
                            linha += params.get(chave);
                        }
                        linha += linhaTemp.substring(linhaTemp.indexOf("|") + 1);
                    } else {
                        linha = linhaTemp;
                        break linha;
                    }

                }
                while (linha.indexOf("<L>") != -1) {
                    linhaTemp2 = linha;
                    linha = linhaTemp2.substring(0, linhaTemp2.indexOf("<L>"));
                    link = linhaTemp2.substring(linhaTemp2.indexOf("<L>") + 3, linhaTemp2.indexOf("<.L>"));
                    String urlLink, descricaoLink = "";
                    if (link.indexOf("#") > 0) {
                        urlLink = link.substring(0, link.indexOf("#"));
                        descricaoLink = link.substring(link.indexOf("#") + 1);
                    } else {
                        urlLink = link;
                        descricaoLink = link;
                    }
                    linha += "<a href='" + urlLink + "'>" + descricaoLink + "</a>";
                    linha += linhaTemp2.substring(linhaTemp2.indexOf("<.L>") + 4);
                }
                saida.append(linha);
                linha = x22.readLine();
            }
        }
//        log.debug("email enviado: " + saida.toString());
        return saida.toString();
    }

    public void addAnexo(String... caminhoCompleto) {
        for (String s : caminhoCompleto) {
            addAnexo(new File(s));
        }
    }

    public void addAnexo(File arquivo) {
        addAnexo(arquivo, arquivo.getName());
    }

    public void addAnexo(File arquivo, String novoNome) {
        if (arquivo.exists()) {
            MimeBodyPart anexo = new MimeBodyPart();
            FileDataSource fileDS = new FileDataSource(arquivo.getAbsoluteFile());
            try {
                anexo.setDataHandler(new DataHandler(fileDS));
                anexo.setFileName(novoNome);
                anexosEmail.add(anexo);
            } catch (MessagingException e) {
                throw new RuntimeException();
            }
        }
    }

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getTexto() {
        return texto;
    }

    public void setTexto(String texto) {
        this.texto = texto;
    }

    public boolean isHtml() {
        return html;
    }

    public void setHtml(boolean html) {
        this.html = html;
    }

    public Map<Character, List<InternetAddress>> getDestino() {
        return destino;
    }

    public void setDestino(Map<Character, List<InternetAddress>> destino) {
        this.destino = destino;
    }

    public List<MimeBodyPart> getAnexosEmail() {
        return anexosEmail;
    }

    public EnviaEmail getEnviaEmail() {
        return enviaEmail;
    }

    public void setEnviaEmail(EnviaEmail enviaEmail) {
        this.enviaEmail = enviaEmail;
    }
}
