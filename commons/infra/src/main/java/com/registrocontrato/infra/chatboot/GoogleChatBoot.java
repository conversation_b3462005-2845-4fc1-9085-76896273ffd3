package com.registrocontrato.infra.chatboot;

import com.registrocontrato.infra.email.entity.StatusEmail;
import org.apache.juli.logging.Log;
import org.apache.juli.logging.LogFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Component
public class GoogleChatBoot {

    private final Log log = LogFactory.getLog(getClass());

    @Value("${google.chat.monitoramento:null}")
    private String urlRobos;

    @Value("${google.chat.email:null}")
    private String urlEmails;

    @Value("${google.chat.email.key:null}")
    private String keyRobos;

    @Value("${google.chat.monitoramento.key:null}")
    private String keyEmail;

    @Value("${google.chat.monitoramento.token:null}")
    private String tokenRobos;

    @Value("${google.chat.email.falhaPermanente:null}")
    private String tokenEmailFalhaPermante;

    @Value("${google.chat.email.falhaTemporaria:null}")
    private String getTokenEmailFalhaTemporaria;

    @Value("${spring.profiles.active:desenv}")
    private String profile;

    public void enviarMensagemGoogleChatRobos(String mensagem) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.urlRobos);
        builder.queryParam("keyRobos", this.keyRobos);
        builder.queryParam("tokenRobos", this.tokenRobos);

        enviarMensagemGoogleChat(mensagem, builder.build().encode().toUri());
    }

    public void enviarMensagemGoogleChatEmail(String mensagem, StatusEmail statusEmail) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(this.urlEmails);
        builder.queryParam("keyRobos", this.keyEmail);
        if (statusEmail == StatusEmail.FALHA_PERMANENTE)
            builder.queryParam("tokenRobos", this.tokenEmailFalhaPermante);
        if (statusEmail == StatusEmail.FALHA_TEMPORARIA)
            builder.queryParam("tokenRobos", this.getTokenEmailFalhaTemporaria);

        enviarMensagemGoogleChat(mensagem, builder.build().encode().toUri());
    }

    private void enviarMensagemGoogleChat(String mensagem, URI endereco) {
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = null;
        try {
            Map<String, String> param = new HashMap<>();
            param.put("text", ajustarMensagemEmHomol(mensagem));

            restTemplate.postForEntity(endereco, param, String.class);
        } catch (Exception e) {
            log.error(response.getBody());
        }
    }

    private String ajustarMensagemEmHomol(String mensagem) {
        String retorno = "HOMOLOGAÇÃO";
        if (!profile.equals("prod"))
            return retorno + " " + mensagem;
        return mensagem;
    }

}
