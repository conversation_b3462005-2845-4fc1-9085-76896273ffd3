package com.registrocontrato.infra.service;

import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.io.Serializable;

@NoRepositoryBean
public interface BaseRepository<T> extends PagingAndSortingRepository<T, Long>, Serializable {

	Page<T> findAll(Example<T> of, Pageable pageRequest);

	Page<T> findAll(Specification<T> specification, Pageable pageRequest);

}
