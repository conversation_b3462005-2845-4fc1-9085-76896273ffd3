package com.registrocontrato.infra.jsf.converter;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.ConverterException;
import javax.faces.convert.FacesConverter;

@FacesConverter(value = "cpfCnpjConverter")
public class CpfCnpjConverter implements Converter {

	public Object getAsObject(FacesContext context, UIComponent component, String value) throws ConverterException {
		String documento = value;
		if (value != null && !value.equals(""))
			documento = value.replaceAll("\\.", "").replaceAll("\\-", "").replaceAll("/", "");
		return documento;
	}

	public String getAsString(FacesContext context, UIComponent component, Object value) throws ConverterException {
		String documento = (String) value;
		if (documento != null && documento.length() == 14)
			documento = documento.substring(0, 2) + "." + documento.substring(2, 5) + "." + documento.substring(5, 8) + "/" + documento.substring(8, 12) + "-" + documento.substring(12, 14);

		if (documento != null && documento.length() == 11)
			documento = documento.substring(0, 3) + "." + documento.substring(3, 6) + "." + documento.substring(6, 9) + "-" + documento.substring(9, 11);

		return documento;
	}
}