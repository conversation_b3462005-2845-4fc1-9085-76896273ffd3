package com.registrocontrato.infra.entity;

public enum TipoRemarcacao {

    REMARCADO("Remarcado",true),
    NORMAL("Normal",false);

    private final String descricao;

    private final boolean tipo;

    TipoRemarcacao(String descricao, boolean tipo) {
        this.descricao = descricao;
        this.tipo = tipo;
    }

    public  String getDescricao() {
        return descricao;
    }

    public  boolean isTipo() {
        return tipo;
    }
}
