package com.registrocontrato.infra.jsf.converter;

import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.entity.Integradora;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

@FacesConverter(value = "integradoraConverter")
public class IntegradoraConverter implements Converter {
    @Override
    public Object getAsObject(FacesContext facesContext, UIComponent uiComponent, String s) {
        String[] split = s.replace("Integradora[", "").replaceAll("]", "").split(",");
        Integradora entity = new Integradora();
        System.out.println(split[0]);
        entity.setId(Long.parseLong(split[0].replaceAll("\\D", "")));
        return entity;
    }

    @Override
    public String getAsString(FacesContext facesContext, UIComponent uiComponent, Object o) {
        if (o != null) {
            return o.toString();
        }
        return "";
    }
}
