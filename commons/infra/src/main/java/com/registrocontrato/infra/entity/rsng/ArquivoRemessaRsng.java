package com.registrocontrato.infra.entity.rsng;

import com.registrocontrato.infra.entity.SimNao;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Audited
@Table(schema = "rsng")
public class ArquivoRemessaRsng {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String nome;

    @Column(nullable = false)
    private Integer status;

    @Column(nullable = false)
    private String hash;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "data_transacao", nullable = false)
    private Date dataTransacao;

    @OneToMany(cascade = {CascadeType.ALL}, fetch = FetchType.EAGER, orphanRemoval = true, mappedBy = "arquivoRemessa")
    private List<ItemArquivoRemessaRsng> registros = new ArrayList<>();

    @Column(name = "tipo_operacao", nullable = true)
    private String tipoOperacao;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private TemplateRsng templateRemessa;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Date getDataTransacao() {
        return dataTransacao;
    }

    public void setDataTransacao(Date dataTransacao) {
        this.dataTransacao = dataTransacao;
    }

    public List<ItemArquivoRemessaRsng> getRegistros() {
        return registros;
    }

    public void setRegistros(List<ItemArquivoRemessaRsng> registros) {
        this.registros = registros;
    }

    public String getTipoOperacao() {
        return tipoOperacao;
    }

    public void setTipoOperacao(String tipoOperacao) {
        this.tipoOperacao = tipoOperacao;
    }

    public TemplateRsng getTemplateRemessa() {
        return templateRemessa;
    }

    public void setTemplateRemessa(TemplateRsng templateRemessa) {
        this.templateRemessa = templateRemessa;
    }

    public Long getTotalSucesso() {
        if (registros != null)
            return registros.stream().filter(i -> i.getValida() == SimNao.S).count();
        return 0L;
    }

    public Long getTotalErro() {
        if (registros != null)
            return registros.stream().filter(i -> i.getValida() == SimNao.N).count();
        return 0L;
    }

    public Integer getTotalRegistros() {
        if (registros != null)
            return registros.size();
        return 0;
    }
}
