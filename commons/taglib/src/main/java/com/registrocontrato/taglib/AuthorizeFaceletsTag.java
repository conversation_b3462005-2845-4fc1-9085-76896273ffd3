package com.registrocontrato.taglib;

import javax.el.ValueExpression;
import javax.faces.view.facelets.FaceletContext;
import javax.faces.view.facelets.TagAttribute;

import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

public class AuthorizeFaceletsTag extends AbstractFaceletsAuthorizeTag {

	public AuthorizeFaceletsTag(FaceletContext faceletContext, TagAttribute access, TagAttribute url, TagAttribute method, TagAttribute ifAllGranted, TagAttribute ifAnyGranted, TagAttribute ifNotGranted) {
		setAccess(getAttributeValue(faceletContext, access, false));
		setUrl(getAttributeValue(faceletContext, url, true));
		setMethod(getAttributeValue(faceletContext, method, true));
		setIfAllGranted(getAttributeValue(faceletContext, ifAllGranted, true));
		setIfAnyGranted(getAttributeValue(faceletContext, ifAnyGranted, true));
		setIfNotGranted(getAttributeValue(faceletContext, ifNotGranted, true));
	}

	public AuthorizeFaceletsTag() {
	}

	private static final String AND = " and ";

	void setIfAllGranted(String ifAllGranted) {
		String[] roles = StringUtils.tokenizeToStringArray(ifAllGranted, ",");
		if (!ObjectUtils.isEmpty(roles)) {
			String expression = toHasRoleExpression(roles);
			setAccess(getAccess() != null ? getAccess() + AND + expression : expression);
		}
	}

	void setIfAnyGranted(String ifAnyGranted) {
		String[] roles = StringUtils.tokenizeToStringArray(ifAnyGranted, ",");
		if (!ObjectUtils.isEmpty(roles)) {
			String expression = toHasAnyRoleExpression(roles, false);
			setAccess(getAccess() != null ? getAccess() + AND + expression : expression);
		}
	}

	void setIfNotGranted(String ifNotGranted) {
		String[] roles = StringUtils.tokenizeToStringArray(ifNotGranted, ",");
		if (!ObjectUtils.isEmpty(roles)) {
			String expression = toHasAnyRoleExpression(roles, true);
			setAccess(getAccess() != null ? getAccess() + AND + expression : expression);
		}
	}

	private static String toHasRoleExpression(String[] roles) {
		StringBuilder expression = new StringBuilder();
		boolean insertSeparator = false;
		for (String role : roles) {
			expression.append(insertSeparator ? AND : "");
			expression.append("hasRole('").append(role).append("')");
			insertSeparator = true;
		}
		return expression.toString();
	}

	private static String toHasAnyRoleExpression(String[] roles, boolean negate) {
		StringBuilder expression = new StringBuilder();
		expression = expression.append(negate ? "!" : "");
		expression = expression.append("hasAnyRole(");
		boolean insertSeparator = false;
		for (String role : roles) {
			expression.append(insertSeparator ? "," : "");
			expression.append('\'').append(role).append('\'');
			insertSeparator = true;
		}
		return expression.append(")").toString();
	}

	private String getAttributeValue(FaceletContext faceletContext, TagAttribute tagAttribute, boolean evaluate) {
		String value = null;
		if (tagAttribute != null) {
			if (evaluate) {
				ValueExpression expression = tagAttribute.getValueExpression(faceletContext, String.class);
				value = (String) expression.getValue(faceletContext.getFacesContext().getELContext());
			} else {
				value = tagAttribute.getValue();
			}
		}
		return value;
	}

}
