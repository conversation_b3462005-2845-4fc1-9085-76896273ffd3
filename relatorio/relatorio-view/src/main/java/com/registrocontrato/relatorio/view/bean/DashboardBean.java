package com.registrocontrato.relatorio.view.bean;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.relatorio.service.RelatorioService;
import com.registrocontrato.relatorio.service.dto.DashboardDTO;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.primefaces.model.charts.ChartData;
import org.primefaces.model.charts.ChartDataSet;
import org.primefaces.model.charts.ChartModel;
import org.primefaces.model.charts.bar.BarChartDataSet;
import org.primefaces.model.charts.bar.BarChartModel;
import org.primefaces.model.charts.line.LineChartDataSet;
import org.primefaces.model.charts.line.LineChartModel;
import org.primefaces.model.charts.pie.PieChartDataSet;
import org.primefaces.model.charts.pie.PieChartModel;
import org.springframework.stereotype.Controller;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.registrocontrato.infra.util.PlaceconUtil.getColorGraph;
import static com.registrocontrato.infra.util.PlaceconUtil.getComecoMesAtual;

@ViewScope
@Controller
public class DashboardBean extends BaseBean {

    private BarChartModel barChartModel;

    private PieChartModel pieChartModel;

    private final RelatorioService relatorioService;

    private final UsuarioService usuarioService;

    private String mes;

    private List<DashboardDTO> list;

    private HashMap<String, BigDecimal> valoresFinal = new HashMap<>();

    public DashboardBean(RelatorioService relatorioService, UsuarioService usuarioService) {
        this.relatorioService = relatorioService;
        this.usuarioService = usuarioService;
    }

    @PostConstruct
    public void init() {
        barChartModel = new BarChartModel();
        pieChartModel = new PieChartModel();
        Date date = getComecoMesAtual();

        SimpleDateFormat sdf = new SimpleDateFormat("MMMM 'de' yyyy", new Locale("pt", "BR"));
        mes = sdf.format(date);
        list = relatorioService.findDashboard(date, getUsername());
        buildChart();
    }

    private void buildChart() {
        if (list == null || list.isEmpty()) {
            return;
        }
        ChartData pieData = new ChartData();
        ChartData barData = new ChartData();
        List<String> labels = new ArrayList<>();
        List<String> colors = new ArrayList<>();
        List<Number> valores = new ArrayList<>();
        PieChartDataSet pieDataSet = new PieChartDataSet();

        for (DashboardDTO relatorio : list) {
            BarChartDataSet barDataSet = new BarChartDataSet();
            barDataSet.setLabel(relatorio.getFinanceira() + " - " + relatorio.getUf().name());
            BigDecimal valorPrevisto = relatorio.getValorCredenciada().multiply(new BigDecimal(relatorio.getQuantidadeContratos()));
            BigDecimal valorPrevistoDetran = relatorio.getValorDetran().multiply(new BigDecimal(relatorio.getQuantidadeContratos()));
            BigDecimal percentual = relatorio.getPercentualDesconto().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
            BigDecimal valorDesconto = relatorio.getValorCredenciada().multiply(new BigDecimal(relatorio.getQuantidadeContratos())).multiply(percentual);

            BigDecimal valorFinal = valorPrevisto.add(valorPrevistoDetran).subtract(valorDesconto);
            relatorio.setValorCredenciada(valorPrevisto);
            relatorio.setValorDetran(valorPrevistoDetran);
            relatorio.setPercentualDesconto(valorDesconto);
            relatorio.setValorFinal(valorFinal);
            barDataSet.setData(Collections.singletonList(valorFinal));
            valores.add(relatorio.getQuantidadeContratos());
            labels.add(relatorio.getFinanceira() + " - " + relatorio.getUf().name());
            barData.addChartDataSet(barDataSet);
            String color = getColorGraph();
            colors.add(color);
            barDataSet.setBackgroundColor(color);
            valoresFinal.compute(relatorio.getFinanceira(), (key, oldValue) ->
                    oldValue == null ? valorFinal : oldValue.add(valorFinal));
        }
        pieDataSet.setData(valores);
        pieDataSet.setBackgroundColor(colors);
        pieData.addChartDataSet(pieDataSet);
        pieData.setLabels(labels);
        pieChartModel.setData(pieData);
        barData.setLabels(Collections.singletonList("Valor previsto"));
        barChartModel.setData(barData);
    }

    public String getRelatorioDashboard(){
        Usuario u = usuarioService.findByCpfFinanceiras(getUsername());
        if (u.isPerfilFinanceira()) {
            return "dashboard/principal-financeira.xhtml";
        }
        return "dashboard/geral.xhtml";
    }

    public BarChartModel getBarChartModel() {
        return barChartModel;
    }

    public PieChartModel getPieChartModel() {
        return pieChartModel;
    }

    public List<Map.Entry<String, BigDecimal>> getValoresFinalList() {
        return new ArrayList<>(valoresFinal.entrySet());
    }

    public String getMes() {
        return mes;
    }

    public void setMes(String mes) {
        this.mes = mes;
    }

    public List<DashboardDTO> getList() {
        return list;
    }
}
