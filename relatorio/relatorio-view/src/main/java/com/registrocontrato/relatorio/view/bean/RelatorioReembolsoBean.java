package com.registrocontrato.relatorio.view.bean;

import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.relatorio.service.RelatorioReembolsoService;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Controller;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Controller
@ViewScope
public class RelatorioReembolsoBean extends BaseBean {
    private final RelatorioReembolsoService service;

    private List<Object[]> list;

    private Boolean consultaRealizada = Boolean.FALSE;

    private Date dataInicio;

    private Date dataFim;

    public RelatorioReembolsoBean(RelatorioReembolsoService service) {
        super();
        this.service = service;
    }

    public void search() {
        if (dataInicio.after(dataFim)) {
            addMessageError("A data fim não pode ser antes que a data de início.");
            return;
        }

        list = service.buscarFinanceirasLinhaDigitavel(dataInicio, dataFim);

        list.forEach(objects -> objects[3] = objects[3].toString().replace(".", ","));

        list.forEach(objects -> objects[3] = String.format("R$ %s", objects[3]));
    }

    public void excelExport() {
        HttpServletResponse response = (HttpServletResponse) getExternalContext().getResponse();
        response.setHeader("Content-Disposition", "attachment; filename=\"" + DateFormatUtils.format(new Date(), "dd/MM/yyyy HH:mm") + ".xlsx\"");

        extracted(response);
    }

    public List<Object[]> getList() {
        return list;
    }

    public void setList(List<Object[]> list) {
        this.list = list;
    }

    public Boolean getConsultaRealizada() {
        return consultaRealizada;
    }

    public void setConsultaRealizada(Boolean consultaRealizada) {
        this.consultaRealizada = consultaRealizada;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    private void extracted(HttpServletResponse response) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Data");

            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("Financeira");
            headerRow.createCell(1).setCellValue("CNPJ");
            headerRow.createCell(2).setCellValue("UF");
            headerRow.createCell(3).setCellValue("Valor");

            int rowNum = 1;
            for (Object[] object : getList()) {
                Row row = sheet.createRow(rowNum++);
                row.createCell(0).setCellValue(Objects.nonNull(object[0]) ? object[0].toString() : "");
                row.createCell(1).setCellValue(Objects.nonNull(object[1]) ? object[1].toString() : "");
                row.createCell(2).setCellValue(Objects.nonNull(object[2]) ? object[2].toString() : "");
                row.createCell(3).setCellValue(Objects.nonNull(object[3]) ? object[3].toString() : "");
            }

            fecharLoading();
            fecharPlanilha(workbook, response);
        } catch (IOException e) {
            logger.error(e.getMessage());
        }
    }

    private void fecharPlanilha(XSSFWorkbook workbook, HttpServletResponse response) throws IOException {
        ServletOutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();

        getCurrentInstance().responseComplete();
    }

    private void fecharLoading() {
        String viewId = getCurrentInstance().getViewRoot().getViewId().replace("/", "_");
        getCurrentInstance().getExternalContext().addResponseCookie(
                org.primefaces.util.Constants.DOWNLOAD_COOKIE + viewId,
                "true",
                Collections.emptyMap()
        );
    }
}
