package com.registrocontrato.relatorio.view.bean;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.ArquivoRemessa;
import com.registrocontrato.relatorio.service.RelatorioRemessaService;
import com.registrocontrato.relatorio.service.dto.RelatorioArquivoRemessaDTO;

@ViewScope
@Controller
public class RelatorioRemessaBean extends BaseCrud<ArquivoRemessa, RelatorioArquivoRemessaDTO> {

	private static final String FLASH_FILTER = "RelatorioRemessaBean";

	private static final long serialVersionUID = 1L;
	
	private Boolean consultaRealizada = false;

	@Autowired
	protected RelatorioRemessaService service;

	@Override
	public void postInitialization() {
		setSize(1);
		super.postInitialization();
		RelatorioArquivoRemessaDTO filter = (RelatorioArquivoRemessaDTO) getFilter();
		filter.setUsuario(getUsername());
	}
	
	@Override
	public void search() {
		super.search();
		consultaRealizada = true;
	}

	public String print() {
		System.out.println("printando");
		getFlash().put(FLASH_FILTER, getFilter());
		return "/remessa/print-remessa.xhtml?faces-redirect=true";
	}

	public void loadPrint() throws IOException {
		setFilter((RelatorioArquivoRemessaDTO) getFlash().get(FLASH_FILTER));
		if (getFilter() == null) {
			getExternalContext().redirect(getExternalContext().getRequestContextPath() + "/error/403.xhtml");
			getCurrentInstance().responseComplete();
		}
		getFilter().setUsuario(getUsername());
	}

	public List<ArquivoRemessa> getListPrint() {
		Page<ArquivoRemessa> page = service.findAll(0, 200000, getFilter());
		return page.getContent();
	}
	
	@Override
	public BaseService<ArquivoRemessa, RelatorioArquivoRemessaDTO> getService() {
		return service;
	}

	public Boolean getConsultaRealizada() {
		return consultaRealizada;
	}
}
