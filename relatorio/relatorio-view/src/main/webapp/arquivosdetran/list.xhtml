<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Arquivos DETRAN MG</h4>

							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<p:dataTable id="dataTable" var="object" value="#{arquivosDetranMGBean.list}" paginator="true" rows="5" paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="{currentPage} de {totalPages} - Total Registros: #{arquivosDetranMGBean.list.rowCount}" 
										paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="Lote de Arquivos">
											#{object}
										</p:column>
										<p:column headerText="">
											<p:commandLink actionListener="#{arquivosDetranMGBean.setPasta(object)}" styleClass="btn btn-link" title="Download Lote de Arquivos" ajax="false" onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
												<span class="mdi mdi-download" />
												<p:fileDownload value="#{arquivosDetranMGBean.file}" />
											</p:commandLink>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
	</ui:define>
</ui:composition>