// Starsoft Mongolia LLC Temuujin Ariunbold

(function ($) {
  $.extend($.summernote.lang, {
    'mn-MN': {
      font: {
        bold: 'Тод',
        italic: 'Налуу',
        underline: 'Доогуур зураас',
        clear: 'Цэвэрлэх',
        height: 'Өндөр',
        name: 'Фонт',
        superscript: 'Дээд илтгэгч',
        subscript: 'Доод илтгэгч',
        strikethrough: 'Дарах',
        size: 'Хэмжээ'
      },
      image: {
        image: 'Зураг',
        insert: 'Оруулах',
        resizeFull: 'Хэмжээ бүтэн',
        resizeHalf: 'Хэмжээ 1/2',
        resizeQuarter: 'Хэмжээ 1/4',
        floatLeft: 'Зүүн талд байрлуулах',
        floatRight: 'Баруун талд байрлуулах',
        floatNone: 'Анхдагч байрлалд аваачих',
        shapeRounded: 'Хүрээ: Дугуй',
        shapeCircle: 'Хүрээ: Тойрог',
        shapeThumbnail: 'Хүрээ: Хураангуй',
        shapeNone: 'Хүрээгүй',
        dragImageHere: 'Зургийг энд чирч авчирна уу',
        selectFromFiles: 'Файлуудаас сонгоно уу',
        maximumFileSize: 'Файлын дээд хэмжээ',
        maximumFileSizeError: 'Файлын дээд хэмжээ хэтэрсэн',
        url: 'Зургийн URL',
        remove: 'Зургийг устгах'
      },
      video: {
        video: 'Видео',
        videoLink: 'Видео холбоос',
        insert: 'Видео оруулах',
        url: 'Видео URL?',
        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion болон Youku)'
      },
      link: {
        link: 'Холбоос',
        insert: 'Холбоос оруулах',
        unlink: 'Холбоос арилгах',
        edit: 'Засварлах',
        textToDisplay: 'Харуулах бичвэр',
        url: 'Энэ холбоос хаашаа очих вэ?',
        openInNewWindow: 'Шинэ цонхонд нээх'
      },
      table: {
        table: 'Хүснэгт'
      },
      hr: {
        insert: 'Хэвтээ шугам оруулах'
      },
      style: {
        style: 'Хэв маяг',
        p: 'p',
        blockquote: 'Иш татах',
        pre: 'Эх сурвалж',
        h1: 'Гарчиг 1',
        h2: 'Гарчиг 2',
        h3: 'Гарчиг 3',
        h4: 'Гарчиг 4',
        h5: 'Гарчиг 5',
        h6: 'Гарчиг 6'
      },
      lists: {
        unordered: 'Эрэмбэлэгдээгүй',
        ordered: 'Эрэмбэлэгдсэн'
      },
      options: {
        help: 'Тусламж',
        fullscreen: 'Дэлгэцийг дүүргэх',
        codeview: 'HTML-Code харуулах'
      },
      paragraph: {
        paragraph: 'Хэсэг',
        outdent: 'Догол мөр хасах',
        indent: 'Догол мөр нэмэх',
        left: 'Зүүн тийш эгнүүлэх',
        center: 'Төвд эгнүүлэх',
        right: 'Баруун тийш эгнүүлэх',
        justify: 'Мөрийг тэгшлэх'
      },
      color: {
        recent: 'Сүүлд хэрэглэсэн өнгө',
        more: 'Өөр өнгөнүүд',
        background: 'Дэвсгэр өнгө',
        foreground: 'Үсгийн өнгө',
        transparent: 'Тунгалаг',
        setTransparent: 'Тунгалаг болгох',
        reset: 'Анхдагч өнгөөр тохируулах',
        resetToDefault: 'Хэвд нь оруулах'
      },
      shortcut: {
        shortcuts: 'Богино холбоос',
        close: 'Хаалт',
        textFormatting: 'Бичвэрийг хэлбэржүүлэх',
        action: 'Үйлдэл',
        paragraphFormatting: 'Догол мөрийг хэлбэржүүлэх',
        documentStyle: 'Бичиг баримтын хэв загвар'
      },
      history: {
        undo: 'Буцаах',
        redo: 'Дахин хийх'
      },
      specialChar: {
        specialChar: 'Тусгай тэмдэгт',
        select: 'Тусгай тэмдэгт сонгох'
      }
    }
  });
})(jQuery);
