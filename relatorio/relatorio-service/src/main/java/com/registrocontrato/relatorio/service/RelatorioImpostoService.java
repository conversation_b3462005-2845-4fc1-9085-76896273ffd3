package com.registrocontrato.relatorio.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.registrocontrato.finaceiro.entity.Imposto;
import com.registrocontrato.finaceiro.entity.RecorrenciaImposto;
import com.registrocontrato.finaceiro.entity.TipoImposto;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.relatorio.repository.RelatorioImpostoRepository;
import com.registrocontrato.relatorio.service.dto.ResultadoImpostoDTO;

@Service
public class RelatorioImpostoService {
	
	@Autowired
	private RelatorioImpostoRepository repository;

	private BigDecimal getBaseCalculo(LocalDateTime d) {
		/**
		 * caso altere a base de calculo acrescentar a data de verificação da nova aliquota
		 */
		if (d.isBefore(new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())) {
			return new BigDecimal(0.32);
		}
		return BigDecimal.ZERO;
	}
	
	/**
	 * base para adicional de IRPJ levando em consideracao o periodo de um mes, ou seja, um terço do valor trimestral da base
	 * @param d
	 * @return
	 */
	private BigDecimal getBaseAdicionalIRPJ(LocalDateTime d) {
		//base de 60000
		if (d.isBefore(new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())) {
			return new BigDecimal((60000/ 3));
		}
		return BigDecimal.ZERO;
	}

	
	
	
	private BigDecimal getPercentualAdicionalIRPJ() {
		/**
		 * caso altere a base de calculo acrescentar a data de verificação da nova aliquota
		 */
		LocalDateTime d = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().minusMonths(1).atStartOfDay();
		if (d.isBefore(new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())) {
			return new BigDecimal(0.1);
		}
		return BigDecimal.ZERO;
	}
	
	
	private BigDecimal getTotalPeriodo(LocalDateTime l, ResultadoImpostoDTO r) {
		Date dataInicio = Date.from(l.atZone(ZoneId.systemDefault()).toInstant());
		Date dataFim =  Date.from(l.plusMonths(1).minusDays(1).atZone(ZoneId.systemDefault()).toInstant());
		r.setDescricaoPeriodoMensal(PlaceconUtil.formatarDataPadrao(dataInicio) + " até " + PlaceconUtil.formatarDataPadrao(dataFim));
		BigDecimal v = repository.getTotalPlace(dataInicio, dataFim);
		return v == null ? BigDecimal.ZERO : v;
	}
	
	
	private BigDecimal getTotalTrimestre(LocalDateTime l, ResultadoImpostoDTO r) {
		int totalMeses = 0;
		if (l.getMonth() == Month.FEBRUARY || l.getMonth() == Month.MAY || l.getMonth() == Month.AUGUST || l.getMonth() == Month.NOVEMBER) {
			totalMeses = 2;
		}
		else if(l.getMonth() == Month.APRIL || l.getMonth() == Month.JULY || l.getMonth() == Month.OCTOBER || l.getMonth() == Month.JANUARY) {
			totalMeses = 1;
		}
		Date dataInicio = Date.from(l.minusMonths(totalMeses).atZone(ZoneId.systemDefault()).toInstant());
		Date dataFim = Date.from(l.plusMonths(1).minusDays(1).atZone(ZoneId.systemDefault()).toInstant()); 
		r.setDescricaoPeriodoTrimestral(PlaceconUtil.formatarDataPadrao(dataInicio) + " até " + PlaceconUtil.formatarDataPadrao(dataFim));
		BigDecimal v = repository.getTotalPlace(dataInicio, dataFim);
		return v == null ? BigDecimal.ZERO : v;
	}
	
	
	public ResultadoImpostoDTO calcularImpostos(Date data) {
		LocalDateTime l = data.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime().minusMonths(2);
		BigDecimal baseCalculo = getBaseCalculo(l);
		ResultadoImpostoDTO r = new ResultadoImpostoDTO(l);
		r.setBaseCalculo(baseCalculo);
		BigDecimal totalPlace = getTotalPeriodo(l, r);
		
		r.setTotalServicos(totalPlace);
		
		List<Imposto>mensais = repository.getImpostosMensais(data);
		for (Imposto i : mensais) {
			r.addResultadoImpostoMensal(r.new ResultadoImposto(i.getPercentualImposto().multiply(totalPlace)
					.divide(new BigDecimal(100), BigDecimal.ROUND_CEILING), i));
		}
		
		int qtdMeses = 1;
		if (l.getMonthValue() == 2 || l.getMonthValue() == 5 || l.getMonthValue() == 8 || l.getMonthValue() == 11) {
			qtdMeses = 3;
		}
		else if (l.getMonthValue() == 1 || l.getMonthValue() == 4 || l.getMonthValue() == 7 || l.getMonthValue() == 10) {
			qtdMeses = 2;
		}
		
		Map<Integer, BigDecimal>mapaTotalPeriodo = new HashMap<Integer, BigDecimal>();
		mapaTotalPeriodo.put(1, totalPlace);
		if (qtdMeses > 1) {
			Date dataInicio = Date.from(l.minusMonths(1).atZone(ZoneId.systemDefault()).toInstant());
			Date dataFim =  Date.from(l.minusDays(1).atZone(ZoneId.systemDefault()).toInstant());
			BigDecimal total = repository.getTotalPlace(dataInicio, dataFim);
			mapaTotalPeriodo.put(2, total == null ? BigDecimal.ZERO : total);
			if (qtdMeses == 3) {
				dataInicio = Date.from(l.minusMonths(2).atZone(ZoneId.systemDefault()).toInstant());
				dataFim =  Date.from(l.minusMonths(1).minusDays(1).atZone(ZoneId.systemDefault()).toInstant());
				total = repository.getTotalPlace(dataInicio, dataFim);
				mapaTotalPeriodo.put(3, total == null ? BigDecimal.ZERO : total);
			}
		}
		String [] desc = new String[qtdMeses];	
		
		for (TipoImposto tipo : TipoImposto.values()) {
			int x = 0;
			String [] valores = new String[qtdMeses];
			BigDecimal total = BigDecimal.ZERO;
			String percentual = "";
			while (x < qtdMeses) {
				LocalDateTime lAux = l.minusMonths(x);
				Imposto imposto = repository.getImposto(Date.from(lAux.atZone(ZoneId.systemDefault()).toInstant()), tipo);
				if (imposto != null) {
					BigDecimal valor = BigDecimal.ZERO;
					desc[x] = PlaceconUtil.leftPad(String.valueOf(lAux.getMonthValue()), 2, "0") + "/" + lAux.getYear();
					percentual = imposto.getPercentualImposto() + "%";
					if (imposto.getRecorrencia() == RecorrenciaImposto.MENSAL) {
						valor = mapaTotalPeriodo.get(x+1).multiply(imposto.getPercentualImposto()).divide(new BigDecimal(100), BigDecimal.ROUND_CEILING);
						valores[x] = PlaceconUtil.formataValorMonetario(valor);
					}
					else {
						valor = mapaTotalPeriodo.get(x+1).multiply(baseCalculo).multiply(imposto.getPercentualImposto()).divide(new BigDecimal(100), BigDecimal.ROUND_CEILING);
							valores[x] = PlaceconUtil.formataValorMonetario(valor);
							
					}
					total = total.add(valor);
				}
				else if (tipo == TipoImposto.ADICIONAL_IRPJ) {
					BigDecimal valor = mapaTotalPeriodo.get(x+1).multiply(baseCalculo).divide(new BigDecimal(1), BigDecimal.ROUND_CEILING);
					if (valor.compareTo(getBaseAdicionalIRPJ(lAux)) > 0) {
						valor = valor.subtract(getBaseAdicionalIRPJ(l));
						valor = valor.multiply(getPercentualAdicionalIRPJ()).divide(BigDecimal.ONE, BigDecimal.ROUND_CEILING);
					}
					else {
						valor = BigDecimal.ZERO;
					}
					valores[x] = PlaceconUtil.formataValorMonetario(valor);
					percentual = "10%";
					total = total.add(valor);
				}
				x++;
			}
			r.addImpostoTrimestral(r.new ResultadoTrimestre(tipo.getDescricao(), percentual , valores, PlaceconUtil.formataValorMonetario(total)));
		}
		r.setPeriodosTrimestre(desc);
		
		BigDecimal totalPlaceTrimestre = getTotalTrimestre(l, r);
		r.setTotalServicosTrimestre(totalPlaceTrimestre);
		
		
		
		
		return r;
	}
	
}
