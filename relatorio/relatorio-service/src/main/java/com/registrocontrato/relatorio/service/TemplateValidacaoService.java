package com.registrocontrato.relatorio.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.TemplateValidacao;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.service.dto.TemplateDTO;
import com.registrocontrato.relatorio.repository.TemplateValidacaoRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TemplateValidacaoService extends BaseService<TemplateValidacao, TemplateDTO> {

    private static final long serialVersionUID = 1L;

    @Autowired
    private TemplateValidacaoRepository templateRepository;

    @Override
    public Page<TemplateValidacao> findAll(int first, int pageSize, TemplateDTO filter) {
        TemplateValidacao entity = new TemplateValidacao();
        BeanUtils.copyProperties(filter, entity);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("descricao", match -> match.contains().ignoreCase());

        return templateRepository.findAll(Example.of(entity, matcher), new PageRequest(first / pageSize, pageSize, new Sort(Sort.Direction.ASC, "descricao")));
    }

    @Override
    public TemplateValidacao findOne(Long id) {
        return templateRepository.findByIdWithDetails(id);
    }

    @Override
    protected PagingAndSortingRepository<TemplateValidacao, Long> getRepository() {
        return templateRepository;
    }

    @Override
    public void save(TemplateValidacao entity) throws ServiceException {
        TemplateValidacao duplicado = templateRepository.findByDescricaoIgnoreCase(entity.getDescricao());
        if (duplicado != null && !duplicado.getId().equals(entity.getId())) {
            throw new ServiceException("Template já cadastrado com essa descrição.");
        }
        super.save(entity);
    }

    public List<TemplateValidacao> findByFinanceira(Financeira financeira) {
        return templateRepository.findTemplateValidacaoByFinanceira(financeira);
    }

    public List<TemplateValidacao> findAllByAtivo() {
        return templateRepository.findAllByAtivo(SimNao.S);
    }
}
