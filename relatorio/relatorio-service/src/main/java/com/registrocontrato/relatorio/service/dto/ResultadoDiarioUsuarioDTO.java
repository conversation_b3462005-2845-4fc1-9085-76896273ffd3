package com.registrocontrato.relatorio.service.dto;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

import com.registrocontrato.infra.entity.Uf;

public class ResultadoDiarioUsuarioDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	private Uf uf;

	private String financeira;

	private String usuario;

	private Date data;

	private BigInteger cadastros;

	private BigInteger transmissoes;

	private BigInteger exclusoes;

	public ResultadoDiarioUsuarioDTO() {
	}

	public ResultadoDiarioUsuarioDTO(Uf uf, String financeira, String usuario, Date data, BigInteger cadastros, BigInteger transmissoes, BigInteger exclusoes) {
		super();
		this.uf = uf;
		this.financeira = financeira;
		this.usuario = usuario;
		this.data = data;
		this.cadastros = cadastros;
		this.transmissoes = transmissoes;
		this.exclusoes = exclusoes;
	}

	public Uf getUf() {
		return uf;
	}

	public void setUf(Uf uf) {
		this.uf = uf;
	}

	public String getFinanceira() {
		return financeira;
	}

	public void setFinanceira(String financeira) {
		this.financeira = financeira;
	}

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public Date getData() {
		return data;
	}

	public void setData(Date data) {
		this.data = data;
	}

	public BigInteger getCadastros() {
		return cadastros;
	}

	public void setCadastros(BigInteger cadastros) {
		this.cadastros = cadastros;
	}

	public BigInteger getTransmissoes() {
		return transmissoes;
	}

	public void setTransmissoes(BigInteger transmissoes) {
		this.transmissoes = transmissoes;
	}

	public BigInteger getExclusoes() {
		return exclusoes;
	}

	public void setExclusoes(BigInteger exclusoes) {
		this.exclusoes = exclusoes;
	}

}
