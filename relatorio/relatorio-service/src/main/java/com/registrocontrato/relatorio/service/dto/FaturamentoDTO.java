package com.registrocontrato.relatorio.service.dto;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.enums.SituacaoCobranca;

import java.math.BigDecimal;
import java.util.Date;

public class FaturamentoDTO {

    private Date dataGeracaoCobranca;
    private String documento;
    private String nomeFinanceira;
    private Uf ufCobranca;
    private SituacaoCobranca situacaoCobranca;
    private String numeroNotaFiscal;

    private Long numeroRegistros;
    private BigDecimal valorCredenciada;
    private BigDecimal valorDesconto;
    private BigDecimal receitaBruta;

    private BigDecimal impostoSobreVenda;
    private BigDecimal receitaLiquida;
    private BigDecimal custoServicoPrestado;
    private BigDecimal impostRendaContribuicaoSocial;

    public Date getDataGeracaoCobranca() {
        return dataGeracaoCobranca;
    }

    public void setDataGeracaoCobranca(Date dataGeracaoCobranca) {
        this.dataGeracaoCobranca = dataGeracaoCobranca;
    }

    public String getDocumento() {
        return documento;
    }

    public void setDocumento(String documento) {
        this.documento = documento;
    }

    public String getNomeFinanceira() {
        return nomeFinanceira;
    }

    public void setNomeFinanceira(String nomeFinanceira) {
        this.nomeFinanceira = nomeFinanceira;
    }

    public Uf getUfCobranca() {
        return ufCobranca;
    }

    public void setUfCobranca(Uf ufCobranca) {
        this.ufCobranca = ufCobranca;
    }

    public SituacaoCobranca getSituacaoCobranca() {
        return situacaoCobranca;
    }

    public void setSituacaoCobranca(SituacaoCobranca situacaoCobranca) {
        this.situacaoCobranca = situacaoCobranca;
    }

    public String getNumeroNotaFiscal() {
        return numeroNotaFiscal;
    }

    public void setNumeroNotaFiscal(String numeroNotaFiscal) {
        this.numeroNotaFiscal = numeroNotaFiscal;
    }

    public Long getNumeroRegistros() {
        return numeroRegistros;
    }

    public void setNumeroRegistros(Long numeroRegistros) {
        this.numeroRegistros = numeroRegistros;
    }

    public BigDecimal getValorCredenciada() {
        return valorCredenciada;
    }

    public void setValorCredenciada(BigDecimal valorCredenciada) {
        this.valorCredenciada = valorCredenciada;
    }

    public BigDecimal getValorDesconto() {
        return valorDesconto;
    }

    public void setValorDesconto(BigDecimal valorDesconto) {
        this.valorDesconto = valorDesconto;
    }

    public BigDecimal getReceitaBruta() {
        return receitaBruta;
    }

    public void setReceitaBruta(BigDecimal receitaBruta) {
        this.receitaBruta = receitaBruta;
    }

    public BigDecimal getImpostoSobreVenda() {
        return impostoSobreVenda;
    }

    public void setImpostoSobreVenda(BigDecimal impostoSobreVenda) {
        this.impostoSobreVenda = impostoSobreVenda;
    }

    public BigDecimal getReceitaLiquida() {
        return receitaLiquida;
    }

    public void setReceitaLiquida(BigDecimal receitaLiquida) {
        this.receitaLiquida = receitaLiquida;
    }

    public BigDecimal getCustoServicoPrestado() {
        return custoServicoPrestado;
    }

    public void setCustoServicoPrestado(BigDecimal custoServicoPrestado) {
        this.custoServicoPrestado = custoServicoPrestado;
    }

    public BigDecimal getImpostRendaContribuicaoSocial() {
        return impostRendaContribuicaoSocial;
    }

    public void setImpostRendaContribuicaoSocial(BigDecimal impostRendaContribuicaoSocial) {
        this.impostRendaContribuicaoSocial = impostRendaContribuicaoSocial;
    }
}
