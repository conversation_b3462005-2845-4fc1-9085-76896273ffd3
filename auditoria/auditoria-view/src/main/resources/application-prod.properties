#datasource 
spring.datasource.jndi-name=java:jboss/datasources/AuditoriaDS

#jpa
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=true
spring.jpa.format-sql=true

#cas
cas.server=https://placecon.com.br/auth
cas.local.login=https://placecon.com.br/auditoria/login/cas
cas.url.ambiente.boasvindas=https://placecon.com.br/

#jsf
jsf.PROJECT_STAGE=Production

server.servlet.session.timeout=30

#log
logging.level=INFO
logging.config=classpath:/log4j2.xml

file.dir=/storage/contratos/
file.dir.image-flow=/image-flow/

#boleto
boleto.url=https://sandbox.boletocloud.com/api
boleto.token=api-key_TiW3A-rbaoQo2PJgn-CxGCR3aVxe4gN5htnQmrtbNQM=
boleto.token.conta.itau=api-key_ibUAA5f_k_Rw7SRgvU705hZqhcZEZhFi4KLPsfCaaWo=
boleto.versao=v1
boleto.conta=24255-7
boleto.agencia=6630
boleto.banco=341
boleto.carteira=109
boleto.beneficiario.nome=PLACE TECNOLOGIA E INOVACAO S/A
boleto.beneficiario.cprf=06.032.507/0001-03
boleto.beneficiario.endereco.cep=04542-001
boleto.beneficiario.endereco.uf=SP
boleto.beneficiario.endereco.localidade=Sao Paulo
boleto.beneficiario.endereco.bairro=Itaim Bibi
boleto.beneficiario.endereco.logradouro=Rua Leopoldo Couto de Magalhaes Junior
boleto.beneficiario.endereco.numero=1098
boleto.beneficiario.endereco.complemento=Sala 91
boleto.titulo=CC
boleto.instrucao1=Nao receber, ambiente de desenvolvimento
boleto.instrucao2=
boleto.instrucao3=

# service DETRAN SP
detran.sp.default.uri=http://*************:80/homol/eaigever/SircofService
detran.sp.context.path=com.registrocontrato.registro.service.detran.sp.client
detran.sp.cnpj=06032507000103
detran.sp.senha=PLACE@


qrcode.validate.url=http://localhost:8080/registro/public/validate.xhtml
sign.url.return=http://localhost:8080

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=prod
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

#upload de arquivos
upload.contrato.registro.layout=010
upload.contrato.tipo_operacao.registro_contrato=22
upload.contrato.tipo_operacao.registro_aditivo_contrato=17

# timeout detran sp
detran.sp.readTimeout=10000
detran.sp.connectionTimeout=10000

sistema=AUDITORIA

placecon.cripto.key=/opt/chaves/prod.key
