#datasource 
spring.datasource.jndi-name=java:jboss/datasources/AuditoriaDS

#jpa
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.id.new_generator_mappings=true
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true
spring.jpa.show-sql=true
spring.jpa.format-sql=true

#cas
#cas.server=http://localhost:8080/auth
cas.server=${CAS_AMBIENTE}/auth
cas.local.login=${CAS_AMBIENTE}/auditoria/login/cas
cas.url.ambiente.boasvindas=https://homol.placecon.com.br/

#jsf
jsf.PROJECT_STAGE=Development

server.servlet.session.timeout=30

#log
logging.level=INFO
logging.config=classpath:/log4j2.xml

file.dir=/data/contratos/

# email
spring.mail.mailgun.domain=placecon.com.br
spring.mail.mailgun.ws-auth=Basic ************************************************************************
spring.mail.contato=<EMAIL>
spring.mail.ambiente=desenv
spring.mail.destinarios.baixa=<EMAIL>
spring.mail.destinarios.suporte=<EMAIL>

#boleto
boleto.url=https://app.boletocloud.com/api
boleto.token=api-key_pPEocFcZcgdIOYZcNgmfzNYNiansbo_MzFG6UoAgaMc=
boleto.token.conta.itau=api-key_ibUAA5f_k_Rw7SRgvU705hZqhcZEZhFi4KLPsfCaaWo=
boleto.versao=v1
boleto.conta=24255-7
boleto.agencia=6630
boleto.banco=341
boleto.carteira=109
boleto.beneficiario.nome=PLACE TECNOLOGIA E INOVACAO S/A
boleto.beneficiario.cprf=06.032.507/0001-03
boleto.beneficiario.endereco.cep=04542-001
boleto.beneficiario.endereco.uf=SP
boleto.beneficiario.endereco.localidade=Sao Paulo
boleto.beneficiario.endereco.bairro=Itaim Bibi
boleto.beneficiario.endereco.logradouro=Rua Leopoldo Couto de Magalhaes Junior
boleto.beneficiario.endereco.numero=1098
boleto.beneficiario.endereco.complemento=Sala 91
boleto.titulo=CC
boleto.instrucao1=
boleto.instrucao2=
boleto.instrucao3=

sistema=AUDITORIA
