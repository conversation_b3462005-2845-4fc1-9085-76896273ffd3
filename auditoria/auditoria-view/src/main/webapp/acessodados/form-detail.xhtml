<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
	
		<f:metadata>
			<f:viewParam id="id" name="id" value="#{acessoDadosBean.idToEdit}"/>
			<f:viewAction action="#{acessoDadosBean.loadDetails()}"/>	
		</f:metadata> 
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Acesso aos Dados</h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Visualizar</h6>
									<h:messages id="messages" warnClass="alert alert-warning alert-dismissable" infoClass="alert alert-success alert-dismissable" errorClass="alert alert-danger alert-dismissable" />
									<form jsf:id="form" jsf:prependId="false">
										<div class="row">
											<div class="col-lg-12">
												<div class="form-group form-group form-group-default">
													<label>URL de Acesso</label>
													<textarea disabled="disabled" class="form-control" jsf:value="#{acessoDadosBean.entity.acesso}"></textarea>
												</div>
											</div>	
										</div>
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group form-group-default form-group-default-select2">
													<label>Usuário</label>
													<input class="form-control" type="text" disabled="disabled" jsf:value="#{acessoDadosBean.usuario.cpf} - #{acessoDadosBean.usuario.nome}"/>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group form-group-default form-group-default-select2">
													<label>Perfil</label>
													<input class="form-control" type="text" disabled="disabled" jsf:value="#{acessoDadosBean.usuario.perfil.descricao}"/>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group form-group-default">
													<label>Sistema</label>
													<input type="text" class="form-control" disabled="disabled" jsf:value="#{acessoDadosBean.entity.sistema}"/>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group form-group-default">
													<label>Data</label>
													<input type="text" class="form-control" disabled="disabled" jsf:value="#{acessoDadosBean.entity.dataFormatada}"/>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group form-group-default">
													<label>IP</label>
													<input type="text" class="form-control" disabled="disabled" jsf:value="#{acessoDadosBean.entity.ip}"/>
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/acessodados/list.xhtml" class="btn btn-default">Voltar</a>
												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
	
</ui:composition>