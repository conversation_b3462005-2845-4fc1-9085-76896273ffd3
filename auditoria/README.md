#install elasticsearch
wget https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-6.2.2.zip
unzip elasticsearch-6.2.2.zip
./bin/elasticsearch-plugin install x-pack
./bin/elasticsearch
cd elasticsearch-6.2.2
./bin/x-pack/setup-passwords interactive
http://locahost:92000

#install kibana
wget https://artifacts.elastic.co/downloads/kibana/kibana-6.2.2-darwin-x86_64.tar.gz
tar -xf kibana-6.2.2-darwin-x86_64.tar.gz
cd kibana-6.2.2-darwin-x86_64
./bin/kibana-plugin install x-pack
vim config/kibana.yml
update user and password at elasticsearch.username and elasticsearch.password
./bin/kibana


#install filebeat
wget https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-6.2.2-darwin-x86_64.tar.gz
tar xf filebeat-6.2.2-darwin-x86_64.tar.gz
cd filebeat-6.2.2
edit filebeat.yml
./filebeat -e -c filebeat.yml


filebeat.prospectors:
- type: log
  enabled: true
  paths:
    - /etc/registro-ws/logs/detran-xx.log

  ### Multiline options
  multiline.pattern: "Audit trail record BEGIN"
  multiline.negate: true
  multiline.match: after

output.elasticsearch:
  hosts: ["localhost:9200"]
  username: "elastic"
  password: "elastic"
