package com.registrocontrato.auditoria.repository;

import java.util.Date;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.registrocontrato.infra.entity.AcessoDados;
import com.registrocontrato.infra.service.BaseRepository;

public interface AcessoDadosRepository extends BaseRepository<AcessoDados> {

	@Query("select count(distinct a.usuario) from AcessoDados a where a.data >= :data")
	Long countByDataBeforeGroupByUsuario(@Param("data")Date data);

}
