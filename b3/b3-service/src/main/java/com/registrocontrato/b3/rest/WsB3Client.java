package com.registrocontrato.b3.rest;

import java.io.File;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.net.ssl.SSLContext;

import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.registrocontrato.b3.dto.ProcessoListRspget;
import com.registrocontrato.b3.dto.ProcessoListRspgetProcessoList;
import com.registrocontrato.b3.dto.ProcessoListRspgetUfList;
import com.registrocontrato.b3.dto.ProcessoRegistroReqpost;
import com.registrocontrato.b3.dto.ProcessoRegistroRsppost;
import com.registrocontrato.b3.dto.ProcessoRspget;
import com.registrocontrato.b3.dto.ProcessoRspgetControle;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;

@Component
public class WsB3Client {

	@Value("${b3.uri:null}")
	private String URI;

	@Value("${b3.cert.keystore:null}")
	private String CERT_KEYSTORE;

	@Value("${b3.cert.truststore:null}")
	private String CERT_TRUSTSTORE;

	@Value("${b3.cert.secret:null}")
	private String CERT_SECRET;

	@Value("${b3.api.key:null}")
	private String API_KEY;

	@Value("${b3.api.secret:null}")
	private String API_SECRET;

	@Value("${b3.connectionTimeout:30000}")
	private Integer connectionTimeout;

	public List<Long> listarProcessos(Uf uf, Date dataInicio, Date dataFim) throws ServiceException {
		try {
			RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());

			String method = "GET";
			String contentMD5 = "";
			String contentType = "";
			String expires = "";
			String inicio = PlaceconUtil.getDataFormatada(dataInicio, "yyyyMMddHHmmss");
			String fim = PlaceconUtil.getDataFormatada(dataFim, "yyyyMMddHHmmss");
			String path = "/contratos/api/v1.0/Processo/List/" + uf + "/" + inicio + "/" + fim;
			String LF = "\n";

			String canonical = method + LF + contentMD5 + LF + contentType + LF + expires + LF + path;

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.set("Authorization",
					"AWS " + API_KEY + ":" + Signature.calculateRFC2104HMAC(canonical, API_SECRET));

			ResponseEntity<ProcessoListRspget> list = restTemplate.exchange(URI + path, HttpMethod.GET,
					new HttpEntity<>(headers), ProcessoListRspget.class);
			List<Long> result = new ArrayList<Long>();
			if (list.getStatusCode() == HttpStatus.OK) {
				ProcessoRspgetControle controle = list.getBody().getControle();
				if (controle.getCode() == 1001) {
					for (ProcessoListRspgetUfList p : list.getBody().getUfList()) {
						for (ProcessoListRspgetProcessoList l : p.getProcessoList()) {
							result.add(l.getIdProcesso());
						}
					}
				} else {
					throw new ServiceException(controle.getCode() + " - " + controle.getDescription());
				}
			}
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			throw new ServiceException(e.getMessage());
		}
	}

	public ProcessoRspget findContrato(Long id, Uf uf) throws ServiceException {
		try {
			RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());

			String method = "GET";
			String contentMD5 = "";
			String contentType = "";
			String expires = "";
			String path = "/contratos/api/v1.0/Processo/" + uf + "/" + id;
			String LF = "\n";

			String canonical = method + LF + contentMD5 + LF + contentType + LF + expires + LF + path;

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.set("Authorization",
					"AWS " + API_KEY + ":" + Signature.calculateRFC2104HMAC(canonical, API_SECRET));

			ResponseEntity<ProcessoRspget> retorno = restTemplate.exchange(URI + path, HttpMethod.GET,
					new HttpEntity<>(headers), ProcessoRspget.class);

			return retorno.getBody();
		} catch (Exception e) {
			throw new ServiceException("Não foi possível carregar o processo detalhado. " + e.getMessage());
		}
	}

	public ProcessoRegistroRsppost notificarRetorno(ProcessoRegistroReqpost dto) throws ServiceException {
		try {
			RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());

			String method = "POST";
			String contentMD5 = "";
			String contentType = "application/json";
			String expires = "";
			String path = "/contratos/api/v1.0/Processo/Registro";
			String LF = "\n";

			String canonical = method + LF + contentMD5 + LF + contentType + LF + expires + LF + path;

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.set("Authorization",
					"AWS " + API_KEY + ":" + Signature.calculateRFC2104HMAC(canonical, API_SECRET));

			ResponseEntity<ProcessoRegistroRsppost> retorno = restTemplate.exchange(URI + path, HttpMethod.POST,
					new HttpEntity<>(dto, headers), ProcessoRegistroRsppost.class);

			return retorno.getBody();
		} catch (Exception e) {
			throw new ServiceException("Não foi possível notificar a B3. " + e.getMessage());
		}
	}

	private ClientHttpRequestFactory getClientHttpRequestFactory() throws Exception {
		File certKeyStore = new ClassPathResource(CERT_KEYSTORE).getFile();

		TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

		SSLContext sslContext = SSLContextBuilder.create()
				.loadKeyMaterial(certKeyStore, CERT_SECRET.toCharArray(), CERT_SECRET.toCharArray())
				.loadTrustMaterial(acceptingTrustStrategy).build();

		HttpClient client = HttpClients.custom().setSSLContext(sslContext).build();

		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(client);
		requestFactory.setConnectTimeout(connectionTimeout);
		requestFactory.setReadTimeout(connectionTimeout);
		return requestFactory;
	}

}
