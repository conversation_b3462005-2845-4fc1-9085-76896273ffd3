package com.registrocontrato.b3.task;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Component;

import com.registrocontrato.infra.entity.Uf;

@Component
public class ProcessoTask implements Serializable {
    private static final long serialVersionUID = 1L;

//    private static final Logger log = LoggerFactory.getLogger(ProcessoTask.class);
//
//    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");

//    private static final String TIME_ZONE = "America/Fortaleza";

//    @Autowired
//    private TaskB3Service taskB3Service;
//
//    @Autowired
//    private ProcessoB3Repository processoB3Repository;
//
//    @Autowired
//    private ProcessoB3Service processoB3Service;

    public static final List<Uf> UFS_INTEGRA = Arrays.asList(Uf.SP, Uf.PI, Uf.RR, Uf.MG, Uf.SC, Uf.PE, Uf.BA, Uf.AC);

//    @Scheduled(cron = "0 0 7 ? * *", zone = TIME_ZONE)
//    public void carregarProcessoUltimoDia(){
//        if(taskB3Service.isRunning(CurrentTaskB3.CARREGAR_PROCESSOSULTIMO_DIA) == Boolean.TRUE){
//            Date dataInicio = PlaceconUtil.minDateTime(new Date());
//            dataInicio = DateUtils.addDays(dataInicio, -1);
//            Date dataFim = PlaceconUtil.maxDateTime(new Date());
//            dataFim = DateUtils.addDays(dataFim, -1);
//
//            ProcessoB3 entity = new ProcessoB3();
//            entity.setDataInicio(dataInicio);
//            entity.setDataFim(dataFim);
//
//            carregarProcessos(dataInicio, dataFim, entity);
//        }
//    }

//    @Scheduled(cron = "* 0/20 * ? * *", zone = TIME_ZONE)
//    public void carregarProcesso20min(){
//        if(taskB3Service.isRunning(CurrentTaskB3.CARREGAR_PROCESSOS_20MIN) == Boolean.TRUE ){
//            Date dataInicio = PlaceconUtil.minDateTime(new Date());
//            Date dataFim = DateUtils.truncate(new Date(), Calendar.SECOND);
//
//            ProcessoB3 entity = new ProcessoB3();
//            entity.setDataInicio(dataInicio);
//            entity.setDataFim(dataFim);
//
//            carregarProcessos(dataInicio, dataFim, entity);
//        }
//    }

//    @Scheduled(cron = "1/10 * * ? * * ", zone = TIME_ZONE)
//    public void notificarErroB310seg(){
//        if(taskB3Service.isRunning(CurrentTaskB3.NOTIFICAR_ERROB3) == Boolean.TRUE ){
//            ProcessoB3 processo = processoB3Repository.findTop1BySituacaoProcessoAndCodigoRetornoB3IsNotNull(SituacaoProcesso.CARREGADO);
//            if(processo != null) {
//                log.info("Notificar Processo com Erro B3 {} at {}", processo.getIdProcesso(), dateFormat.format(new Date()));
//                try {
//                    processoB3Service.notifyErroProcesso(processo.getId());
//                } catch (ServiceException e) {
//                    log.error("Notificar Processo com Erro B3 {} at {} \n\n", e.getMessage());
//                }
//            }
//        }
//    }

//    @Scheduled(cron = "1/10 * * ? * * ", zone = TIME_ZONE)
//    public void notificarSucessoB310seg(){
//        if(taskB3Service.isRunning(CurrentTaskB3.NOTIFICAR_SUCESSOB3) == Boolean.TRUE ){
//            ProcessoB3 processo = processoB3Repository.findTop1BySituacaoProcessoAndSituacaoProcessoDETRAN(SituacaoProcesso.TRANSMITIDO, SituacaoProcessoDETRAN.SUCESSO_DETRAN);
//            if(processo != null) {
//                log.info("Notificar Sucesso B3 {} at {}", processo.getIdProcesso(), dateFormat.format(new Date()));
//                try {
//                    processoB3Service.notifySuccessProcesso(processo.getId());
//                } catch (ServiceException e) {
//                    log.error("Notificar Sucesso B3 {} at {} \n\n", e.getMessage());
//                }
//            }
//        }
//    }

//    @Scheduled(cron = "1/10 * * ? * * ", zone = TIME_ZONE)
//    public void enviarProcessoParaDETRAN10seg(){
//        if(taskB3Service.isRunning(CurrentTaskB3.ENVIAR_PROCESSO_DETRAN) == Boolean.TRUE){
//            ProcessoB3 processo = processoB3Repository.findTop1TransmissaoDETRAN();
//            if(processo != null) {
//                log.info("Enviar processo para DETRAN {} at {}", processo.getIdProcesso(), dateFormat.format(new Date()));
//                try {
//                    processoB3Service.sendProcesso(processo.getId());
//                } catch (ServiceException e) {
//                    processoB3Service.loadProcesso(processo.getId());
//                    log.error("Enviar processo para DETRAN", e.getMessage());
//                }
//            }
//        }
//    }

//    private void carregarProcessos(Date dataInicio, Date dataFim, ProcessoB3 entity) {
//        for(Uf u : UFS_INTEGRA) {
//            try {
//                Thread.sleep(10000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//
//            log.info("Carregar Processos {} B3 {} a {} at {}", u, PlaceconUtil.formatarDataHora(dataInicio), PlaceconUtil.formatarDataHora(dataFim), dateFormat.format(new Date()));
//            entity.setEstado(u);
//            try {
//                processoB3Service.loadProcessos(entity);
//            } catch (ServiceException e) {
//                log.error("Erro ao Carregar Processos B3 \n\n", e.getMessage());
//            }
//        }
//    }
}
