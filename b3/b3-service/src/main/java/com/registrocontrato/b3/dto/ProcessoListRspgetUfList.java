package com.registrocontrato.b3.dto;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.registrocontrato.infra.entity.Uf;

public class ProcessoListRspgetUfList {

	@JsonProperty("uf")
	private Uf uf = null;

	@JsonProperty("ProcessoList")
	private List<ProcessoListRspgetProcessoList> processoList = new ArrayList<ProcessoListRspgetProcessoList>(0);

	public Uf getUf() {
		return uf;
	}

	public void setUf(Uf uf) {
		this.uf = uf;
	}

	public ProcessoListRspgetUfList uf(Uf uf) {
		this.uf = uf;
		return this;
	}

	public List<ProcessoListRspgetProcessoList> getProcessoList() {
		return processoList;
	}

	public void setProcessoList(List<ProcessoListRspgetProcessoList> processoList) {
		this.processoList = processoList;
	}

	public ProcessoListRspgetUfList processoList(List<ProcessoListRspgetProcessoList> processoList) {
		this.processoList = processoList;
		return this;
	}

	public ProcessoListRspgetUfList addProcessoListItem(ProcessoListRspgetProcessoList processoListItem) {
		this.processoList.add(processoListItem);
		return this;
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("class ProcessoListRspgetUfList {\n");
		sb.append("    uf: ").append(toIndentedString(uf)).append("\n");
		sb.append("    processoList: ").append(toIndentedString(processoList)).append("\n");
		sb.append("}");
		return sb.toString();
	}

	/**
	 * Convert the given object to string with each line indented by 4 spaces
	 * (except the first line).
	 */
	private static String toIndentedString(java.lang.Object o) {
		if (o == null) {
			return "null";
		}
		return o.toString().replace("\n", "\n    ");
	}
}
