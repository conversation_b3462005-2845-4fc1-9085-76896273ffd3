package com.registrocontrato.b3.repository;

import com.registrocontrato.b3.entity.*;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.registrocontrato.infra.service.BaseRepository;

@Repository
public interface ProcessoB3Repository extends BaseRepository<ProcessoB3> {

	ProcessoB3 findByIdProcesso(Long idProcesso);

	@Query("select r from ProcessoB3 r where r.id = (select max(p.id) from ProcessoB3 p where p.situacaoProcesso = :situacaoProcesso and p.situacaoProcessoDETRAN = :situacaoProcessoDETRAN and p.tentativasEnvio < 10) ")
	ProcessoB3 findTop1BySituacaoProcessoAndSituacaoProcessoDETRAN(
			@Param("situacaoProcesso") SituacaoProcesso situacaoProcesso,
			@Param("situacaoProcessoDETRAN") SituacaoProcessoDETRAN situacaoProcessoDETRAN);

	@Query("select r from ProcessoB3 r where r.id = (select max(p.id) from ProcessoB3 p where p.situacaoProcesso = :situacaoProcesso and p.retornoB3 is not null and p.tentativasEnvio < 5) ")
	ProcessoB3 findTop1BySituacaoProcessoAndCodigoRetornoB3IsNotNull(
			@Param("situacaoProcesso") SituacaoProcesso situacaoProcesso);

	@Query("select r from ProcessoB3 r where r.id = (select max(p.id) from ProcessoB3 p where p.situacaoProcesso not in ('TRANSMITIDO', 'ENCERRADO') and p.situacaoProcessoDETRAN is null and p.tentativasEnvio < 5) ")
	ProcessoB3 findTop1TransmissaoDETRAN();



}
