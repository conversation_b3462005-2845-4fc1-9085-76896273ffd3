package com.registrocontrato.b3.entity;

import com.registrocontrato.infra.entity.BaseEntity;

import javax.persistence.*;

@Entity
@Table(schema = "b3")
public class ErroPlaceconErroSendB3 extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long mensagemId;

    @Enumerated(EnumType.STRING)
    private RetornoB3 retornoB3;

    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public Long getMensagemId() {
        return mensagemId;
    }

    public void setMensagemId(Long mensagemId) {
        this.mensagemId = mensagemId;
    }

    public RetornoB3 getRetornoB3() {
        return retornoB3;
    }

    public void setRetornoB3(RetornoB3 retornoB3) {
        this.retornoB3 = retornoB3;
    }
}
