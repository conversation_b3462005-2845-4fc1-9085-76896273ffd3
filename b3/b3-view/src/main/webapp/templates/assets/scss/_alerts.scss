/* =============
   Alerts
============= */
.alert {
  position: relative;

  .alert-link {
    font-weight: 600;
  }
}

.alert-dismissable .close, .alert-dismissible .close {
  opacity: 0.9;
}

.alert-icon {
  padding-left: 50px;

  i {
    position: absolute;
    left: 0;
    height: 50px;
    width: 50px;
    text-align: center;
    top: 0;
    line-height: 50px;
    font-size: 22px;
  }
}

.alert-success {
  color: $success;
  background-color: lighten($success,40%);
  border-color: lighten($success,20%);
  .alert-link {
    color: darken($success,10%);
  }
  hr {
    border-top-color: darken($success,10%);
  }
}

.alert-info {
  color: $info;
  background-color: lighten($info,40%);
  border-color: lighten($info,20%);
  .alert-link {
    color: darken($info,10%);
  }
  hr {
    border-top-color: darken($info,10%);
  }
}

.alert-warning {
  color: $warning;
  background-color: lighten($warning, 40%);
  border-color: lighten($warning, 20%);
  .alert-link {
    color: darken($warning, 10%);
  }
  hr {
    border-top-color: darken($warning, 10%);
  }
}

.alert-danger {
  color: $danger;
  background-color: lighten($danger,32%);
  border-color: lighten($danger,20%);
  .alert-link {
    color: darken($danger,10%);
  }
  hr {
    border-top-color: darken($danger,10%);
  }
}

.alert-white {
  background-color: $white !important;
}


// Sweet Alerts 2

.swal2-modal {
  font-family: $font-primary;

  .swal2-title {
    font-size: 28px;
  }
  .swal2-content {
    font-size: 16px;
  }
  .swal2-spacer {
    margin: 10px 0;
  }
  .swal2-file, .swal2-input, .swal2-textarea {
    border: 2px solid $muted;
    font-size: 16px;
    box-shadow: none !important;
  }
}

.swal2-icon.swal2-question {
  color: $custom;
  border-color: $custom;
}

.swal2-icon.swal2-success {
  border-color: $success;

  .line {
    background-color: $success;
  }

  .placeholder {
    border-color: $success;
  }
}

.swal2-icon.swal2-warning {
  color: $warning;
  border-color: $warning;
}

.swal2-icon.swal2-error {
  border-color: $danger;
  .line {
    background-color: $danger;
  }
}
.swal2-modal .swal2-file:focus, .swal2-modal .swal2-input:focus, .swal2-modal .swal2-textarea:focus {
    outline: 0;
    border: 2px solid $custom;
}


