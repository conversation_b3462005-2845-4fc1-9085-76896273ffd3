/* =============
   Form
============= */
label {
  font-weight: 500;
  font-family: $font-secondary;
}

textarea.form-control {
  min-height: 90px;
}
.form-control {
  border: 1px solid darken($light,10%);
  border-radius: 4px;
  max-width: 100%;
  height: 38px;
  -webkit-box-shadow: none;
  box-shadow: none;
  font-size: 14px;
}
.form-control:focus {
  border: 1px solid #aaaaaa;
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: 0 !important;
}

select[multiple] {
  height: auto;
}

.input-lg {
  height: 46px;
  font-size: 16px;
  line-height: 1.3333333;
  border-radius: 4px;
}
.input-sm {
  height: 30px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

.form-horizontal .form-group {
  margin-left: -10px;
  margin-right: -10px;
}

.has-success .checkbox, .has-success .checkbox-inline,
.has-success .control-label, .has-success .help-block,
.has-success .radio, .has-success .radio-inline,
.has-success.checkbox label, .has-success.checkbox-inline label,
.has-success.radio label, .has-success.radio-inline label,
.has-success .form-control-feedback{
  color: $success;
}

.has-warning .checkbox, .has-warning .checkbox-inline,
.has-warning .control-label, .has-warning .help-block,
.has-warning .radio, .has-warning .radio-inline,
.has-warning.checkbox label, .has-warning.checkbox-inline label,
.has-warning.radio label, .has-warning.radio-inline label,
.has-warning .form-control-feedback{
  color: $warning;
}
.has-error .checkbox, .has-error .checkbox-inline,
.has-error .control-label, .has-error .help-block,
.has-error .radio, .has-error .radio-inline,
.has-error.checkbox label, .has-error.checkbox-inline label,
.has-error.radio label, .has-error.radio-inline label,
.has-error .form-control-feedback{
  color: $danger;
}
.has-success .form-control {
  border-color: $success;
  box-shadow: none !important;
}
.has-warning .form-control {
  border-color: $warning;
  box-shadow: none !important;
}
.has-error .form-control {
  border-color: $danger;
  box-shadow: none !important;
}
.input-group-addon {
  border-radius: 2px;
  border: 1px solid #eeeeee;
}