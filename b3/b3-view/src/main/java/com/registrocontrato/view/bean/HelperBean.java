package com.registrocontrato.view.bean;

import java.io.Serializable;

import org.springframework.stereotype.Controller;
import org.springframework.web.context.annotation.ApplicationScope;

import com.registrocontrato.b3.entity.RetornoB3;
import com.registrocontrato.b3.entity.SituacaoProcesso;
import com.registrocontrato.b3.entity.SituacaoProcessoB3;
import com.registrocontrato.b3.entity.SituacaoProcessoDETRAN;
import com.registrocontrato.b3.entity.CurrentTaskB3;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;

@Controller
@ApplicationScope
public class HelperBean implements Serializable {

	private static final long serialVersionUID = 1L;

	public SimNao[] getSimNao() {
		return SimNao.values();
	}

	public Uf[] getUfs() {
		return new Uf[] { Uf.SP, Uf.PI, Uf.RR, Uf.MG, Uf.SC, Uf.PE, Uf.BA, Uf.AC };
	}

	public SituacaoProcessoDETRAN[] getSituacoesProcessoDETRAN() {
		return SituacaoProcessoDETRAN.values();
	}

	public SituacaoProcessoB3[] getSituacoesProcessoB3() {
		return SituacaoProcessoB3.values();
	}

	public SituacaoProcesso[] getSituacoesProcesso() {
		return SituacaoProcesso.values();
	}

	public RetornoB3[] getRetornosB3() {
		return RetornoB3.values();
	}
	
	public CurrentTaskB3[] getTasks() {
		return CurrentTaskB3.values();
	}

}
