package com.registrocontrato.view.bean;

import com.registrocontrato.b3.service.TaskB3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

import com.registrocontrato.b3.dto.ProcessoB3DTO;
import com.registrocontrato.b3.entity.ProcessoB3;
import com.registrocontrato.b3.entity.SituacaoProcessoB3;
import com.registrocontrato.b3.entity.SituacaoProcessoDETRAN;
import com.registrocontrato.b3.entity.CurrentTaskB3;
import com.registrocontrato.b3.service.ProcessoB3Service;
import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;

@Controller
@ViewScope
public class ProcessoB3Bean extends BaseCrud<ProcessoB3, ProcessoB3DTO> {

	private static final long serialVersionUID = 1L;
	
	@Value("${processo.url.contrato}")
	private String urlContrato;

	@Autowired
	private ProcessoB3Service processoB3Service;
	
	@Autowired
	private TaskB3Service taskB3Service;
	
	public String loadProcessos() {
		try {
			processoB3Service.loadProcessos(entity);
			addMessageInfo("Processos carregados com sucesso.");
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		}
		return "/processos/list.xhtml?faces-redirect=true";
	}
	
	public String sendProcesso(Long id) {
		try {
			processoB3Service.sendProcesso(id);
			addMessageInfo("Processo enviado com sucesso.");
		} catch (Exception e) {
			logger.error("Erro no envio de contrato.", e);
			addMessageError(e.getMessage());
			processoB3Service.loadProcesso(id);
		}
		return getRetorno();
	}
	
	public String notifySuccessProcesso(Long id) {
		try {
			ProcessoB3 processoB3 = processoB3Service.notifySuccessProcesso(id);
			if(processoB3.getSituacaoProcessoB3() == SituacaoProcessoB3.SUCESSO_B3) {
				addMessageInfo("Notificação realizada com sucesso.");
			} else {
				addMessageError("Erro ao notificar a B3 sobre sucesso do processo.");
			}
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		}
		return getRetorno();
	}
	
	public String notifyErroProcesso(Long id) {
		try {
			processoB3Service.notifyErroProcesso(id);
			addMessageInfo("Notificação realizada com sucesso.");
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		}
		return getRetorno();
	}

	public String verifyProcesso(Long id) throws Exception {
		try {
			ProcessoB3 processoB3 = processoB3Service.notifySuccessProcesso(id);
			if(processoB3.getSituacaoProcessoDETRAN() == SituacaoProcessoDETRAN.ERRO_DETRAN) {
				getExternalContext().redirect(urlContrato + processoB3.getIdContrato());
			}
			if(processoB3.getSituacaoProcessoB3() == SituacaoProcessoB3.SUCESSO_B3) {
				addMessageInfo("Notificação realizada com sucesso.");
			} else {
				addMessageError("Erro ao notificar a B3 sobre sucesso do processo.");
			}
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		}
		return getRetorno();
	}
	
	public String linkProcesso(Long id) {
		try {
			processoB3Service.linkProcesso(id);
			addMessageInfo("Vínculo realizado com sucesso.");
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		}
		return getRetorno();
	}
	
	private String getRetorno() {
		if(getFilter().getSituacaoProcesso() == null) {
			return "/processos/list.xhtml?faces-redirect=true";
		}
		return "/processos/list.xhtml?faces-redirect=true&status="+getFilter().getSituacaoProcesso();
	}
	
//	public void modificarTask(CurrentTaskB3 currentTaskB3,Boolean ativo) {
//		taskB3Service.updateTask(ativo,currentTaskB3);
//	}

	public Boolean isAtivo(CurrentTaskB3 currentTaskB3) {
		return taskB3Service.isRunning(currentTaskB3);

	}
	

	@Override
	public BaseService<ProcessoB3, ProcessoB3DTO> getService() {
		return processoB3Service;
	}

}
