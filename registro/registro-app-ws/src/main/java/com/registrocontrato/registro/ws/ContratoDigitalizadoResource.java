package com.registrocontrato.registro.ws;

import com.registrocontrato.commons.ws.dto.ContratoDigitalizadoDTO;
import com.registrocontrato.commons.ws.mapper.ContratoDigitalizadoMapper;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.ContratoDigitalizado;
import com.registrocontrato.registro.service.ContratoDigitalizadoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/v1/digitalizado/")
public class ContratoDigitalizadoResource {

    private static final String CONSULTAR_CONTRATOS_SEM_ENVIO = "Consultar contratos digitalizados sem envio por Uf e Financeira";
    private static final String ATUALIZAR_CONTRATO_POR_REFERENCIA_ARQUIVO = "Atualizar contrato digitalizado por arquivo de referencia";
    private static final String BUSCAR_ARQUIVO_BASE64 = "Consultar contrato digitalizado em base64";

    @Autowired
    private ContratoDigitalizadoService service;

    @Autowired
    private ContratoDigitalizadoMapper contratoDigitalizadoMapper;

    private final String PENDENCIAS_FINANCEIRA = "Consultar documento de financeiras com contratos digitalizados pendentes";

    @GetMapping(value = "financeiras/pendente/{uf}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<String> getFinanceirasContratoNaoEnviado(@PathVariable(name = "uf", required = true) Uf estado) {
        return service.getFinanceirasPendentesByEstado(estado);
    }

    @GetMapping(value = "consultar/semenvio/{uf}/{docFinanceira}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<ContratoDigitalizadoDTO> getContratosSemEnvio(
            @PathVariable(name = "uf") Uf uf,
            @PathVariable(value = "docFinanceira") String docFinanceira) {
        List<ContratoDigitalizadoDTO> contratosDTO = new ArrayList<>();
        List<ContratoDigitalizado> contratos = service.getContratosSemEnvioPorFinanceiraEUf(docFinanceira, uf);
        contratos.forEach(contrato -> {
            ContratoDigitalizadoDTO dto = contratoDigitalizadoMapper.convertToDTO(contrato);
            contratosDTO.add(dto);
        });
        return contratosDTO;
    }

    @PatchMapping(value = "notificar/enviado/{refArquivo:.+}/{id}", produces = MediaType.ALL_VALUE)
    public ResponseEntity atualizarContrato(
            @PathVariable(name = "refArquivo") String refArquivo, @PathVariable(name = "id") Long id) {
        ContratoDigitalizado contratoDigitalizado = service.getContratoPorReferenciaArquivoEId(refArquivo, id);
        if (Objects.isNull(contratoDigitalizado)) {
            return ResponseEntity.notFound().build();
        }
        contratoDigitalizado.setEnviado(true);
        contratoDigitalizado.setDataProcessamento(new Date());
        service.save(contratoDigitalizado);
        return ResponseEntity.ok().build();
    }

    @GetMapping(value = "arquivo/base64/{refArquivo}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ResponseEntity<String> base64ArquivoContrato(@PathVariable(name = "refArquivo") String referenciaArquivo) {
        Optional<String> optionalResposta = service.codificaArquivoBase64(referenciaArquivo);
        if (optionalResposta.isPresent())
            return new ResponseEntity<>(optionalResposta.get(), HttpStatus.OK);
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

}
