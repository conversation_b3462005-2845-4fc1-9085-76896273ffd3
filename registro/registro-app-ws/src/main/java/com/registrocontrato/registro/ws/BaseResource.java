package com.registrocontrato.registro.ws;

import java.io.Serializable;

import javax.ws.rs.core.Response;


public abstract class BaseResource implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String TRANSAÇAO_EXECUTADA_COM_SUCESSO = "Transaçao executada com sucesso.";
    
    protected Response montagemRetorno(String mensagem, Response.Status status) {
        return Response.status(status).entity(mensagem).build();
    }
    
    protected Response retornoSucesso(String mensagem) {
        return montagemRetorno(mensagem, Response.Status.OK);
    }

    protected Response retornoErro(String mensagem) {
        return montagemRetorno(mensagem, Response.Status.BAD_REQUEST);
    }
    
    protected Response retornoSucessoPadrao() {
        return retornoSucesso(TRANSAÇAO_EXECUTADA_COM_SUCESSO);
    }
}
