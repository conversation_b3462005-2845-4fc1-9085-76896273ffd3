package com.registrocontrato.registro.ws;

import com.registrocontrato.infra.entity.StatusProcessamento;
import com.registrocontrato.registro.entity.RemessaChassi;
import com.registrocontrato.registro.service.RemessaChassiService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/remessa")
public class RemessaResource {

    private final RemessaChassiService remessaChassiService;

    public RemessaResource(RemessaChassiService remessaChassiService) {
        this.remessaChassiService = remessaChassiService;
    }

    @PutMapping("/alterarstatus/{operacao}/{idArquivo}")
    public ResponseEntity<?> alterarStatusRemessaChassi(@PathVariable(name = "operacao", required = true) int operacao,
                                                        @PathVariable(name = "idArquivo", required = true) Long idArquivo) {

        RemessaChassi remessaChassi = remessaChassiService.findOne(idArquivo);

        remessaChassiService.finalizarRemessa(remessaChassi, StatusProcessamento.values()[operacao]);

        return ResponseEntity.ok("Status alterado com sucesso");

    }
}
