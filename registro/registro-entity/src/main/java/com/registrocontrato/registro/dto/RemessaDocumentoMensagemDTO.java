package com.registrocontrato.registro.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.registrocontrato.infra.entity.SimNao;

import java.io.Serializable;

public class RemessaDocumentoMensagemDTO implements Serializable {

    private static final long serialVersionUID = 3620871551978172746L;

    @JsonProperty
    private String chassi;

    private String mensagem;

    private SimNao sucesso;

    public RemessaDocumentoMensagemDTO() {

    }

    public RemessaDocumentoMensagemDTO(String chassi, String mensagem, String sucesso) {
        this.chassi = chassi;
        this.mensagem = mensagem;
        this.sucesso = SimNao.valueOf(sucesso);
    }

    public RemessaDocumentoMensagemDTO(String chassi, String mensagem) {
        this.chassi = chassi;
        this.mensagem = mensagem;
        this.sucesso = SimNao.S;
    }

    public RemessaDocumentoMensagemDTO(String mensagem) {
        this("", mensagem);
    }

    public String imprimimeOcorrencia() {
        return this.chassi + ": " + this.mensagem;
    }

    public String getChassi() {
        return chassi;
    }

    public void setChassi(String chassi) {
        this.chassi = chassi;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public SimNao getSucesso() {
        return sucesso;
    }

    public void setSucesso(SimNao sucesso) {
        this.sucesso = sucesso;
    }

}
