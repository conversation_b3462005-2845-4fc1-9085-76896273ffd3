package com.registrocontrato.registro.entity;

import java.util.List;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

import com.registrocontrato.infra.entity.BaseEntity;
import com.registrocontrato.infra.entity.SimNao;

@Entity
@Audited
@Table(schema = "registro")
public class Marca extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private String codigoDenatran;

	private String descricao;

	@Enumerated(EnumType.STRING)
	private SimNao ativo;

	@OneToMany(cascade = {}, fetch = FetchType.LAZY, mappedBy = "marca")
	private List<Modelo> modelos;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCodigoDenatran() {
		return codigoDenatran;
	}

	public void setCodigoDenatran(String codigoDenatran) {
		this.codigoDenatran = codigoDenatran;
	}

	public String getDescricao() {
		return descricao;
	}

	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}

	public SimNao getAtivo() {
		return ativo;
	}

	public void setAtivo(SimNao ativo) {
		this.ativo = ativo;
	}

	public List<Modelo> getModelos() {
		return modelos;
	}

	public void setModelos(List<Modelo> modelos) {
		this.modelos = modelos;
	}

}
