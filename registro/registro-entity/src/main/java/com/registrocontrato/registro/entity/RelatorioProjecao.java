package com.registrocontrato.registro.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.registrocontrato.infra.entity.BaseEntity;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;

@Entity
@Table(schema = "registro")
public class RelatorioProjecao extends BaseEntity{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	
	private BigDecimal valorIntegraMais;
	private BigDecimal valorCredenciada;
	private BigDecimal valorDetran;
	private Integer totalContratos;
	private Integer totalRegistros;
	private Date data;
	private Boolean finalSemana;
	@Enumerated(EnumType.STRING)
	private Uf uf;
	private Long idFinanceira;
	@Enumerated(EnumType.STRING)
	private SimNao integraMais;
	private Long totalRegistrosAditivo;

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {
		this.id = id;
		
	}

	public BigDecimal getValorIntegraMais() {
		return valorIntegraMais;
	}

	public void setValorIntegraMais(BigDecimal valorIntegraMais) {
		this.valorIntegraMais = valorIntegraMais;
	}
	
	public BigDecimal getValorCredenciada() {
		return valorCredenciada;
	}
	
	public void setValorCredenciada(BigDecimal valorCredenciada) {
		this.valorCredenciada = valorCredenciada;
	}
	
	public BigDecimal getValorDetran() {
		return valorDetran;
	}
	
	public void setValorDetran(BigDecimal valorDetran) {
		this.valorDetran = valorDetran;
	}

	public Integer getTotalContratos() {
		return totalContratos;
	}

	public void setTotalContratos(Integer totalContratos) {
		this.totalContratos = totalContratos;
	}

	public Integer getTotalRegistros() {
		return totalRegistros;
	}

	public void setTotalRegistros(Integer totalRegistros) {
		this.totalRegistros = totalRegistros;
	}
	
	public Date getData() {
		return data;
	}

	public void setData(Date data) {
		this.data = data;
	}
	
	public Boolean getFinalSemana() {
		return finalSemana;
	}
	
	public void setFinalSemana(Boolean finalSemana) {
		this.finalSemana = finalSemana;
	}
	
	public Long getIdFinanceira() {
		return idFinanceira;
	}
	
	public void setIdFinanceira(Long idFinanceira) {
		this.idFinanceira = idFinanceira;
	}
	
	public Uf getUf() {
		return uf;
	}
	
	public void setUf(Uf uf) {
		this.uf = uf;
	}
	
	public SimNao getIntegraMais() {
		return integraMais;
	}
	
	public void setIntegraMais(SimNao integraMais) {
		this.integraMais = integraMais;
	}
	
	public Long getTotalRegistrosAditivo() {
		return totalRegistrosAditivo;
	}
	
	public void setTotalRegistrosAditivo(Long totalRegistrosAditivo) {
		this.totalRegistrosAditivo = totalRegistrosAditivo;
	}
}
