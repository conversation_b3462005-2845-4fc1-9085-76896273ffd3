package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;

import javax.persistence.*;

@Entity
@Table(schema = "image")
public class Credencial {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String login;

    private String password;

    private String operador;

    @Enumerated(EnumType.STRING)
    private Uf uf;

    @JoinColumn
    @ManyToOne(fetch = FetchType.EAGER)
    private Financeira financeira;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOperador() {
        return operador;
    }

    public void setOperador(String operador) {
        this.operador = operador;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }
}
