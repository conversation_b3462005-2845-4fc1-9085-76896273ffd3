package com.registrocontrato.registro.entity;

import java.util.Calendar;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.registrocontrato.infra.entity.BaseEntity;

@Entity
@Table(schema = "financeiro")
public class ArquivoTokenBaixado extends BaseEntity{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	
	@Column(nullable=false)
	private String token;
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(nullable=false)
	private Date dataProcessamento;
	
	public ArquivoTokenBaixado(){
		
	}
	
	public ArquivoTokenBaixado(String token){
		dataProcessamento = Calendar.getInstance().getTime();
		this.token = token;
	}
	
	public Date getDataProcessamento() {
		return dataProcessamento;
	}
	
	public void setDataProcessamento(Date dataProcessamento) {
		this.dataProcessamento = dataProcessamento;
	}
	
	public String getToken() {
		return token;
	}
	
	public void setToken(String token) {
		this.token = token;
	}

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public void setId(Long id) {

		this.id = id;
	}
}
