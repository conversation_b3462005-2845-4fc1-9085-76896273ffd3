package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.BaseEntity;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


@Entity
@Audited
@Table(schema = "rsng")
public class BilhetagemGravame extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Temporal(TemporalType.DATE)
    private Date dataInicio;

    @Temporal(TemporalType.DATE)
    private Date dataFim;

    @Enumerated(EnumType.STRING)
    private Uf uf;


    private BigDecimal valorCetip;

    private BigDecimal valorFenaseg;

    private BigDecimal valorPlace;

    @ManyToOne
    @JoinColumn(name = "financeira_id")
    private Financeira financeira;

    public BigDecimal getValorPlace() {
        return valorPlace;
    }

    public void setValorPlace(BigDecimal valorPlace) {
        this.valorPlace = valorPlace;
    }

    public BigDecimal getValorCetip() {
        return valorCetip;
    }

    public void setValorCetip(BigDecimal valorCetip) {
        this.valorCetip = valorCetip;
    }

    public BigDecimal getValorFenaseg() {
        return valorFenaseg;
    }

    public void setValorFenaseg(BigDecimal valorFenaseg) {
        this.valorFenaseg = valorFenaseg;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Uf getUf() {
        return uf;
    }

    public void setUf(Uf uf) {
        this.uf = uf;
    }

    public BigDecimal getValorTotal() {
        if (valorCetip == null) {
            valorCetip = BigDecimal.ZERO;
        }
        if (valorFenaseg == null) {
            valorFenaseg = BigDecimal.ZERO;
        }
        if (valorPlace == null) {
            valorPlace = BigDecimal.ZERO;
        }
        return valorCetip.add(valorFenaseg).add(valorPlace);
    }


    @Override
    public Long getId() {
        return id;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }
}
