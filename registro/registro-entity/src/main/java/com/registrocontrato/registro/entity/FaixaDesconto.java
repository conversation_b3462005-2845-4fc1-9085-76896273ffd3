package com.registrocontrato.registro.entity;

import com.registrocontrato.infra.entity.BaseEntity;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Audited
@Table(schema = "financeiro")
public class FaixaDesconto extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long quantidadeInicial;

    private Long quantidadeFinal;

    private BigDecimal valorFinal;

    private BigDecimal percentual;

    private Long indice;

    @JoinColumn
    @ManyToOne
    private PrecoComposto precoComposto;

    @JoinColumn
    @ManyToOne(cascade = {}, fetch = FetchType.LAZY)
    private CupomDesconto cupom;

    public FaixaDesconto() {
        setQuantidadeInicial(1L);
    }

    public FaixaDesconto(Long indice) {
        this.indice = indice;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuantidadeFinal() {
        return quantidadeFinal;
    }

    public void setQuantidadeFinal(Long quantidadeFinal) {
        this.quantidadeFinal = quantidadeFinal;
    }

    public Long getQuantidadeInicial() {
        return quantidadeInicial;
    }

    public void setQuantidadeInicial(Long quantidadeInicial) {
        this.quantidadeInicial = quantidadeInicial;
    }

    public BigDecimal getPercentual() {
        return percentual;
    }

    public void setPercentual(BigDecimal percentual) {
        this.percentual = percentual;
    }

    public CupomDesconto getCupom() {
        return cupom;
    }

    public void setCupom(CupomDesconto cupom) {
        this.cupom = cupom;
    }

    public Long getIndice() {
        return indice;
    }

    public void setIndice(Long indice) {
        this.indice = indice;
    }

    public PrecoComposto getPrecoComposto() {
        return precoComposto;
    }

    public void setPrecoComposto(PrecoComposto precoComposto) {
        this.precoComposto = precoComposto;
    }

    public BigDecimal getValorFinal() {
        return valorFinal;
    }

    public void setValorFinal(BigDecimal valorFinal) {
        this.valorFinal = valorFinal;
    }
}
