package com.registrocontrato.registro.service.validation;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.repository.ContratoRepository;
import com.registrocontrato.registro.repository.CredenciamentoRepository;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.repository.UsuarioRepository;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Date;

import static com.registrocontrato.infra.util.PlaceconUtil.converteLocalDateEmDate;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ContratoValidationTest {

    @InjectMocks
    private ContratoValidation contratoValidation;

    @Mock
    private ContratoRepository contratoRepository;

    @Mock
    private FinanceiraRepository financeiraRepository;

    @Mock
    private UsuarioRepository usuarioRepository;

    @Mock
    private CredenciamentoRepository credenciamentoRepository;

    private AutoCloseable closeable;


    @BeforeEach
    void setup() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void shutdown() throws Exception {
        closeable.close();
    }

    @Test
    public void test_validarNumeroEnderecoDevedor_ufObrigatoriaECampoPreenchido_deveExecutarSemExcecao() throws ServiceException {
        Contrato contrato = Mockito.mock(Contrato.class);
        when(contrato.getNumeroEnderecoDevedor()).thenReturn("1231");
        when(contrato.getUfRegistro()).thenReturn(Uf.RS);

        contratoValidation.validarNumeroEnderecoDevedor(contrato);
    }

    @Test(expected = ServiceException.class)
    public void test_validarNumeroEnderecoDevedor_ufObrigatoriaECampoVazio_deveLancarExcecao() throws ServiceException {
        Contrato contrato = Mockito.mock(Contrato.class);
        when(contrato.getNumeroEnderecoDevedor()).thenReturn("");
        when(contrato.getUfRegistro()).thenReturn(Uf.RS);

        contratoValidation.validarNumeroEnderecoDevedor(contrato);
    }

    @Test(expected = ServiceException.class)
    public void test_validarNumeroEnderecoDevedor_ufObrigatoriaECampoNulo_deveLancarExcecao() throws ServiceException {
        Contrato contrato = Mockito.mock(Contrato.class);
        when(contrato.getNumeroEnderecoDevedor()).thenReturn(null);
        when(contrato.getUfRegistro()).thenReturn(Uf.RS);

        contratoValidation.validarNumeroEnderecoDevedor(contrato);
    }

    @Test
    public void test_validarNumeroEnderecoDevedor_ufNaoObrigatoria_naoDeveLancarExcecao() throws ServiceException {
        Contrato contrato = Mockito.mock(Contrato.class);
        when(contrato.getUfRegistro()).thenReturn(Uf.MS);

        contratoValidation.validarNumeroEnderecoDevedor(contrato);
    }
}
