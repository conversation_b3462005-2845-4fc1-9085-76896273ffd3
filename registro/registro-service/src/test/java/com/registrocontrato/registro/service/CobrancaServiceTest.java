package com.registrocontrato.registro.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SimNao;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.repository.CobrancaRepository;
import com.registrocontrato.registro.service.cobranca.CobrancaService;
import com.registrocontrato.registro.service.cobranca.EnviaCobrancaEmail;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;

@RunWith(MockitoJUnitRunner.class)
public class CobrancaServiceTest {

    @InjectMocks
    private CobrancaService cobrancaService;

    @Mock
    private FinanceiraService financeiraService;
    @Mock
    private EnviaCobrancaEmail enviaCobrancaEmail;
    @Mock
    private CobrancaRepository cobrancaRepository;


//    ------------------------

    @Test
    public void test_financeiraPossuiCobranca_PossuiUmaCobrancaComAFinanceiraEComRegistros() {
        Financeira financeiraArgumentoMock = Mockito.mock(Financeira.class);
        Financeira financeiraCobrancaMock = Mockito.mock(Financeira.class);

        List<Cobranca> cobrancas = new ArrayList<>();

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraCobrancaMock);
        cobrancas.add(cobrancaMock);

        Cobranca cobrancaMock2 = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock2.getFinanceira()).thenReturn(financeiraArgumentoMock);
        Mockito.when(cobrancaMock2.getQuantidadeRegistros()).thenReturn(132L);
        cobrancas.add(cobrancaMock2);

        Boolean resultado = cobrancaService.financeiraPossuiCobranca(financeiraArgumentoMock, cobrancas);
        assertTrue(resultado);
    }

    @Test
    public void test_financeiraPossuiCobranca_PossuiUmaCobrancaComAFinanceiraSemRegistros() {
        Financeira financeiraArgumentoMock = Mockito.mock(Financeira.class);
        Financeira financeiraCobrancaMock = Mockito.mock(Financeira.class);

        List<Cobranca> cobrancas = new ArrayList<>();

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraCobrancaMock);
        cobrancas.add(cobrancaMock);

        Cobranca cobrancaMock2 = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock2.getFinanceira()).thenReturn(financeiraArgumentoMock);
        Mockito.when(cobrancaMock2.getQuantidadeRegistros()).thenReturn(0L);
        cobrancas.add(cobrancaMock2);

        Boolean resultado = cobrancaService.financeiraPossuiCobranca(financeiraArgumentoMock, cobrancas);
        assertFalse(resultado);
    }

    @Test
    public void test_financeiraPossuiCobranca_CobrancasVazias() {
        Financeira financeiraMock = Mockito.mock(Financeira.class);

        Boolean resultado = cobrancaService.financeiraPossuiCobranca(financeiraMock, new ArrayList<>());
        assertFalse(resultado);
    }

    @Test
    public void test_financeiraPossuiCobranca_SemCobrancaComAFinanceira() {
        Financeira financeiraArgumentoMock = Mockito.mock(Financeira.class);
        Financeira financeiraCobrancaMock = Mockito.mock(Financeira.class);
        Financeira financeiraCobrancaMock2 = Mockito.mock(Financeira.class);

        List<Cobranca> cobrancas = new ArrayList<>();

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraCobrancaMock);
        cobrancas.add(cobrancaMock);

        Cobranca cobrancaMock2 = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock2.getFinanceira()).thenReturn(financeiraCobrancaMock2);
        cobrancas.add(cobrancaMock2);

        Boolean resultado = cobrancaService.financeiraPossuiCobranca(financeiraArgumentoMock, cobrancas);
        assertFalse(resultado);
    }


//    ---------------

    @Test
    public void test_enviaEmailFinanceirasSemFaturamento_comCobrancaNoPeriodoJaGerada_deveNaoFazerNada() {
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Uf uf = Uf.DF;
        Mockito.when(cobrancaRepository.countCobrancasByPeriodoAndUf(any(Date.class), any(Date.class), eq(uf))).thenReturn(1L);

        cobrancaService.enviaEmailFinanceirasSemFaturamento(new ArrayList<>(), uf, dataInicio, dataFim, null);
    }

    @Test
    public void test_enviaEmailFinanceirasSemFaturamento_financeiraComAvisoDesligado_deveNaoFazerNada() {
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Uf uf = Uf.DF;

        Financeira financeiraMock = Mockito.mock(Financeira.class);
        Mockito.when(financeiraMock.getEnviarEmailSemFaturamento()).thenReturn(SimNao.N);
        Mockito.when(financeiraMock.getId()).thenReturn(1L);

        Mockito.when(cobrancaRepository.countCobrancasByPeriodoAndUf(any(Date.class), any(Date.class), eq(uf))).thenReturn(0L);
        Mockito.when(financeiraService.findOne(eq(financeiraMock.getId()))).thenReturn(financeiraMock);

        cobrancaService.enviaEmailFinanceirasSemFaturamento(new ArrayList<>(), uf, dataInicio, dataFim, financeiraMock);
    }

    @Test
    public void test_enviaEmailFinanceirasSemFaturamento_financeiraComAvisoLigadoEPossuiCobranca_deveNaoFazerNada() {
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Uf uf = Uf.DF;

        Financeira financeiraMock = Mockito.mock(Financeira.class);
        Mockito.when(financeiraMock.getEnviarEmailSemFaturamento()).thenReturn(SimNao.S);
        Mockito.when(financeiraMock.getId()).thenReturn(1L);

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraMock);
        Mockito.when(cobrancaMock.getQuantidadeRegistros()).thenReturn(123L);

        Mockito.when(cobrancaRepository.countCobrancasByPeriodoAndUf(any(Date.class), any(Date.class), eq(uf))).thenReturn(0L);
        Mockito.when(financeiraService.findOne(eq(financeiraMock.getId()))).thenReturn(financeiraMock);

        ArrayList<Cobranca> cobrancas = new ArrayList<>();
        cobrancas.add(cobrancaMock);

        cobrancaService.enviaEmailFinanceirasSemFaturamento(cobrancas, uf, dataInicio, dataFim, financeiraMock);
        Mockito.verify(enviaCobrancaEmail, times(0)).enviaEmailFinanceiraEstadoSemFaturamento(eq(financeiraMock), eq(uf), eq(dataInicio), eq(dataFim));
    }

    @Test
    public void test_enviaEmailFinanceirasSemFaturamento_financeiraComAvisoLigadoENaoPossuiCobranca_deveEnviarEmail() {
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Uf uf = Uf.DF;

        Financeira financeiraMock = Mockito.mock(Financeira.class);
        Mockito.when(financeiraMock.getEnviarEmailSemFaturamento()).thenReturn(SimNao.S);

        Financeira financeiraCobrancaMock = Mockito.mock(Financeira.class);

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraCobrancaMock);

        Mockito.when(cobrancaRepository.countCobrancasByPeriodoAndUf(any(Date.class), any(Date.class), eq(uf))).thenReturn(0L);
        Mockito.when(financeiraService.findOne(eq(financeiraMock.getId()))).thenReturn(financeiraMock);
        Mockito.doNothing().when(enviaCobrancaEmail).enviaEmailFinanceiraEstadoSemFaturamento(any(Financeira.class), any(Uf.class), any(Date.class), any(Date.class));

        ArrayList<Cobranca> cobrancas = new ArrayList<>();
        cobrancas.add(cobrancaMock);

        cobrancaService.enviaEmailFinanceirasSemFaturamento(cobrancas, uf, dataInicio, dataFim, financeiraMock);
        Mockito.verify(enviaCobrancaEmail, times(1)).enviaEmailFinanceiraEstadoSemFaturamento(eq(financeiraMock), eq(uf), eq(dataInicio), eq(dataFim));
    }

    @Test
    public void test_enviaEmailFinanceirasSemFaturamento_financeirasComAlertaLigadoPossuemCobranca_deveNaoEnviarNenhumEmail() {
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Uf uf = Uf.DF;

        Financeira financeiraMock1 = Mockito.mock(Financeira.class);

        Financeira financeiraMock2 = Mockito.mock(Financeira.class);

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraMock1);
        Mockito.when(cobrancaMock.getQuantidadeRegistros()).thenReturn(123L);

        Cobranca cobrancaMock2 = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock2.getFinanceira()).thenReturn(financeiraMock2);
        Mockito.when(cobrancaMock2.getQuantidadeRegistros()).thenReturn(123L);

        List<Cobranca> cobrancas = Arrays.asList(cobrancaMock, cobrancaMock2);

        Mockito.when(cobrancaRepository.countCobrancasByPeriodoAndUf(any(Date.class), any(Date.class), eq(uf))).thenReturn(0L);
        Mockito.when(financeiraService.findFinanceirasAvisoFaturamentoAtivoECredenciamentoAtivo(eq(uf), eq(dataInicio), eq(dataFim)))
                .thenReturn(Arrays.asList(financeiraMock1, financeiraMock2));

        cobrancaService.enviaEmailFinanceirasSemFaturamento(cobrancas, uf, dataInicio, dataFim, null);
        Mockito.verify(enviaCobrancaEmail, times(0)).enviaEmailFinanceiraEstadoSemFaturamento(any(Financeira.class), eq(uf), eq(dataInicio), eq(dataFim));
    }

    @Test
    public void test_enviaEmailFinanceirasSemFaturamento_financeirasComAlertaLigadoUmaNaoPossuiCobranca_deveEnviarUmEmail() {
        Date dataInicio = new Date();
        Date dataFim = new Date();
        Uf uf = Uf.DF;

        Financeira financeiraMock1 = Mockito.mock(Financeira.class);

        Financeira financeiraMock2 = Mockito.mock(Financeira.class);

        Financeira financeiraCobrancaMock = Mockito.mock(Financeira.class);

        Cobranca cobrancaMock = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock.getFinanceira()).thenReturn(financeiraCobrancaMock);

        Cobranca cobrancaMock2 = Mockito.mock(Cobranca.class);
        Mockito.when(cobrancaMock2.getFinanceira()).thenReturn(financeiraMock2);
        Mockito.when(cobrancaMock2.getQuantidadeRegistros()).thenReturn(123L);

        List<Cobranca> cobrancas = Arrays.asList(cobrancaMock, cobrancaMock2);

        Mockito.when(cobrancaRepository.countCobrancasByPeriodoAndUf(any(Date.class), any(Date.class), eq(uf))).thenReturn(0L);
        Mockito.when(financeiraService.findFinanceirasAvisoFaturamentoAtivoECredenciamentoAtivo(eq(uf), eq(dataInicio), eq(dataFim)))
                .thenReturn(Arrays.asList(financeiraMock1, financeiraMock2));

        cobrancaService.enviaEmailFinanceirasSemFaturamento(cobrancas, uf, dataInicio, dataFim, null);
        Mockito.verify(enviaCobrancaEmail, times(1)).enviaEmailFinanceiraEstadoSemFaturamento(eq(financeiraMock1), eq(uf), eq(dataInicio), eq(dataFim));
    }
}
