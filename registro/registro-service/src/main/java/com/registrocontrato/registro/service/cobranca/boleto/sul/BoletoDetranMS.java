package com.registrocontrato.registro.service.cobranca.boleto.sul;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoDetran;
import com.registrocontrato.registro.service.cobranca.boleto.BoletoDetranInterface;
import com.registrocontrato.registro.service.detran.ms.rest.WsDetranMS;
import com.registrocontrato.registro.service.detran.ms.client.BoletoCobrancaDTO;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Base64;
import java.util.Date;

@Component
public class BoletoDetranMS implements BoletoDetranInterface {

    private final Log logger = LogFactory.getLog(getClass());

    private String fileDir;

    private final WsDetranMS wsDetranMsRestClient;

    public BoletoDetranMS(@Value("${file-boleto.dir:null}") String fileDir, WsDetranMS wsDetranMsRestClient) {
        this.fileDir = fileDir;
        this.wsDetranMsRestClient = wsDetranMsRestClient;
    }

    @Override
    public Date emitirBoletoDetran(String numeroDocumento, Cobranca cobranca, BoletoDetran boletoDetran) throws ServiceException {

        logger.info("Geração do Boleto do Detran -> " + cobranca.getEstado());

        try {
            BoletoCobrancaDTO[] boleto = wsDetranMsRestClient.consultarBoleto(cobranca.getFinanceira().getDocumento());
            logger.info("Buscou informações do boleto");
            logger.info("Boleto: " + boleto);
            String dataVencimento = boleto[0].getVencimento();
            logger.info("Data de Vencimento: " + dataVencimento);
            String numeroBoleto = boleto[0].getNumeroDocumento();
            logger.info("Numero do Boleto MS: " + numeroBoleto);
            String boletoByte = wsDetranMsRestClient.buscaBoletoByte(numeroBoleto, cobranca.getFinanceira().getDocumento());
            File targetFile = new File(fileDir, numeroDocumento + ".pdf");
            byte[] arquivo = Base64.getDecoder().decode(boletoByte);
            FileOutputStream file = new FileOutputStream(targetFile);
            file.write(arquivo);
            logger.info("Gerou o Boleto do Detran -> " + cobranca.getEstado());
            return PlaceconUtil.stringToDateDataPadrao(dataVencimento);
        } catch (Exception e) {
            logger.error("Erro na Geração do Boleto " + cobranca.getEstado());
//            throw new ServiceException("cobrança não gerada: " + e.getMessage());
        }
        return null;
    }

    @Override
    public Uf getUf() {
        return Uf.MS;
    }
}
