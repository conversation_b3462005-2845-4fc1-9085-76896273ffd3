package com.registrocontrato.registro.repository;

import java.util.List;

import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.CupomDesconto;
import com.registrocontrato.registro.entity.FaixaDesconto;

public interface FaixaDescontoRepository extends BaseRepository<FaixaDesconto> {
	
	List<FaixaDesconto>findByCupomOrderByIndiceAsc(CupomDesconto cupom);

}
