package com.registrocontrato.registro.service.dto.migracao;

public class AnexoMigracaoDTO {

    ContratoMigracaoDTO contratoMigracao;

    VeiculoMigracaoDTO veiculoMigracao;

    String arquivoBase64;

    String referenciaArquivo;

    String nomeArquivo;

    public AnexoMigracaoDTO() {
    }

    public AnexoMigracaoDTO(ContratoMigracaoDTO contratoMigracao, VeiculoMigracaoDTO veiculoMigracao, String arquivoBase64, String referenciaArquivo, String nomeArquivo) {
        this.contratoMigracao = contratoMigracao;
        this.veiculoMigracao = veiculoMigracao;
        this.arquivoBase64 = arquivoBase64;
        this.referenciaArquivo = referenciaArquivo;
        this.nomeArquivo = nomeArquivo;
    }

    public ContratoMigracaoDTO getContratoMigracao() {
        return contratoMigracao;
    }

    public void setContratoMigracao(ContratoMigracaoDTO contratoMigracao) {
        this.contratoMigracao = contratoMigracao;
    }

    public VeiculoMigracaoDTO getVeiculoMigracao() {
        return veiculoMigracao;
    }

    public void setVeiculoMigracao(VeiculoMigracaoDTO veiculoMigracao) {
        this.veiculoMigracao = veiculoMigracao;
    }

    public String getArquivoBase64() {
        return arquivoBase64;
    }

    public void setArquivoBase64(String arquivoBase64) {
        this.arquivoBase64 = arquivoBase64;
    }

    public String getReferenciaArquivo() {
        return referenciaArquivo;
    }

    public void setReferenciaArquivo(String referenciaArquivo) {
        this.referenciaArquivo = referenciaArquivo;
    }

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }
}
