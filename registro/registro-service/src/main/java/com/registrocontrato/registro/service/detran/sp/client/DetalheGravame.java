//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2018.05.14 às 06:31:46 PM BRT 
//

package com.registrocontrato.registro.service.detran.sp.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import javax.xml.bind.annotation.XmlType;

import com.registrocontrato.infra.util.PlaceconUtil;

/**
 * <p>
 * Classe Java de DetalheGravame complex type.
 * 
 * <p>
 * O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro
 * desta classe.
 * 
 * <pre>
 * &lt;complexType name="DetalheGravame"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="anoFabricacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="anoModelo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="chassi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="cnpjAgente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="codigoAgente" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataCancelamento" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataEmissao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataInclusaoGravame" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataQuitacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="dataVigenciaContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="flagContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="idenRemarcacao" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="nomeFinanciado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroDocumentoFinanciado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="numeroGravame" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="placa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="renavam" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="situacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tipoDocumentoFinanciado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tipoGravame" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UFPlaca" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DetalheGravame", propOrder = { "anoFabricacao", "anoModelo", "chassi", "cnpjAgente", "codigoAgente", "dataCancelamento", "dataEmissao", "dataInclusaoGravame", "dataQuitacao", "dataVigenciaContrato",
		"flagContrato", "idenRemarcacao", "nomeFinanciado", "numeroContrato", "numeroDocumentoFinanciado", "numeroGravame", "placa", "renavam", "situacao", "tipoDocumentoFinanciado", "tipoGravame", "ufPlaca" })
public class DetalheGravame {

	protected String anoFabricacao;
	protected String anoModelo;
	protected String chassi;
	protected String cnpjAgente;
	protected String codigoAgente;
	protected String dataCancelamento;
	protected String dataEmissao;
	protected String dataInclusaoGravame;
	protected String dataQuitacao;
	protected String dataVigenciaContrato;
	protected String flagContrato;
	protected int idenRemarcacao;
	protected String nomeFinanciado;
	protected String numeroContrato;
	protected String numeroDocumentoFinanciado;
	protected String numeroGravame;
	protected String placa;
	protected String renavam;
	protected String situacao;
	protected String tipoDocumentoFinanciado;
	protected String tipoGravame;
	@XmlElement(name = "UFPlaca")
	protected String ufPlaca;
	
	@XmlTransient
	protected String situacaoContrato;
	@XmlTransient
	protected String situacaoAditivoContrato; 
	@XmlTransient
	protected String situacaoVeiculo; 
	@XmlTransient
	protected String numeroContratoAditivo;

	/**
	 * Obtém o valor da propriedade anoFabricacao.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAnoFabricacao() {
		return anoFabricacao;
	}

	/**
	 * Define o valor da propriedade anoFabricacao.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAnoFabricacao(String value) {
		this.anoFabricacao = value;
	}

	/**
	 * Obtém o valor da propriedade anoModelo.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAnoModelo() {
		return anoModelo;
	}

	/**
	 * Define o valor da propriedade anoModelo.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAnoModelo(String value) {
		this.anoModelo = value;
	}

	/**
	 * Obtém o valor da propriedade chassi.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getChassi() {
		return chassi;
	}

	/**
	 * Define o valor da propriedade chassi.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setChassi(String value) {
		this.chassi = value;
	}

	/**
	 * Obtém o valor da propriedade cnpjAgente.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCnpjAgente() {
		return cnpjAgente;
	}

	/**
	 * Define o valor da propriedade cnpjAgente.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCnpjAgente(String value) {
		this.cnpjAgente = value;
	}

	/**
	 * Obtém o valor da propriedade codigoAgente.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCodigoAgente() {
		return codigoAgente;
	}

	/**
	 * Define o valor da propriedade codigoAgente.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCodigoAgente(String value) {
		this.codigoAgente = value;
	}

	/**
	 * Obtém o valor da propriedade dataCancelamento.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDataCancelamento() {
		return PlaceconUtil.formataData(dataCancelamento);
	}

	/**
	 * Define o valor da propriedade dataCancelamento.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDataCancelamento(String value) {
		this.dataCancelamento = value;
	}

	/**
	 * Obtém o valor da propriedade dataEmissao.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDataEmissao() {
		return PlaceconUtil.formataData(dataEmissao);
	}

	/**
	 * Define o valor da propriedade dataEmissao.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDataEmissao(String value) {
		this.dataEmissao = value;
	}

	/**
	 * Obtém o valor da propriedade dataInclusaoGravame.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDataInclusaoGravame() {
		return PlaceconUtil.formataData(dataInclusaoGravame);
	}

	/**
	 * Define o valor da propriedade dataInclusaoGravame.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDataInclusaoGravame(String value) {
		this.dataInclusaoGravame = value;
	}

	/**
	 * Obtém o valor da propriedade dataQuitacao.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDataQuitacao() {
		return PlaceconUtil.formataData(dataQuitacao);
	}

	/**
	 * Define o valor da propriedade dataQuitacao.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDataQuitacao(String value) {
		this.dataQuitacao = value;
	}

	/**
	 * Obtém o valor da propriedade dataVigenciaContrato.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDataVigenciaContrato() {
		return PlaceconUtil.formataData(dataVigenciaContrato);
	}

	/**
	 * Define o valor da propriedade dataVigenciaContrato.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDataVigenciaContrato(String value) {
		this.dataVigenciaContrato = value;
	}

	/**
	 * Obtém o valor da propriedade flagContrato.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getFlagContrato() {
		return flagContrato;
	}

	/**
	 * Define o valor da propriedade flagContrato.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setFlagContrato(String value) {
		this.flagContrato = value;
	}

	/**
	 * Obtém o valor da propriedade idenRemarcacao.
	 * 
	 */
	public int getIdenRemarcacao() {
		return idenRemarcacao;
	}

	public String getRemarcacao() {
		return idenRemarcacao == 1 ? "Sim" : "Não";
	}

	/**
	 * Define o valor da propriedade idenRemarcacao.
	 * 
	 */
	public void setIdenRemarcacao(int value) {
		this.idenRemarcacao = value;
	}

	/**
	 * Obtém o valor da propriedade nomeFinanciado.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getNomeFinanciado() {
		return nomeFinanciado;
	}

	/**
	 * Define o valor da propriedade nomeFinanciado.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setNomeFinanciado(String value) {
		this.nomeFinanciado = value;
	}

	/**
	 * Obtém o valor da propriedade numeroContrato.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getNumeroContrato() {
		return numeroContrato;
	}

	/**
	 * Define o valor da propriedade numeroContrato.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setNumeroContrato(String value) {
		this.numeroContrato = value;
	}

	/**
	 * Obtém o valor da propriedade numeroDocumentoFinanciado.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getNumeroDocumentoFinanciado() {
		return numeroDocumentoFinanciado;
	}

	/**
	 * Define o valor da propriedade numeroDocumentoFinanciado.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setNumeroDocumentoFinanciado(String value) {
		this.numeroDocumentoFinanciado = value;
	}

	/**
	 * Obtém o valor da propriedade numeroGravame.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getNumeroGravame() {
		return numeroGravame;
	}

	/**
	 * Define o valor da propriedade numeroGravame.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setNumeroGravame(String value) {
		this.numeroGravame = value;
	}

	/**
	 * Obtém o valor da propriedade placa.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPlaca() {
		return placa;
	}

	/**
	 * Define o valor da propriedade placa.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPlaca(String value) {
		this.placa = value;
	}

	/**
	 * Obtém o valor da propriedade renavam.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRenavam() {
		return renavam;
	}

	/**
	 * Define o valor da propriedade renavam.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRenavam(String value) {
		this.renavam = value;
	}

	/**
	 * Obtém o valor da propriedade situacao.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSituacao() {
		if(situacao != null && situacao.equals("1")) {
			return "Ativo";
		}
		return "Inativo";
	}

	/**
	 * Define o valor da propriedade situacao.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSituacao(String value) {
		this.situacao = value;
	}

	/**
	 * Obtém o valor da propriedade tipoDocumentoFinanciado.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTipoDocumentoFinanciado() {
		if (tipoDocumentoFinanciado != null && tipoDocumentoFinanciado.equals("1")) {
			return "CPF";
		}
		return tipoDocumentoFinanciado;
	}

	/**
	 * Define o valor da propriedade tipoDocumentoFinanciado.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTipoDocumentoFinanciado(String value) {
		this.tipoDocumentoFinanciado = value;
	}

	/**
	 * Obtém o valor da propriedade tipoGravame.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTipoGravame() {
		if (tipoGravame != null) {
			if (tipoGravame.equals("1")) {
				return "Arrendamento";
			} else if (tipoGravame.equals("2")) {
				return "Reserva de Domínio";
			} else if (tipoGravame.equals("3")) {
				return "Alienação";
			} else if (tipoGravame.equals("4")) {
				return "Penhor Mercantil";
			}
		}
		return tipoGravame;
	}

	/**
	 * Define o valor da propriedade tipoGravame.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTipoGravame(String value) {
		this.tipoGravame = value;
	}

	/**
	 * Obtém o valor da propriedade ufPlaca.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUFPlaca() {
		return ufPlaca;
	}

	/**
	 * Define o valor da propriedade ufPlaca.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUFPlaca(String value) {
		this.ufPlaca = value;
	}

	public String getSituacaoContrato() {
		return situacaoContrato;
	}

	public void setSituacaoContrato(String situacaoContrato) {
		this.situacaoContrato = situacaoContrato;
	}

	public String getSituacaoAditivoContrato() {
		return situacaoAditivoContrato;
	}

	public void setSituacaoAditivoContrato(String situacaoAditivoContrato) {
		this.situacaoAditivoContrato = situacaoAditivoContrato;
	}

	public String getSituacaoVeiculo() {
		return situacaoVeiculo;
	}

	public void setSituacaoVeiculo(String situacaoVeiculo) {
		this.situacaoVeiculo = situacaoVeiculo;
	}

    public String getNumeroContratoAditivo() {
        return numeroContratoAditivo;
    }

    public void setNumeroContratoAditivo(String numeroContratoAditivo) {
        this.numeroContratoAditivo = numeroContratoAditivo;
    }
	
}
