package com.registrocontrato.registro.service.cobranca.notareembolso.norte;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.repository.CobrancaRepository;
import com.registrocontrato.registro.service.cobranca.notareembolso.NotaDeReembolsoDefault;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.ServletContext;
import java.util.Collections;
import java.util.List;

@Component
public class NotaDeReembolsoAP extends NotaDeReembolsoDefault {

    public NotaDeReembolsoAP(CobrancaRepository cobrancaRepository, @Value("${file-nf.dir:null}") String fileNfDir, ServletContext sc) {
        super(cobrancaRepository, fileNfDir, sc);
    }

    @Override
    public Uf getUf() {
        return Uf.AP;
    }

    @Override
    public List<String> getCnpjFinanceiras() {
        return Collections.emptyList();
    }
}
