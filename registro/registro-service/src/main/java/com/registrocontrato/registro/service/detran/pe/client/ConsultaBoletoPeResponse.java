package com.registrocontrato.registro.service.detran.pe.client;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ConsultaBoletoPeResponse {

    @JsonProperty("AnoEnvio")
    private String AnoEnvio;
    @JsonProperty("MesEnvio")
    private String MesEnvio;
    @JsonProperty("QuantidadeEnvio")
    private String QuantidadeEnvio;
    @JsonProperty("ValorUnitarioEnvio")
    private String ValorUnitarioEnvio;
    @JsonProperty("DataVencimento")
    private String DataVencimento;
    @JsonProperty("CpfCnpjAgenteFinanceiro")
    private String CpfCnpjAgenteFinanceiro;
    @JsonProperty("NomeAgenteFinanceiro")
    private String NomeAgenteFinanceiro;
    @JsonProperty("QuantidadeGravame")
    private String QuantidadeGravame;
    @JsonProperty("DataPagamento")
    private String DataPagamento;
    @JsonProperty("ValorPago")
    private String ValorPago;
    @JsonProperty("NumeroBancario")
    private String NumeroBancario;
    @JsonProperty("NomeBanco")
    private String NomeBanco;
    @JsonProperty("SaldoAberto")
    private String SaldoAberto;
    @JsonProperty("Texto")
    private String Texto;
    @JsonProperty("TipoMensagem")
    private String TipoMensagem;
    private String sTipoMensagem;
    private String excecao;

    public String getAnoEnvio() {
        return AnoEnvio;
    }

    public void setAnoEnvio(String anoEnvio) {
        AnoEnvio = anoEnvio;
    }

    public String getMesEnvio() {
        return MesEnvio;
    }

    public void setMesEnvio(String mesEnvio) {
        MesEnvio = mesEnvio;
    }

    public String getQuantidadeEnvio() {
        return QuantidadeEnvio;
    }

    public void setQuantidadeEnvio(String quantidadeEnvio) {
        QuantidadeEnvio = quantidadeEnvio;
    }

    public String getValorUnitarioEnvio() {
        return ValorUnitarioEnvio;
    }

    public void setValorUnitarioEnvio(String valorUnitarioEnvio) {
        ValorUnitarioEnvio = valorUnitarioEnvio;
    }

    public String getDataVencimento() {
        return DataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        DataVencimento = dataVencimento;
    }

    public String getCpfCnpjAgenteFinanceiro() {
        return CpfCnpjAgenteFinanceiro;
    }

    public void setCpfCnpjAgenteFinanceiro(String cpfCnpjAgenteFinanceiro) {
        CpfCnpjAgenteFinanceiro = cpfCnpjAgenteFinanceiro;
    }

    public String getNomeAgenteFinanceiro() {
        return NomeAgenteFinanceiro;
    }

    public void setNomeAgenteFinanceiro(String nomeAgenteFinanceiro) {
        NomeAgenteFinanceiro = nomeAgenteFinanceiro;
    }

    public String getQuantidadeGravame() {
        return QuantidadeGravame;
    }

    public void setQuantidadeGravame(String quantidadeGravame) {
        QuantidadeGravame = quantidadeGravame;
    }

    public String getDataPagamento() {
        return DataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        DataPagamento = dataPagamento;
    }

    public String getValorPago() {
        return ValorPago;
    }

    public void setValorPago(String valorPago) {
        ValorPago = valorPago;
    }

    public String getNumeroBancario() {
        return NumeroBancario;
    }

    public void setNumeroBancario(String numeroBancario) {
        NumeroBancario = numeroBancario;
    }

    public String getNomeBanco() {
        return NomeBanco;
    }

    public void setNomeBanco(String nomeBanco) {
        NomeBanco = nomeBanco;
    }

    public String getSaldoAberto() {
        return SaldoAberto;
    }

    public void setSaldoAberto(String saldoAberto) {
        SaldoAberto = saldoAberto;
    }

    public String getTexto() {
        return Texto;
    }

    public void setTexto(String texto) {
        Texto = texto;
    }

    public String getTipoMensagem() {
        return TipoMensagem;
    }

    public void setTipoMensagem(String tipoMensagem) {
        TipoMensagem = tipoMensagem;
    }

    public String getsTipoMensagem() {
        return sTipoMensagem;
    }

    public void setsTipoMensagem(String sTipoMensagem) {
        this.sTipoMensagem = sTipoMensagem;
    }

    public String getExcecao() {
        return excecao;
    }

    public void setExcecao(String excecao) {
        this.excecao = excecao;
    }

}
