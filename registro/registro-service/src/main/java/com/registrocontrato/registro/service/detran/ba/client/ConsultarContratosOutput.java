//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.registrocontrato.registro.service.detran.ba.client;

import com.registrocontrato.registro.service.detran.ba.client.GeneralListItems;
import com.registrocontrato.registro.service.detran.ba.client.GeneralStatusMessages;

import java.math.BigInteger;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(
        name = "consultarContratos_Output",
        propOrder = {"codigoUsuario", "senhaUsuario", "codigoOperacao", "matricAtendente", "tipoServicoWeb", "codigoEstacao", "chassi", "listaContratos", "listaContratosList", "retornoCritica", "statusMessages"}
)
public class ConsultarContratosOutput {
    protected BigInteger codigoUsuario;
    protected String senhaUsuario;
    protected String codigoOperacao;
    protected BigInteger matricAtendente;
    protected String tipoServicoWeb;
    protected String codigoEstacao;
    protected String chassi;
    protected BigInteger listaContratos;
    @XmlElement(
            name = "listaContratos_List"
    )
    protected GeneralListItems listaContratosList;
    protected BigInteger retornoCritica;
    @XmlElement(
            name = "StatusMessages"
    )
    protected GeneralStatusMessages statusMessages;

    public ConsultarContratosOutput() {
    }

    public BigInteger getCodigoUsuario() {
        return this.codigoUsuario;
    }

    public void setCodigoUsuario(BigInteger var1) {
        this.codigoUsuario = var1;
    }

    public String getSenhaUsuario() {
        return this.senhaUsuario;
    }

    public void setSenhaUsuario(String var1) {
        this.senhaUsuario = var1;
    }

    public String getCodigoOperacao() {
        return this.codigoOperacao;
    }

    public void setCodigoOperacao(String var1) {
        this.codigoOperacao = var1;
    }

    public BigInteger getMatricAtendente() {
        return this.matricAtendente;
    }

    public void setMatricAtendente(BigInteger var1) {
        this.matricAtendente = var1;
    }

    public String getTipoServicoWeb() {
        return this.tipoServicoWeb;
    }

    public void setTipoServicoWeb(String var1) {
        this.tipoServicoWeb = var1;
    }

    public String getCodigoEstacao() {
        return this.codigoEstacao;
    }

    public void setCodigoEstacao(String var1) {
        this.codigoEstacao = var1;
    }

    public String getChassi() {
        return this.chassi;
    }

    public void setChassi(String var1) {
        this.chassi = var1;
    }

    public BigInteger getListaContratos() {
        return this.listaContratos;
    }

    public void setListaContratos(BigInteger var1) {
        this.listaContratos = var1;
    }

    public GeneralListItems getListaContratosList() {
        return this.listaContratosList;
    }

    public void setListaContratosList(GeneralListItems var1) {
        this.listaContratosList = var1;
    }

    public BigInteger getRetornoCritica() {
        return this.retornoCritica;
    }

    public void setRetornoCritica(BigInteger var1) {
        this.retornoCritica = var1;
    }

    public GeneralStatusMessages getStatusMessages() {
        return this.statusMessages;
    }

    public void setStatusMessages(GeneralStatusMessages var1) {
        this.statusMessages = var1;
    }
}
