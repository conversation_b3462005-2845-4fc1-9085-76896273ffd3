//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2019.07.19 às 04:52:20 PM BRT 
//


package com.registrocontrato.registro.service.detran.rj.client;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.registrocontrato.registro.service.detran.rj.client package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.registrocontrato.registro.service.detran.rj.client
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link GTRN0003 }
     * 
     */
    public GTRN0003 createGTRN0003() {
        return new GTRN0003();
    }

    /**
     * Create an instance of {@link GTRN0003Response }
     * 
     */
    public GTRN0003Response createGTRN0003Response() {
        return new GTRN0003Response();
    }

    /**
     * Create an instance of {@link GTRN0003 .Variavel }
     * 
     */
    public GTRN0003 .Variavel createGTRN0003Variavel() {
        return new GTRN0003 .Variavel();
    }

    /**
     * Create an instance of {@link GTRN0003Response.Variavel }
     * 
     */
    public GTRN0003Response.Variavel createGTRN0003ResponseVariavel() {
        return new GTRN0003Response.Variavel();
    }

}
