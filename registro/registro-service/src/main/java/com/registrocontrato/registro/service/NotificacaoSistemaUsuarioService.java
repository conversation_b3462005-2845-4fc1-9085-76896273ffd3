package com.registrocontrato.registro.service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.NotificacaoSistema;
import com.registrocontrato.registro.entity.NotificacaoSistemaUsuario;
import com.registrocontrato.registro.repository.NotificacaoSistemaUsuarioRepository;
import com.registrocontrato.registro.service.dto.NotificacaoUsuarioDTO;
import com.registrocontrato.seguranca.entity.Usuario;

@Service
@Transactional
public class NotificacaoSistemaUsuarioService extends BaseService<NotificacaoSistemaUsuario, NotificacaoUsuarioDTO> {

	private static final long serialVersionUID = 1L;

	@Autowired
	private NotificacaoSistemaUsuarioRepository repository;
	
	@Override
	public Page<NotificacaoSistemaUsuario> findAll(int first, int pageSize, NotificacaoUsuarioDTO filter) {

		Specification<NotificacaoSistemaUsuario> notificacaoSpecification = new Specification<NotificacaoSistemaUsuario>() {

			@Override
			public Predicate toPredicate(Root<NotificacaoSistemaUsuario> root, CriteriaQuery<?> cq, CriteriaBuilder cb) {
				List<Predicate> predicates = new ArrayList<>();
				Join<NotificacaoSistemaUsuario, NotificacaoSistema> joinNotificacao = root.join("notificacao", JoinType.INNER);

				if (!StringUtils.isBlank(filter.getTitulo())) {
					predicates.add(cb.like(cb.lower(joinNotificacao.<String>get("titulo")), "%" + filter.getTitulo() + "%"));
				}
				if (filter.getUf() != null){
					predicates.add(cb.equal(joinNotificacao.<Uf>get("uf"), filter.getUf()));
				}
				if (filter.getDataInicio() != null){
					predicates.add(cb.greaterThanOrEqualTo(joinNotificacao.<Date>get("dataInicio"), filter.getDataInicio()));
				}
				if (filter.getDataFim() != null){
					predicates.add(cb.lessThanOrEqualTo(joinNotificacao.<Date>get("dataFim"), filter.getDataFim()));
				}
				predicates.add(cb.equal(root.<String>get("cpf"), filter.getUsuario().getCpf()));

				return andTogether(predicates, cb);
			}

			private Predicate andTogether(List<Predicate> predicates, CriteriaBuilder cb) {
				return cb.and(predicates.toArray(new Predicate[0]));
			}
		};
		return repository.findAll(notificacaoSpecification, new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "notificacao.dataInicio")));
	}
	
	public List<NotificacaoSistemaUsuario>findLidasUsuario(Usuario usuario){
		return repository.findByCpf(usuario.getCpf());
	}

	@Override
	protected PagingAndSortingRepository<NotificacaoSistemaUsuario, Long> getRepository() {
		return repository;
	}
	
	public NotificacaoSistemaUsuario findByNotificacaoAndUsuario(NotificacaoSistema notificacao, String cpf){
		return repository.findByNotificacaoAndCpf(notificacao, cpf);
	}
	
	public List<NotificacaoSistemaUsuario> findByNotificacao(NotificacaoSistema notificacao){
		return repository.findByNotificacao(notificacao);
	}
	
	public void delete(Collection<NotificacaoSistemaUsuario>colecao){
		if (!PlaceconUtil.isListaVaziaOuNula(colecao)){
			repository.delete(colecao);
		}
	}

}
