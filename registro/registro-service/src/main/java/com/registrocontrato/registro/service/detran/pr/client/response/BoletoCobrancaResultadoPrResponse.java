package com.registrocontrato.registro.service.detran.pr.client.response;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

public class BoletoCobrancaResultadoPrResponse {

	private String agenteCNPJ;

	private String registradoraCNPJ;

	private String linhaDigitavel;

	private String codigoBarras;

	@JsonFormat(shape = JsonFormat.Shape.STRING, locale = "pt_BR", timezone = "GMT-3")
	private Date dataEmissao;

	@JsonFormat(shape = JsonFormat.Shape.STRING, locale = "pt_BR", timezone = "GMT-3")
	private Date dataVencimento;

	private BigDecimal valor;

	private String mesReferencia;

	private String pdf;

	public String getAgenteCNPJ() {
		return agenteCNPJ;
	}

	public void setAgenteCNPJ(String agenteCNPJ) {
		this.agenteCNPJ = agenteCNPJ;
	}

	public String getRegistradoraCNPJ() {
		return registradoraCNPJ;
	}

	public void setRegistradoraCNPJ(String registradoraCNPJ) {
		this.registradoraCNPJ = registradoraCNPJ;
	}

	public String getLinhaDigitavel() {
		return linhaDigitavel;
	}

	public void setLinhaDigitavel(String linhaDigitavel) {
		this.linhaDigitavel = linhaDigitavel;
	}

	public String getCodigoBarras() {
		return codigoBarras;
	}

	public void setCodigoBarras(String codigoBarras) {
		this.codigoBarras = codigoBarras;
	}

	public Date getDataEmissao() {
		return dataEmissao;
	}

	public void setDataEmissao(Date dataEmissao) {
		this.dataEmissao = dataEmissao;
	}

	public Date getDataVencimento() {
		return dataVencimento;
	}

	public void setDataVencimento(Date dataVencimento) {
		this.dataVencimento = dataVencimento;
	}

	public BigDecimal getValor() {
		return valor;
	}

	public void setValor(BigDecimal valor) {
		this.valor = valor;
	}

	public String getMesReferencia() {
		return mesReferencia;
	}

	public void setMesReferencia(String mesReferencia) {
		this.mesReferencia = mesReferencia;
	}

	public String getPdf() {
		return pdf;
	}

	public void setPdf(String pdf) {
		this.pdf = pdf;
	}
}
