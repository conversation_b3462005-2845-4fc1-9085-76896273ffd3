package com.registrocontrato.registro.service.detran.al.rest;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.TipoMensagem;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.al.client.ConsultaResponse;
import com.registrocontrato.registro.service.detran.al.client.EnvioImagemResponse;
import com.registrocontrato.registro.service.detran.al.client.LoginOAuthResponse;
import com.registrocontrato.registro.service.detran.al.client.RegistrarContratoRequest;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.registro.service.dto.RetornoContratoDetranDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.text.ParseException;
import java.util.Collections;

@Component
@Profile({"desenv", "homol"})
public class WsDetranAlRestClientDev extends WsDetranAL {


    protected WsDetranAlRestClientDev(
            FinanceiraRepository financeiraRepository,
            AcessoSenhaService acessoSenhaService,
            MensagemRetornoRepository mensagemRetornoRepository,
            RegistroEnvioService registroEnvioService,
            UsuarioService usuarioService) {
        super(financeiraRepository, acessoSenhaService, mensagemRetornoRepository, registroEnvioService, usuarioService);
    }

    @Override
    public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
        try {
            RegistrarContratoRequest request = putRequest(contrato);
            return obterToken(contrato.getFinanceira()).map(token -> {

                HttpHeaders headers = getHttpHeaders(MediaType.APPLICATION_JSON);
                headers.setAccept(Collections.singletonList(MediaType.ALL));
                headers.set("Authorization", token.getTokenType() + " " + token.getAccessToken());
                String url = getUrl() + URL_REGISTRO_CONTRATO;

                RestTemplate restTemplate = new RestTemplate();
//                HttpEntity<RegistroResponse> response = restTemplate.exchange(
//                        url,
//                        HttpMethod.POST,
//                        new HttpEntity<>(request, headers),
//                        RegistroResponse.class
//                );

//                if (response.getBody() != null) {
//                    RegistroResponse body = response.getBody();
//                    log.info(body.toString());
//                    MensagemRetornoDTO mensagem = new MensagemRetornoDTO();
//                    mensagem.setUf(getUf());
//                    mensagem.setCodigo(body.getNumeroDocumentoEletronico().toString());
//                    mensagem.setDescricao(body.getMsgRespostaDetran());
//                    mensagem.setTipoMensagem(TipoMensagem.DETRAN);
//                    mensagem.setSucesso(body.getCodResposta().equals("0"));
//                }

                return getErroPadrao(getUf());
            }).orElse(getErroPadrao(getUf()));
        } catch (ParseException e) {
            throw new ServiceException("Falha na geração da requisição.");
        } catch (HttpClientErrorException e) {
            log.error(e.getMessage());
            throw new ServiceException("Falha no envio do contrato.");
        }
    }

    public RetornoContratoDetranDTO consultarContratoChassi(String chassi, String cnpjAgente, String numeroContrato) throws Exception {
        Financeira financeira = financeiraRepository.findByDocumento(cnpjAgente);
        String resultado = obterToken(financeira).map(token -> {

//            Veiculo veiculo = veiculoRepository.findFirstByNumeroChassi(chassi);
//            if (Objects.isNull(veiculo) || Strings.isNullOrEmpty(veiculo.getNumeroRegistroDetran())) {
//                throw new ServiceException("Veículo não existe no Placecon ou não possui número de confirmação do Detran");
//            }

            HttpHeaders headers = getHttpHeaders(null);
            headers.setAccept(Collections.singletonList(MediaType.ALL));
            headers.set("Authorization", token.getTokenType() + " " + token.getAccessToken());
            String uri = getUrl() + URL_CONSULTA_CONTRATO;
            String url = uri.replace("{chassi}", chassi);


            RestTemplate restTemplate = new RestTemplate();
            HttpEntity<ConsultaResponse> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    ConsultaResponse.class
            );

            ConsultaResponse body = response.getBody();
            if (body != null && body.getCodigo().equals("0")) {
                return body.getResultado();
            }
            throw new ServiceException("Resposta invalida na consulta do contrato.");
        }).orElseThrow(() -> new ServiceException("Falha na consulta do contrato."));

        log.info(resultado);

        return null;
    }

    @Override
    public MensagemRetornoDTO envioImagem(Veiculo veiculo, String referenciaArquivo) {
        long start = System.nanoTime();
        Contrato contrato = veiculo.getContrato();

        MultiValueMap<String, String> request = new LinkedMultiValueMap<>();
        request.add("base64", obterBase64(referenciaArquivo));

        EnvioImagemResponse responseEntity = null;
//O arquivo de imagem deve estar em formato PDF e o tamanho máximo não pode ultrapassar 50mb.
//        ➢ Tipo do Corpo: x-www-form-urlencoded (PDF) base64: <base64_encoded_string>

        try {
            log.info("Gerando requisição - Detran AL");

            LoginOAuthResponse token = obterToken(contrato.getFinanceira())
                    .orElseThrow(() -> new ServiceException("Falha ao gerar token para enviar a imagem para o Detran AL"));

            HttpHeaders headers = getHttpHeaders(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setAccept(Collections.singletonList(MediaType.ALL));
            headers.set("Authorization", token.getTokenType() + " " + token.getAccessToken());
            String uri = getUrl() + URL_ENVIO_IMAGEM;
            String url = uri.replace("{chassi}", veiculo.getNumeroChassi());


            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactoryWithSSL());
            HttpEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.PUT,
                    new HttpEntity<>(request, headers),
                    String.class
            );
            log.info("Imagem enviada - Detran AL " + response);

            responseEntity = PlaceconUtil.jsonParaObject(response.getBody(), EnvioImagemResponse.class)
                    .orElseThrow(() -> new ServiceException("Falha na deserialização do token."));

            if (response.getBody() != null) {
                log.info(responseEntity.toString());
                MensagemRetornoDTO mensagem = new MensagemRetornoDTO();
                mensagem.setUf(getUf());
                mensagem.setNumeroDetran(String.valueOf(responseEntity.getNumeroDocumentoEletronico()));
                mensagem.setCodigo(responseEntity.getCodResposta());
                mensagem.setDescricao(responseEntity.getMsgResposta());
                mensagem.setNumeroDetran(responseEntity.getHashOperacao());
                mensagem.setTipoMensagem(TipoMensagem.DETRAN);
                return mensagem;
            }

            return getErroPadrao(getUf());

        } catch (HttpClientErrorException e) {
            log.error(e.getMessage());
            return getErroPadrao(getUf());
        } finally {
            registrarLog(contrato, start, referenciaArquivo, responseEntity);
        }
    }
}
