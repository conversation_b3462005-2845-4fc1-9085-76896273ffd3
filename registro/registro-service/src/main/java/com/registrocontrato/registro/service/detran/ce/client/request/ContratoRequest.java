package com.registrocontrato.registro.service.detran.ce.client.request;

import com.registrocontrato.infra.entity.Uf;

import java.math.BigDecimal;

public class ContratoRequest {

    private String bairroFinanciado;
    private String cepFinanciado;
    private String chassi;
    private String codigoMunicipioFinanciado;
    private String codigoMunicipioPagamento;
    private String complementoFinancado;
    private String cpfCnpjFinanciado;
    private String dataContrato;
    private String dataPagamento;
    private String emailFinanciado;
    private String epocaPagamento;
    private String logradouroFinanciado;
    private String nomeFinanciado;
    private Integer numeroRestricaoGravame;
    private Integer quantidadeParcelas;
    private String numeroContrato;
    private Double taxaJuros;
    private String telefoneCel;
    private String telefoneFixo;
    private Uf ufFinanciado;
    private BigDecimal valorTotalDivida;


    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public String getBairroFinanciado() {
        return bairroFinanciado;
    }

    public void setBairroFinanciado(String bairroFinanciado) {
        this.bairroFinanciado = bairroFinanciado;
    }

    public String getCepFinanciado() {
        return cepFinanciado;
    }

    public void setCepFinanciado(String cepFinanciado) {
        this.cepFinanciado = cepFinanciado;
    }

    public String getChassi() {
        return chassi;
    }

    public void setChassi(String chassi) {
        this.chassi = chassi;
    }

    public String getCodigoMunicipioFinanciado() {
        return codigoMunicipioFinanciado;
    }

    public void setCodigoMunicipioFinanciado(String codigoMunicipioFinanciado) {
        this.codigoMunicipioFinanciado = codigoMunicipioFinanciado;
    }

    public String getCodigoMunicipioPagamento() {
        return codigoMunicipioPagamento;
    }

    public void setCodigoMunicipioPagamento(String codigoMunicipioPagamento) {
        this.codigoMunicipioPagamento = codigoMunicipioPagamento;
    }

    public String getComplementoFinancado() {
        return complementoFinancado;
    }

    public void setComplementoFinancado(String complementoFinancado) {
        this.complementoFinancado = complementoFinancado;
    }

    public String getCpfCnpjFinanciado() {
        return cpfCnpjFinanciado;
    }

    public void setCpfCnpjFinanciado(String cpfCnpjFinanciado) {
        this.cpfCnpjFinanciado = cpfCnpjFinanciado;
    }

    public String getDataContrato() {
        return dataContrato;
    }

    public void setDataContrato(String dataContrato) {
        this.dataContrato = dataContrato;
    }

    public String getDataPagamento() {
        return dataPagamento;
    }

    public void setDataPagamento(String dataPagamento) {
        this.dataPagamento = dataPagamento;
    }

    public String getEmailFinanciado() {
        return emailFinanciado;
    }

    public void setEmailFinanciado(String emailFinanciado) {
        this.emailFinanciado = emailFinanciado;
    }

    public String getEpocaPagamento() {
        return epocaPagamento;
    }

    public void setEpocaPagamento(String epocaPagamento) {
        this.epocaPagamento = epocaPagamento;
    }

    public String getLogradouroFinanciado() {
        return logradouroFinanciado;
    }

    public void setLogradouroFinanciado(String logradouroFinanciado) {
        this.logradouroFinanciado = logradouroFinanciado;
    }

    public String getNomeFinanciado() {
        return nomeFinanciado;
    }

    public void setNomeFinanciado(String nomeFinanciado) {
        this.nomeFinanciado = nomeFinanciado;
    }

    public Integer getNumeroRestricaoGravame() {
        return numeroRestricaoGravame;
    }

    public void setNumeroRestricaoGravame(Integer numeroRestricaoGravame) {
        this.numeroRestricaoGravame = numeroRestricaoGravame;
    }

    public Integer getQuantidadeParcelas() {
        return quantidadeParcelas;
    }

    public void setQuantidadeParcelas(Integer quantidadeParcelas) {
        this.quantidadeParcelas = quantidadeParcelas;
    }

    public Double getTaxaJuros() {
        return taxaJuros;
    }

    public void setTaxaJuros(Double taxaJuros) {
        this.taxaJuros = taxaJuros;
    }

    public String getTelefoneCel() {
        return telefoneCel;
    }

    public void setTelefoneCel(String telefoneCel) {
        this.telefoneCel = telefoneCel;
    }

    public String getTelefoneFixo() {
        return telefoneFixo;
    }

    public void setTelefoneFixo(String telefoneFixo) {
        this.telefoneFixo = telefoneFixo;
    }

    public Uf getUfFinanciado() {
        return ufFinanciado;
    }

    public void setUfFinanciado(Uf ufFinanciado) {
        this.ufFinanciado = ufFinanciado;
    }

    public BigDecimal getValorTotalDivida() {
        return valorTotalDivida;
    }

    public void setValorTotalDivida(BigDecimal valorTotalDivida) {
        this.valorTotalDivida = valorTotalDivida;
    }

}
