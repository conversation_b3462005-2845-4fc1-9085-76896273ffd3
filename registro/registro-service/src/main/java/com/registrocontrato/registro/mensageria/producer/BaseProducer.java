package com.registrocontrato.registro.mensageria.producer;

import org.slf4j.Logger;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;


@Service
public abstract class BaseProducer<T> {

    abstract String getTopico();
    abstract KafkaTemplate<String, T> getKafkaTemplate();
    abstract Logger getLogger();

    public void send(T conteudo) {
        getLogger().info(String.format("Payload enviado: %s", conteudo));
        Message<T> mensagem = criarMensagem(conteudo);
        getKafkaTemplate().send(mensagem);
    }

    private Message<T> criarMensagem(T conteudo) {
        return MessageBuilder
                .withPayload(conteudo)
                .setHeader(KafkaHeaders.TOPIC, getTopico())
                .build();
    }
}
