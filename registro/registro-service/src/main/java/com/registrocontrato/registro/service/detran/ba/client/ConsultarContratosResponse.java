//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.registrocontrato.registro.service.detran.ba.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(
        name = "",
        propOrder = {"consultarContratosResult"}
)
@XmlRootElement(
        name = "consultarContratosResponse"
)
public class ConsultarContratosResponse {
    protected ConsultarContratosOutput consultarContratosResult;

    public ConsultarContratosResponse() {
    }

    public ConsultarContratosOutput getConsultarContratosResult() {
        return this.consultarContratosResult;
    }

    public void setConsultarContratosResult(ConsultarContratosOutput var1) {
        this.consultarContratosResult = var1;
    }
}
