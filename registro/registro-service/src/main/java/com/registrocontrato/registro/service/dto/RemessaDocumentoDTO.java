package com.registrocontrato.registro.service.dto;

import com.registrocontrato.infra.entity.Financeira;

import java.io.Serializable;
import java.util.Date;

public class RemessaDocumentoDTO implements Serializable {

    private static final long serialVersionUID = 8528281131820186925L;
    private Long idArquivo;

    private String nomeArquivo;

    private String cnpjFinanceira;

    private String hash;

    private String usuario;

    private Date dataInicio;

    private Date dataFim;

    private Financeira financeira;

    private String chassi;

    public String getNomeArquivo() {
        return nomeArquivo;
    }

    public void setNomeArquivo(String nomeArquivo) {
        this.nomeArquivo = nomeArquivo;
    }

    public String getUsuario() {
        return usuario;
    }

    public void setUsuario(String usuario) {
        this.usuario = usuario;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public Long getIdArquivo() {
        return idArquivo;
    }

    public void setIdArquivo(Long idArquivo) {
        this.idArquivo = idArquivo;
    }

    public String getCnpjFinanceira() {
        return cnpjFinanceira;
    }

    public void setCnpjFinanceira(String cnpjFinanceira) {
        this.cnpjFinanceira = cnpjFinanceira;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }

    public String getChassi() { return chassi;}

    public void setChassi(String chassi) { this.chassi = chassi; }
}
