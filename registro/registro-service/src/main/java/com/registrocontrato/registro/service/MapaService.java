package com.registrocontrato.registro.service;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.SituacaoFinanceiraEstado;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class MapaService {

    private static final long serialVersionUID = 1L;

    private static String YELLOW_COLOR = "#e2ab3b";
    private static String GREEN_COLOR = "#aaf0b3";
    private static String GREY_COLOR = "#ddd";
    private static String RED_COLOR = "#d57171";

    private Map<Uf, Long> mapDias = new HashMap<>();
    private Map<Uf, String> mapLabels = new HashMap<>();

    @Autowired
    private FinanceiraService financeiraService;
    @Autowired
    private DashboardService dashboardService;

    public void init(List<Financeira> financeiras) {
        mapLabels = new HashMap<>();
        mapDias = new HashMap<>();
        for (Financeira f : financeiras) {
            inserirValores(f);
        }
    }

    public void createMapaFinanceira(Financeira f, List<Financeira> financeirasDefault) {
        if (Objects.isNull(f)) {
            init(financeirasDefault);
            return;
        }
        mapLabels = new HashMap<>();
        mapDias = new HashMap<>();
        f.setSituacoesFinanceiraEstado(financeiraService.findSituacoesFinanceiraEstado(f));
        inserirValores(f);
    }

    public void inserirValores(Financeira f) {
        long diaRestantes = 0l;
        for (SituacaoFinanceiraEstado sit : f.getSituacoesFinanceiraEstado()) {
            long diff = sit.getDataFimCredenciamentoDetran().getTime() - Calendar.getInstance().getTimeInMillis();
            diaRestantes = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
            String nome = montaNomeAbreviado(f);

            if (mapLabels.get(sit.getUf()) == null) {
                mapLabels.put(sit.getUf(), nome + getTextoLabelEstado(diaRestantes));
            } else {
                mapLabels.put(sit.getUf(), mapLabels.get(sit.getUf()) + "<br/>" + nome + getTextoLabelEstado(diaRestantes));
            }

            if (mapDias.get(sit.getUf()) == null || diaRestantes > mapDias.get(sit.getUf())) {
                mapDias.put(sit.getUf(), diaRestantes);

            }
        }
    }


    private String montaNomeAbreviado(Financeira f) {
        String nome = f.getNome();
        if (f.getNome().length() > 20) {
            nome = PlaceconUtil.stringMax(f.getNome(), 17);
            nome = nome + "...";
        }
        return nome;
    }

    private String getTextoLabelEstado(long diaRestantes) {
        if (diaRestantes < 0) {
            return " - Expirou a " + diaRestantes * -1 + " dias";
        } else {
            return " - " + diaRestantes + " dias restantes.";
        }
    }

    public String getColors() {
        StringBuilder string = new StringBuilder("{ ");
        for (Uf uf : Uf.listar()) {
            String color = GREY_COLOR;
            if (mapDias.get(uf) != null) {
                if (dashboardService.getEstadosVitalicios().contains(uf)) {
                   color = GREEN_COLOR;
                } else if (mapDias.get(uf) <= 30) {
                    color = RED_COLOR;
                } else if (mapDias.get(uf) <= 90) {
                    color = YELLOW_COLOR;
                } else if (mapDias.get(uf) > 90) {
                    color = GREEN_COLOR;
                }
            }
            string.append(" " + uf.name().toLowerCase() + ": '" + color + "',");
        }
        string.append(" }");
        return string.toString();
    }

    public String getPins() {
        StringBuilder string = new StringBuilder("{ ");
        for (Uf uf : Uf.listar()) {
            if (mapLabels.get(uf) == null) {
                string.append(" " + uf.name().toLowerCase() + ": 'Estado sem credenciamento.',");
            } else if (dashboardService.getEstadosVitalicios().contains(uf)) {
                string.append(" " + uf.name().toLowerCase() + ": 'Credenciamento sem prazo de validade',");
            } else {
                string.append(" " + uf.name().toLowerCase() + ": escapeXml('" + mapLabels.get(uf) + "'),");
            }
        }
        string.append(" }");
        return string.toString();
    }
}
