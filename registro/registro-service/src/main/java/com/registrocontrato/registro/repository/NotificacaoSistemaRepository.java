package com.registrocontrato.registro.repository;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.NotificacaoSistema;
import com.registrocontrato.seguranca.entity.Usuario;

@Repository
public interface NotificacaoSistemaRepository extends BaseRepository<NotificacaoSistema> {

	
	@Query("from NotificacaoSistema n where (n.uf = :#{#usuario.uf} or n.uf is null or (select x.uf from Usuario x where x = :usuario) is null) "
			+ "and n.dataInicio <= current_date and n.dataFim >= current_date "
			+ "and (n.financeira is null or n.financeira in (select f from Usuario u join u.financeiras f where u = :usuario) )"
			+ "and not exists (select 1 from NotificacaoSistemaUsuario nsu where nsu.notificacao = n and nsu.cpf = :#{#usuario.cpf})")
	public List<NotificacaoSistema>findNaoLidas(@Param("usuario")Usuario usuario, Pageable pageable);
	
	
	@Query("from NotificacaoSistema n where exists (select 1 from NotificacaoSistemaUsuario nsu where nsu.cpf = :#{#usuario.cpf} and nsu.notificacao = n)")
	public List<NotificacaoSistema>findLidas(@Param("usuario")Usuario usuario, Pageable pageable);
	
	
	@Query("select case when count(*) > 0 then true else false end from NotificacaoSistema n "
			+ "where n.id = :idNotificacao "
			+ "and (n.uf = :#{#usuario.uf} or n.uf is null or (select x.uf from Usuario x where x = :usuario) is null) "
			+ "and n.dataInicio <= current_date and n.dataFim >= current_date "
			+ "and (n.financeira is null or n.financeira in (select f from Usuario u join u.financeiras f where u = :usuario) )")
	public boolean isVisivel(@Param("usuario")Usuario usuario, @Param("idNotificacao") Long idNotificacao);
}
