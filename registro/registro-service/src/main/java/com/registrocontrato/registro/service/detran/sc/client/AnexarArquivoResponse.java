//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.03.16 at 09:13:47 PM BRT 
//


package com.registrocontrato.registro.service.detran.sc.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AnexarArquivoResult" type="{http://webservicesh.sc.gov.br/detran/RegistroContrato}ResultadoAnexarArquivo" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "anexarArquivoResult"
})
@XmlRootElement(name = "AnexarArquivoResponse")
public class AnexarArquivoResponse {

    @XmlElement(name = "AnexarArquivoResult")
    protected ResultadoAnexarArquivo anexarArquivoResult;

    /**
     * Gets the value of the anexarArquivoResult property.
     * 
     * @return
     *     possible object is
     *     {@link ResultadoAnexarArquivo }
     *     
     */
    public ResultadoAnexarArquivo getAnexarArquivoResult() {
        return anexarArquivoResult;
    }

    /**
     * Sets the value of the anexarArquivoResult property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultadoAnexarArquivo }
     *     
     */
    public void setAnexarArquivoResult(ResultadoAnexarArquivo value) {
        this.anexarArquivoResult = value;
    }

}
