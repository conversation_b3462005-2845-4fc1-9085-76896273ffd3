package com.registrocontrato.registro.service;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Service;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.Marca;
import com.registrocontrato.registro.repository.MarcaRepository;
import com.registrocontrato.registro.service.dto.MarcaDTO;

import java.util.Optional;

@Service
public class MarcaService extends BaseService<Marca, MarcaDTO> {

	private static final long serialVersionUID = 1L;

	@Autowired
	private MarcaRepository marcaRepository;

	@Override
	public Page<Marca> findAll(int first, int pageSize, MarcaDTO filter) {
		Marca entity = new Marca();
		BeanUtils.copyProperties(filter, entity);
		ExampleMatcher matcher = ExampleMatcher.matching()
				.withMatcher("codigoDenatran", match -> match.contains().ignoreCase())
				.withMatcher("descricao", match -> match.contains().ignoreCase());

		return marcaRepository.findAll(Example.of(entity, matcher), new PageRequest(first / pageSize, pageSize, new Sort(Direction.ASC, "descricao")));
	}

	@Override
	public void save(Marca entity) throws ServiceException {
		Marca duplicado = marcaRepository.findTop1ByDescricaoIgnoreCaseOrderById(entity.getDescricao());
		if (duplicado != null && !duplicado.getId().equals(entity.getId())) {
			throw new ServiceException("Marca já cadastrado com a descrição: " + entity.getDescricao());
		}
		super.save(entity);
	}

	@Override
	protected PagingAndSortingRepository<Marca, Long> getRepository() {
		return marcaRepository;
	}


	public Marca findByMarcaId(Long id){
		Optional<Marca> marca = marcaRepository.findByMarcaId(id);
		if (marca.isPresent()){
			return marca.get();
		}
		return null;
	};

}
