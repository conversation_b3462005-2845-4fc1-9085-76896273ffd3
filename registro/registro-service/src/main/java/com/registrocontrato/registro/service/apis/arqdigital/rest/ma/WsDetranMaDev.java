package com.registrocontrato.registro.service.apis.arqdigital.rest.ma;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.apis.arqdigital.MontarRequisicao;
import com.registrocontrato.registro.service.apis.arqdigital.request.ContratoRequest;
import com.registrocontrato.registro.service.apis.arqdigital.rest.WsDetranArqDigital;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

@Service
@Profile({"desenv", "homol"})
public class WsDetranMaDev extends WsDetranArqDigital {

    public WsDetranMaDev(RestTemplateBuilder restTemplateBuilder,
                         MensagemRetornoRepository mensagemRetornoRepository,
                         RegistroEnvioService registroEnvioService,
                         AcessoSenhaService acessoSenhaService,
                         FinanceiraRepository financeiraRepository,
                         UsuarioService usuarioService) {
        super(restTemplateBuilder,
                mensagemRetornoRepository,
                registroEnvioService,
                acessoSenhaService,
                financeiraRepository,
                usuarioService);
    }

    @Override
    public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
        long start = System.nanoTime();
        ContratoRequest request = new MontarRequisicao().montarRequest(contrato);
        if(veiculo.getNumeroChassi().startsWith("9") || contrato.getIdProcessoB3() != null) {
            registrarLog(contrato, start, request, "SUCESSO - HOMOL");
            return getSucesso(contrato.getUfRegistro());
        }
        registrarLog(contrato, start, request, "ERRO AO REGISTRAR CONTRATO - HOMOL");
        return getErro(getUf(), "Erro ao enviar contrato - HOMOLOGAÇÃO");
    }

    public Uf getUf() {
        return Uf.MA;
    }

}
