package com.registrocontrato.registro.service.dto.migracao;

import com.registrocontrato.infra.entity.*;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Marca;
import com.registrocontrato.registro.entity.Modelo;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.enums.*;
import com.registrocontrato.registro.repository.IntegradoraRepository;
import com.registrocontrato.registro.repository.MarcaRepository;
import com.registrocontrato.registro.repository.ModeloRepository;
import com.registrocontrato.registro.repository.MunicipioRepository;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.registrocontrato.infra.util.PlaceconUtil.getEnumName;
import static com.registrocontrato.infra.util.PlaceconUtil.simNaoToBoolean;

@Component
public class ContratoMigracaoMapper {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private MunicipioRepository municipioRepository;

    @Autowired
    private FinanceiraRepository financeiraRepository;

    @Autowired
    private MarcaRepository marcaRepository;

    @Autowired
    private ModeloRepository modeloRepository;

    @Autowired
    private IntegradoraRepository integradoraRepository;

    public ContratoMigracaoDTO contratoToMigracaoDto(Contrato contrato) {
        ContratoMigracaoDTO dto = new ContratoMigracaoDTO();
        BeanUtils.copyProperties(contrato, dto, "veiculos", "id");

        dto.setMunicipioDevedor_cod(Objects.nonNull(contrato.getMunicipioDevedor()) ? contrato.getMunicipioDevedor().getCodigoDenatran() : null);
        dto.setMunicipioGarantidor_cod(Objects.nonNull(contrato.getMunicipioGarantidor()) ? contrato.getMunicipioGarantidor().getCodigoDenatran() : null);
        dto.setMunicipioLiberacao_cod(Objects.nonNull(contrato.getMunicipioLiberacao()) ? contrato.getMunicipioLiberacao().getCodigoDenatran() : null);

        dto.setUfRegistro_name(getEnumName(contrato.getUfRegistro()));
        dto.setUfLiberacaoCredito_name(getEnumName(contrato.getUfLiberacaoCredito()));
        dto.setUfEnderecoDevedor_name(getEnumName(contrato.getUfEnderecoDevedor()));
        dto.setUfEnderecoGarantidor_name(getEnumName(contrato.getUfEnderecoGarantidor()));

        dto.setTipoContrato_name(getEnumName(contrato.getTipoContrato()));
        dto.setTipoRestricao_name(getEnumName(contrato.getTipoRestricao()));
        dto.setTipoBaixaContrato_name(getEnumName(contrato.getTipoBaixaContrato()));
        dto.setTipoVrg_name(getEnumName(contrato.getTipoVrg()));
        dto.setSiglaIndiceFinaceiro_name(getEnumName(contrato.getSiglaIndiceFinaceiro()));
        dto.setPossuiGarantidor_name(simNaoToBoolean(contrato.getPossuiGarantidor()));
        dto.setSituacao_name(getEnumName(contrato.getSituacao()));
        dto.setAprovadoAuditoria_name(getEnumName(contrato.getAprovadoAuditoria()));
        dto.setAssinado_name(getEnumName(contrato.getAssinado()));
        dto.setSituacaoFinanceira_name(getEnumName(contrato.getSituacaoFinanceira()));
        if (contrato.getIntegradora() != null)
            dto.setIntegradoras_id(contrato.getIntegradora().getId());

        dto.setFinanceira_id(contrato.getFinanceira().getId());

        logger.info("INFORMANDO VEICULOS");
        List<VeiculoMigracaoDTO> veiculosDto = contrato.getVeiculos().stream()
                .map(this::veiculoToMigracaoDto)
                .collect(Collectors.toList());
        dto.setVeiculos(veiculosDto);

        return dto;
    }

    public VeiculoMigracaoDTO veiculoToMigracaoDto(Veiculo veiculo) {
        VeiculoMigracaoDTO dto = new VeiculoMigracaoDTO();
        BeanUtils.copyProperties(veiculo, dto, "marca", "modelo", "id");

        dto.setMarca_id(Objects.nonNull(veiculo.getMarca()) ? veiculo.getMarca().getId() : null);
        dto.setModelo_id(Objects.nonNull(veiculo.getModelo()) ? veiculo.getModelo().getId() : null);
        dto.setTipo_name(getEnumName(veiculo.getTipo()));
        dto.setMensagemRetorno_id(Objects.nonNull(veiculo.getMensagemRetorno()) ? veiculo.getMensagemRetorno().getId() : null);
        dto.setUf_name(Objects.nonNull(veiculo.getUf()) ? veiculo.getUf().name() : null);
        dto.setChassiRemarcado(veiculo.getChassiRemarcado());
        dto.setVeiculo0Km(veiculo.getVeiculo0Km());
        dto.setPlaca(veiculo.getPlaca());
        dto.setNumeroRenavam(veiculo.getNumeroRenavam());
        dto.setAnoFabricacao(veiculo.getAnoFabricacao());
        dto.setAnoModelo(veiculo.getAnoModelo());

        return dto;
    }

    public Contrato contratoDtoToEntity(ContratoMigracaoDTO contrato) {
        Contrato entity = new Contrato();
        BeanUtils.copyProperties(contrato, entity, "veiculos", "anexos", "id");

        Municipio municipioDevedor = municipioRepository.findByCodigoDenatran(contrato.getMunicipioDevedor_cod());
        Municipio municipioLiberacao = municipioRepository.findByCodigoDenatran(contrato.getMunicipioLiberacao_cod());
        Municipio municipioGarantidor = municipioRepository.findByCodigoDenatran(contrato.getMunicipioGarantidor_cod());

        Uf ufRegistro = Uf.getEnum(contrato.getUfRegistro_name());
        Uf ufEnderecoDevedor = Uf.getEnum(contrato.getUfEnderecoDevedor_name());
        Uf ufLiberacaoCredito = Uf.getEnum(contrato.getUfLiberacaoCredito_name());
        Uf ufEnderecoGarantidor = Uf.getEnum(contrato.getUfEnderecoGarantidor_name());

        TipoRestricao tipoRestricao = TipoRestricao.getByName(contrato.getTipoRestricao_name());
        TipoContrato tipoContrato = TipoContrato.getByName(contrato.getTipoContrato_name());
        IndiceFinanceiro siglaIndiceFinaceiro = IndiceFinanceiro.getByName(contrato.getSiglaIndiceFinaceiro_name());
        TipoVrg tipoVrg = TipoVrg.getByName(contrato.getTipoVrg_name());

        TipoBaixaContrato tipoBaixa = TipoBaixaContrato.getByName(contrato.getTipoBaixaContrato_name());
        Situacao situacao = Situacao.getByName(contrato.getSituacao_name());
        SituacaoFinanceira situacaoFinanceira = SituacaoFinanceira.getByName(contrato.getSituacaoFinanceira_name());
        SimNao aprovadoAuditoria = SimNao.getByName(contrato.getAprovadoAuditoria_name());
        SimNao assinado = SimNao.getByName(contrato.getAssinado_name());

        Financeira financeira = financeiraRepository.findOne(contrato.getFinanceira_id());
        entity.setFinanceira(financeira);
        //TODO: se não encontratrar a financeira, buscar no placecon da winov

        entity.setTipoBaixaContrato(tipoBaixa);
        entity.setSituacao(situacao);
        entity.setSituacaoFinanceira(situacaoFinanceira);
        entity.setAprovadoAuditoria(aprovadoAuditoria);
        entity.setAssinado(assinado);
        entity.setTipoContrato(tipoContrato);
        entity.setTipoRestricao(tipoRestricao);
        entity.setSiglaIndiceFinaceiro(siglaIndiceFinaceiro);
        entity.setMunicipioLiberacao(municipioLiberacao);
        entity.setMunicipioDevedor(municipioDevedor);
        entity.setMunicipioGarantidor(municipioGarantidor);
        entity.setUfEnderecoDevedor(ufEnderecoDevedor);
        entity.setUfLiberacaoCredito(ufLiberacaoCredito);
        entity.setUfRegistro(ufRegistro);
        entity.setUfEnderecoGarantidor(ufEnderecoGarantidor);
        entity.setTipoVrg(tipoVrg);

        Integradora integradora = integradoraRepository.findOne(contrato.getFinanceira_id());
        entity.setIntegradora(integradora);

        // 3o garantidor
        entity.setPossuiGarantidor(contrato.isPossuiGarantidor_name() ? SimNao.S : SimNao.N);
        //TODO: preencher terceiro garantidor

        if (Objects.nonNull(contrato.getDataAditivoContrato()) && StringUtils.isNotEmpty(contrato.getDataAditivoContrato().toString()) && isAditivo(contrato))
            entity.setDataAditivoContrato(contrato.getDataAditivoContrato());
        if (Objects.nonNull(contrato.getNumeroAditivoContrato()) && StringUtils.isNotEmpty(contrato.getNumeroAditivoContrato()) && isAditivo(contrato))
            entity.setNumeroAditivoContrato(contrato.getNumeroAditivoContrato());

        // veiculo ou frota
        Veiculo veiculo = null;
        for (VeiculoMigracaoDTO v : contrato.getVeiculos()) {
            veiculo = veiculoDtoToVeiculo(v, entity);
            entity.getVeiculos().add(veiculo);
        }

        return entity;
    }

    public Veiculo veiculoDtoToVeiculo(VeiculoMigracaoDTO v, Contrato contrato) {
        Veiculo veiculo = new Veiculo();
        BeanUtils.copyProperties(v, veiculo, "id");

        if (v.getMarca_id() != null) {
            Marca marca = marcaRepository.findOne(v.getMarca_id());
            veiculo.setMarca(marca);
        }

        if (v.getModelo_id() != null) {
            Modelo modelo = modeloRepository.findOne(v.getModelo_id());
            veiculo.setModelo(modelo);
        }
        //TODO: se não encontratrar, buscar no placecon da winov

        veiculo.setTipo(v.getTipo_name() != null ? TipoVeiculo.valueOf(v.getTipo_name()) : null);
        veiculo.setContrato(contrato);
        veiculo.setUf(Uf.getEnum(v.getUf_name()));
        veiculo.setChassiRemarcado(v.getChassiRemarcado());
        veiculo.setTipo(TipoVeiculo.getByName(v.getTipo_name()));
        veiculo.setVeiculo0Km(veiculo.getVeiculo0Km());
        veiculo.setPlaca(v.getPlaca());
        veiculo.setNumeroRenavam(v.getNumeroRenavam());
        veiculo.setAnoModelo(v.getAnoModelo());
        veiculo.setAnoFabricacao(v.getAnoFabricacao());

        return veiculo;
    }

    private Boolean isAditivo(ContratoMigracaoDTO contrato) {
        return !contrato.getTipoContrato_name().equals(TipoContrato.CONTRATO_PRINCIPAL.name()) && !contrato.getTipoContrato_name().equals(TipoContrato.NAO_IDENTIFICADO.name());
    }


}
