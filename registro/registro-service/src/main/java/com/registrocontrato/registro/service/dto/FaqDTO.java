package com.registrocontrato.registro.service.dto;

import com.registrocontrato.infra.entity.SimNao;

public class FaqDTO {

	private String pergunta;
	private String resposta;
	private SimNao ativo;
	public String getPergunta() {
		return pergunta;
	}
	public void setPergunta(String pergunta) {
		this.pergunta = pergunta;
	}
	public String getResposta() {
		return resposta;
	}
	public void setResposta(String resposta) {
		this.resposta = resposta;
	}
	public SimNao getAtivo() {
		return ativo;
	}
	public void setAtivo(SimNao ativo) {
		this.ativo = ativo;
	}
	
	
	
}
