package com.registrocontrato.registro.service.detran.al.client;

public class VeiculosALRequest {

    private String numeroChassi;
    private Integer identificadorRemarcacao;
    private String ufRegistroGravame;
    private String ufPlacaAtual;
    private String placa;
    private Integer numeroRenavam;
    private Integer numeroGravame;
    private Integer anoFabricacao;
    private Integer anoModelo;
    private Integer codEspecie;

    public String getNumeroChassi() {
        return numeroChassi;
    }

    public void setNumeroChassi(String numeroChassi) {
        this.numeroChassi = numeroChassi;
    }

    public Integer getIdentificadorRemarcacao() {
        return identificadorRemarcacao;
    }

    public void setIdentificadorRemarcacao(Integer identificadorRemarcacao) {
        this.identificadorRemarcacao = identificadorRemarcacao;
    }

    public String getUfRegistroGravame() {
        return ufRegistroGravame;
    }

    public void setUfRegistroGravame(String ufRegistroGravame) {
        this.ufRegistroGravame = ufRegistroGravame;
    }

    public String getUfPlacaAtual() {
        return ufPlacaAtual;
    }

    public void setUfPlacaAtual(String ufPlacaAtual) {
        this.ufPlacaAtual = ufPlacaAtual;
    }

    public String getPlaca() {
        return placa;
    }

    public void setPlaca(String placa) {
        this.placa = placa;
    }

    public Integer getNumeroRenavam() {
        return numeroRenavam;
    }

    public void setNumeroRenavam(Integer numeroRenavam) {
        this.numeroRenavam = numeroRenavam;
    }

    public Integer getNumeroGravame() {
        return numeroGravame;
    }

    public void setNumeroGravame(Integer numeroGravame) {
        this.numeroGravame = numeroGravame;
    }

    public Integer getAnoFabricacao() {
        return anoFabricacao;
    }

    public void setAnoFabricacao(Integer anoFabricacao) {
        this.anoFabricacao = anoFabricacao;
    }

    public Integer getAnoModelo() {
        return anoModelo;
    }

    public void setAnoModelo(Integer anoModelo) {
        this.anoModelo = anoModelo;
    }

    public Integer getCodEspecie() {
        return codEspecie;
    }

    public void setCodEspecie(Integer codEspecie) {
        this.codEspecie = codEspecie;
    }
}
