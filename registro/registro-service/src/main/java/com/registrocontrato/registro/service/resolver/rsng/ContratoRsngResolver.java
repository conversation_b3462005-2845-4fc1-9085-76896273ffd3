package com.registrocontrato.registro.service.resolver.rsng;

import com.registrocontrato.infra.entity.CampoFormularioRemessa;
import com.registrocontrato.infra.entity.CampoTemplateRemessa;
import com.registrocontrato.infra.entity.Municipio;
import com.registrocontrato.infra.entity.TipoCampoFormularioRemessa;
import com.registrocontrato.infra.entity.rsng.CampoFormularioRsngRemessa;
import com.registrocontrato.infra.entity.rsng.CampoTemplateRsngRemessa;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.entity.VeiculoRsng;
import com.registrocontrato.registro.enums.IndiceFinanceiro;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.registro.repository.MunicipioRepository;
import com.registrocontrato.registro.service.resolver.CampoTemplateResolver;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ContratoRsngResolver implements CampoTemplateRsngResolver {

	@Autowired
	private MunicipioRepository municipioRepository;

	@Override
	public void put(VeiculoRsng veiculo, CampoTemplateRsngRemessa c, String valor) {
		if (!validateRequired(veiculo, c, valor)) {
			return;
		}

		if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.codigoMunicipioLiberacaoCredito) {
			Municipio municipio = municipioRepository.findByCodigoDenatran(valor);
			if (municipio == null) {
				putError("Código do Município da Liberação de Crédito Inválido", veiculo);
				return;
			}
			veiculo.getContratoRsng().setMunicipioLiberacao(municipio);
			veiculo.getContratoRsng().setUfLiberacaoCredito(municipio.getUf());
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.comentario) {
			veiculo.getContratoRsng().setComentario(getString(valor));
		}
		else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.dataContrato) {
			veiculo.getContratoRsng().setDataContrato(getDataFormato(c, valor, veiculo));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.dataLiberacaoCredito) {
			veiculo.getContratoRsng().setDataLiberacaoCredito(getDataFormato(c, valor, veiculo));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.dataVencimentoPrimeiraParcela) {
			veiculo.getContratoRsng().setDataVencimentoPrimeiraParcela(getDataFormato(c, valor, veiculo));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.dataVencimentoUltimaParcela) {
			veiculo.getContratoRsng().setDataVencimentoUltimaParcela(getDataFormato(c, valor, veiculo));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.descricaoPenalidade) {
			if(veiculo.getContratoRsng().getIndicadorPenalidade() != null) {
				if(veiculo.getContratoRsng().getIndicadorPenalidade()) {
					veiculo.getContratoRsng().setDescricaoPenalidade(getString(valor));
				} else {
					veiculo.getContratoRsng().setDescricaoPenalidade(null);
				}
			} else {
				veiculo.getContratoRsng().setDescricaoPenalidade(getString(valor));
				veiculo.getContratoRsng().setIndicadorPenalidade(StringUtils.isNotBlank(veiculo.getContratoRsng().getDescricaoPenalidade()));
			}
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.indicadorComissao) {
			veiculo.getContratoRsng().setIndicadorComissao(getBoolean(valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.indicadorPenalidade) {
			veiculo.getContratoRsng().setIndicadorPenalidade(getBoolean(valor));
			if(!veiculo.getContratoRsng().getIndicadorPenalidade()) {
				veiculo.getContratoRsng().setDescricaoPenalidade(null);
			}
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.indicadorTaxaMora) {
			veiculo.getContratoRsng().setIndicadorTaxaMoraDia(getBoolean(valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.indicadorTaxaMulta) {
			veiculo.getContratoRsng().setIndicadorTaxaMulta(getBoolean(valor));
		}
		else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.numeroContrato) {
			veiculo.getContratoRsng().setNumeroContrato(getString(valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.numeroCotaConsorcio) {
			veiculo.getContratoRsng().setNumeroCotaConsorcio(getLong(c, valor, veiculo));
			if(veiculo.getContratoRsng().getNumeroCotaConsorcio() != null && veiculo.getContratoRsng().getNumeroCotaConsorcio() > 0) {
				veiculo.getContratoRsng().setTipoRestricao(TipoRestricao.ALIENACAO_FIDUCIARIA_CONS);
			}
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.numeroGrupoConsorcio) {
			veiculo.getContratoRsng().setNumeroGrupoConsorcio(getString(valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.percentualComissao) {
			veiculo.getContratoRsng().setPercentualComissao(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.qtdMeses) {
			veiculo.getContratoRsng().setQuantidadeMeses(getInteger(c, valor, veiculo));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.siglaIndiceFinanceiro) {
			IndiceFinanceiro indice = IndiceFinanceiro.getByCodigo(valor);
			if (indice == null) {
				indice = IndiceFinanceiro.getByDescricao(valor);
			}
			veiculo.getContratoRsng().setSiglaIndiceFinaceiro(indice == null ? IndiceFinanceiro.OUTRO : indice);
		}
		else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.tipoRestricao) {

			if (c.getTipoCampoFormularioRemessa() == TipoCampoFormularioRemessa.TipoRestricao) {
				veiculo.getContratoRsng().setTipoRestricao(TipoRestricao.getByCodigo(valor));
				if (veiculo.getContratoRsng().getTipoRestricao() == null) {
					veiculo.getContratoRsng().setTipoRestricao(TipoRestricao.getByDescricao(valor));
				}
			} else {
				veiculo.getContratoRsng().setTipoRestricao(TipoRestricao.getByPosition(Integer.parseInt(valor)));
				if (veiculo.getContratoRsng().getTipoRestricao() == null) {
					veiculo.getContratoRsng().setTipoRestricao(TipoRestricao.getByDescricao(valor));
				}
			}

			verificarObrigatoriedade(c,
                    Objects.isNull(veiculo.getContratoRsng().getTipoRestricao()) ? null : veiculo.getContratoRsng().getTipoRestricao().toString(),
					veiculo);

		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.ufRegistroGravame) {
			veiculo.getContratoRsng().setUfRegistro(getUf(c, valor, veiculo));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorIOF) {
			veiculo.getContratoRsng().setValorIOF(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorParcela) {
			veiculo.getContratoRsng().setValorParcela(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorTaxaContrato) {
			veiculo.getContratoRsng().setValorTaxaContrato(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorTaxaJurosAno) {
			veiculo.getContratoRsng().setValorTaxaJurosAno(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorTaxaMora) {
			veiculo.getContratoRsng().setValorTaxaMoraDia(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorTaxaMulta) {
			veiculo.getContratoRsng().setValorTaxaMulta(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorTaxaoJurosMes) {
			veiculo.getContratoRsng().setValorTaxaJurosMes(getDecimal(c, valor));
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorTotalDivida) {
			veiculo.getContratoRsng().setValorTotalDivida(getDecimal(c, valor));
			if(veiculo.getContratoRsng().getValorCredito() == null) {
				veiculo.getContratoRsng().setValorCredito(getDecimal(c, valor));
			}
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.valorCredito) {
			veiculo.getContratoRsng().setValorCredito(getDecimal(c, valor));
			if(veiculo.getContratoRsng().getValorTotalDivida() == null) {
				veiculo.getContratoRsng().setValorTotalDivida(getDecimal(c, valor));
			}
		} else if (c.getCampoFormularioRemessa() == CampoFormularioRsngRemessa.email) {
			veiculo.getContratoRsng().setEmailDevedor(getString(valor));
		}
	}

	private void verificarObrigatoriedade(CampoTemplateRsngRemessa c, String valor, VeiculoRsng veiculo) {
		if (c.getObrigatorio() && Objects.isNull(valor)) {
			String mensagemErro = String.format("O campo %s é obrigatório. Foi identificado o valor de %s", c.getCampoFormularioRemessa(), valor);
			putError(mensagemErro, veiculo);
		}
	}
}
