package com.registrocontrato.registro.service.apis.arqdigital;

import com.registrocontrato.registro.enums.TipoVeiculo;

public class GrupoAdapter {

    public static String adaptaGrupoVeiculo(TipoVeiculo tipoVeiculo) {

        if (TipoVeiculo.getDuasRodas().contains(tipoVeiculo))
            return "MOTO";
        else if (tipoVeiculo.getDescricao().contains("Trator"))
            return "PRODUTO_DE_FORCA";
        else if (tipoVeiculo.getDescricao().contains("ônibus"))
            return "ONIBUS";
        else if (tipoVeiculo.getDescricao().contains("utilitário"))
            return "UTILITARIO";
        else if (tipoVeiculo.getDescricao().equals("Caminhão"))
            return "CAMINHAO";
        else if (tipoVeiculo.getDescricao().contains("Camione<PERSON>")
                || tipoVeiculo.getDescricao().contains("Caminhonete")
                || tipoVeiculo.getDescricao().contains("Automóvel"))
            return "CARRO";
        else
            return "OUTROS";
    }

}
