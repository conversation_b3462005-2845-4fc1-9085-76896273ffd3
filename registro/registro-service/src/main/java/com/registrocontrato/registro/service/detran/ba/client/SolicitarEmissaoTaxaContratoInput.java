//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.05.20 at 12:15:28 PM BRT 
//


package com.registrocontrato.registro.service.detran.ba.client;

import java.math.BigInteger;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for solicitarEmissaoTaxaContrato_Input complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="solicitarEmissaoTaxaContrato_Input">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="codigoUsuario" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="senhaUsuario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoOperacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CnpjPagador" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="chassiVeiculo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataContrato" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="origemGravame" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroRestSng" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="nossoNumero" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "solicitarEmissaoTaxaContrato_Input", propOrder = {
    "codigoUsuario",
    "senhaUsuario",
    "codigoOperacao",
    "cnpjPagador",
    "chassiVeiculo",
    "dataContrato",
    "origemGravame",
    "numeroRestSng",
    "nossoNumero"
})
public class SolicitarEmissaoTaxaContratoInput {

    protected BigInteger codigoUsuario;
    protected String senhaUsuario;
    protected String codigoOperacao;
    @XmlElement(name = "CnpjPagador")
    protected String cnpjPagador;
    protected String chassiVeiculo;
    protected BigInteger dataContrato;
    protected String origemGravame;
    protected BigInteger numeroRestSng;
    protected BigInteger nossoNumero;

    /**
     * Gets the value of the codigoUsuario property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCodigoUsuario() {
        return codigoUsuario;
    }

    /**
     * Sets the value of the codigoUsuario property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCodigoUsuario(BigInteger value) {
        this.codigoUsuario = value;
    }

    /**
     * Gets the value of the senhaUsuario property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenhaUsuario() {
        return senhaUsuario;
    }

    /**
     * Sets the value of the senhaUsuario property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenhaUsuario(String value) {
        this.senhaUsuario = value;
    }

    /**
     * Gets the value of the codigoOperacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoOperacao() {
        return codigoOperacao;
    }

    /**
     * Sets the value of the codigoOperacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoOperacao(String value) {
        this.codigoOperacao = value;
    }

    /**
     * Gets the value of the cnpjPagador property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCnpjPagador() {
        return cnpjPagador;
    }

    /**
     * Sets the value of the cnpjPagador property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCnpjPagador(String value) {
        this.cnpjPagador = value;
    }

    /**
     * Gets the value of the chassiVeiculo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChassiVeiculo() {
        return chassiVeiculo;
    }

    /**
     * Sets the value of the chassiVeiculo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChassiVeiculo(String value) {
        this.chassiVeiculo = value;
    }

    /**
     * Gets the value of the dataContrato property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataContrato() {
        return dataContrato;
    }

    /**
     * Sets the value of the dataContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataContrato(BigInteger value) {
        this.dataContrato = value;
    }

    /**
     * Gets the value of the origemGravame property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrigemGravame() {
        return origemGravame;
    }

    /**
     * Sets the value of the origemGravame property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrigemGravame(String value) {
        this.origemGravame = value;
    }

    /**
     * Gets the value of the numeroRestSng property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNumeroRestSng() {
        return numeroRestSng;
    }

    /**
     * Sets the value of the numeroRestSng property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNumeroRestSng(BigInteger value) {
        this.numeroRestSng = value;
    }

    /**
     * Gets the value of the nossoNumero property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNossoNumero() {
        return nossoNumero;
    }

    /**
     * Sets the value of the nossoNumero property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNossoNumero(BigInteger value) {
        this.nossoNumero = value;
    }

}
