package com.registrocontrato.registro.service.detran.pr.rest;

import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Anexo;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.CobrancaJobRepository;
import com.registrocontrato.registro.repository.CredenciamentoRepository;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.repository.MunicipioRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.cobranca.CobrancaJobService;
import com.registrocontrato.registro.service.detran.ms.client.BoletoCobrancaDTO;
import com.registrocontrato.registro.service.detran.pr.client.request.ContratoFinanVeiculoPrRequest;
import com.registrocontrato.registro.service.detran.pr.client.request.VeiculoPrRequest;
import com.registrocontrato.registro.service.detran.pr.client.response.*;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

import static com.registrocontrato.infra.util.PlaceconUtil.getDDD;
import static com.registrocontrato.infra.util.PlaceconUtil.getNumeroTelefone;

@Component
@Profile({"desenv", "homol"})
public class WsDetranPrRestClientDev extends WsDetranPR {

    public WsDetranPrRestClientDev(RegistroEnvioService registroEnvioService,
                                   FinanceiraRepository financeiraRepository,
                                   MensagemRetornoRepository mensagemRetornoRepository,
                                   CredenciamentoRepository credenciamentoRepository,
                                   MunicipioRepository municipioRepository,
                                   EnviaEmail enviaEmail,
                                   AcessoSenhaService acessoSenhaService,
                                   UsuarioService usuarioService,
                                   CobrancaJobRepository cobrancaJobRepository) {
        super(registroEnvioService, financeiraRepository, mensagemRetornoRepository,
                credenciamentoRepository, municipioRepository, enviaEmail, acessoSenhaService, usuarioService, cobrancaJobRepository);
    }

    @Override
    public String enviarAnexo(Contrato contrato, Anexo anexo, Veiculo veiculo) {
        if (veiculo.getNumeroChassi().startsWith("9B"))
            return "Erro no Envio";
        return null;
    }

    @Override
    public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
        long start = System.nanoTime();
        ContratoFinanVeiculoPrRequest request = putRequest(contrato);
        for (Veiculo v : contrato.getVeiculos()) {
            if (v.getNumeroChassi().startsWith("9") || contrato.getIdProcessoB3() != null) {
                registrarLog(contrato, start, request, "SUCESSO - HOMOL");
                return getSucesso(contrato.getUfRegistro());
            }

            if (v.getNumeroChassi().startsWith("8") || (contrato.getAlteracao() && contrato.getCepDevedor().startsWith("7"))) {
                ContratoFinanVeiculoPrResponse response = new ContratoFinanVeiculoPrResponse();
                response.setResultado(null);
                response.setMensagens(Arrays.asList(new MensagenPrResponse("8054", "Placa Inválida", "ERRO")));
                registrarLog(contrato, start, request, response);
                enviarEmailSuporte(enviaEmail, "HOMOLOGACAO - Mensagem de Erro na comunicacao de contrato");
                return new MensagemRetornoDTO(getErroPadrao(Uf.PR).getCodigo());
            }
        }

        ContratoFinanVeiculoPrResponse response = new ContratoFinanVeiculoPrResponse();
        response.setResultado("000000000");
        registrarLog(contrato, start, request, response);
        return new MensagemRetornoDTO(getSucesso(Uf.PR).getCodigo());
    }


    private ContratoFinanVeiculoPrRequest putRequest(Contrato contrato) {
        Financeira financeira = financeiraRepository.findOne(contrato.getFinanceira().getId());

        ContratoFinanVeiculoPrRequest request = new ContratoFinanVeiculoPrRequest();

        // financiado
        request.setEndFinanciadoLogradouro(contrato.getEnderecoDevedor());
        request.setEndFinanciadoNumero(StringUtils.isNumeric(contrato.getNumeroEnderecoDevedor()) ? Integer.valueOf(contrato.getNumeroEnderecoDevedor()) : 0);
        request.setEndFinanciadoComplemento(contrato.getComplementoEnderecoDevedor());
        request.setEndFinanciadoBairro(contrato.getBairroDevedor());
        request.setEndFinanciadoCodMunicipio(PlaceconUtil.getValorInteger(contrato.getMunicipioDevedor().getCodigoDenatran()));
        request.setEndFinanciadoUf(contrato.getUfEnderecoDevedor());
        request.setEndFinanciadoCep(PlaceconUtil.getValorInteger(contrato.getCepDevedor()));
        request.setEndFinanciadoDDDTelefone(contrato.getDddDevedor());
        request.setEndFinanciadoNumeroTelefone(contrato.getTelefoneDevedor());
        request.setFinanciadoNumeroCIC(PlaceconUtil.getValorLong(contrato.getCpfCnpjDevedorFinanciado()));
        request.setFinanciadoTipoCIC(contrato.getCpfCnpjDevedorFinanciado().length() > 11 ? 2 : 1);
        request.setFinanciadoNome(contrato.getNomeDevedorFinanciado());

        // agente
        request.setEndAgenteLogradouro(financeira.getEndereco());
        request.setEndAgenteNumero(StringUtils.isNumeric(financeira.getNumero()) ? Integer.valueOf(financeira.getNumero()) : 0);
        request.setEndAgenteComplemento(financeira.getComplemento());
        request.setEndAgenteBairro(financeira.getBairro());
        request.setEndAgenteCodMunicipio(PlaceconUtil.getValorInteger(financeira.getMunicipio().getCodigoDenatran()));
        request.setEndAgenteUf(financeira.getUfEndereco());
        request.setEndAgenteCep(PlaceconUtil.getValorInteger(financeira.getCep()));
        request.setEndAgenteDDDTelefone(PlaceconUtil.getValorInteger(getDDD(financeira.getTelefoneComercialRepresentante())));
        request.setEndAgenteNumeroTelefone(PlaceconUtil.getValorInteger(getNumeroTelefone(financeira.getTelefoneComercialRepresentante())));
        request.setAgenteNumeroCIC(PlaceconUtil.getValorLong(financeira.getDocumento()));
        request.setAgenteTipoCIC(financeira.getDocumento().length() > 11 ? 2 : 1);
        request.setAgenteNome(financeira.getNome());

        // contrato
        request.setQtdeParcelas(contrato.getQuantidadeMeses());
        request.setTaxaJurosMes(contrato.getValorTaxaJurosMes());
        request.setTaxaJurosAno(contrato.getValorTaxaJurosAno());
        request.setIndTaxaJurosMulta(contrato.getIndicadorTaxaMulta() ? "Sim" : "Não");
        request.setIndTaxaMoraDia(contrato.getIndicadorTaxaMoraDia() ? "Sim" : "Não");
        request.setValorTotalFinanciamento(contrato.getValorCredito());
        request.setValorIOF(contrato.getValorIOF());
        request.setValorParcela(contrato.getValorParcela());
        request.setDataVenctoPriParcela(contrato.getDataVencimentoPrimeiraParcela());
        request.setDataVenctoUltParcela(contrato.getDataVencimentoUltimaParcela());
        request.setDataLiberacaoCredito(contrato.getDataLiberacaoCredito());
        request.setUfLiberacaoCredito(contrato.getUfLiberacaoCredito());
        if (contrato.getMunicipioLiberacao() != null)
            request.setMunicipioLiberacaoCredito(PlaceconUtil.getValorInteger(contrato.getMunicipioLiberacao().getCodigoDenatran()));
        request.setIndices(contrato.getSiglaIndiceFinaceiro().name());
        request.setNumeroGrupoConsorcio(contrato.getNumeroGrupoConsorcio());
        request.setNumeroCotaConsorcio(contrato.getNumeroCotaConsorcio());

        request.setNumeroRegistroContratoRegistradora(contrato.getNumeroRegistroEletronico().toString());
        if (contrato.getNumeroAditivoContrato() != null) {
            request.setNumeroAditivoContrato(contrato.getNumeroAditivoContrato());
            request.setDataAditivoContrato(contrato.getDataAditivoContrato());
            request.setNumeroRegistroAditivoRegistradora(contrato.getNumeroRegistroEletronico().toString());
        }

        BigDecimal valorTaxaContrato = credenciamentoRepository.findByAtivo(Uf.PR, new Date()).getValorTotal();
        request.setValorTaxaContrato(valorTaxaContrato);

        request.setTaxaMulta(contrato.getValorTaxaMulta());
        request.setTaxaMora(contrato.getValorTaxaMoraDia());
        request.setIndPenalidade(contrato.getIndicadorPenalidade() ? "Sim" : "Não");
        request.setPenalidade(Objects.nonNull(contrato.getDescricaoPenalidade()) ? contrato.getDescricaoPenalidade() : null);
        request.setIndComissao(contrato.getIndicadorComissao() ? "Sim" : "Não");
        request.setPercentualComissao(contrato.getPercentualComissao());
        request.setNumeroContrato(contrato.getNumeroContrato());
        request.setDataContrato(contrato.getDataContrato());
        request.setQuantidadeVeiculos(contrato.getVeiculos().size());

        // assinatura
        request.setAssinatura(RandomStringUtils.randomAlphabetic(21));

        VeiculoPrRequest veiculo = null;
        for (Veiculo v : contrato.getVeiculos()) {
            veiculo = new VeiculoPrRequest();
            veiculo.setChassi(v.getNumeroChassi());
            veiculo.setIndRemarcacao(v.getChassiRemarcado() ? "Sim" : "Não");
            veiculo.setUfLicenciamento(contrato.getUfRegistro());
            veiculo.setUfPlaca(v.getUf());
            veiculo.setPlaca(v.getPlaca());
            veiculo.setRenavam(PlaceconUtil.getValorLong(v.getNumeroRenavam()));
            veiculo.setAnoFabricacao(v.getAnoFabricacao());
            veiculo.setAnoModelo(v.getAnoModelo());
            veiculo.setNumeroRestricao(PlaceconUtil.getValorInteger(v.getNumeroGravame()));
            request.addVeiculo(veiculo);
        }

        return request;
    }


    @Override
    public BoletoCobrancaDTO consultarBoleto(String documentoFinanceira, Date dataInicioCobranca) {
        String numCIC = "57723801000100";
//        String numCIC = documentoFinanceira;
        String tipoCIC = documentoFinanceira.length() > 11 ? "2" : "1";
        Calendar data = Calendar.getInstance();
        data.setTime(dataInicioCobranca);

        String ano = "2022";
//        String ano = String.valueOf(data.get(Calendar.YEAR));
//        String mes = String.valueOf(data.get(Calendar.MONTH) + 1);
        String mes = "5";

        String url = getUrl() + String.format("/v1/agente/%s/%s/cobranca/boleto/%s/%s", numCIC, tipoCIC, ano, mes);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + getAuthorization());
        HttpEntity<?> requestBoleto = new HttpEntity<>(headers);

        ClientHttpRequestFactory requestFactory = getClientHttpRequestFactory();
        RestTemplate restTemplate = new RestTemplate(requestFactory);

//        ResponseEntity<BoletoCobrancaPrResponse> responseBoletoEntity = restTemplate.exchange(url, HttpMethod.GET, requestBoleto, BoletoCobrancaPrResponse.class);
//        if (responseBoletoEntity.getStatusCode() == HttpStatus.OK) {
//            BoletoCobrancaDTO boleto = new BoletoCobrancaDTO();
//            BoletoCobrancaResultadoPrResponse response = responseBoletoEntity.getBody().getResultado();
//            boleto.setLinhaDigitalvel(response.getLinhaDigitavel());
//            boleto.setVencimento(PlaceconUtil.formataData(response.getDataVencimento()));
//            boleto.setDataEmissao(PlaceconUtil.formataData(response.getDataEmissao()));
//            boleto.setTotalGeral(response.getValor().toString());
//            return boleto;
//        }
//        return null;

        //MOCANDO DADOS FICTÍCIOS POIS A API DO DETRAN PR SÓ ESTÁ LIBERADA NO IP DE PRODUÇÃO
        BoletoCobrancaDTO boleto = new BoletoCobrancaDTO();
        boleto.setLinhaDigitalvel("123456");
        boleto.setVencimento("20220101");
        boleto.setDataEmissao("20220101");
        boleto.setTotalGeral("2000.78");
        return boleto;

    }

    @Override
    public InputStream downloadBoleto(Cobranca cobranca) {
        String numCIC = cobranca.getFinanceira().getDocumento();
        String tipoCIC = cobranca.getFinanceira().getDocumento().length() > 11 ? "2" : "1";
        Calendar data = Calendar.getInstance();
        data.setTime(cobranca.getDataInicio());

        String ano = String.valueOf(data.get(Calendar.YEAR));
        String mes = String.valueOf(data.get(Calendar.MONTH) + 1);


        String url = getUrl() + String.format("/v1/agente/%s/%s/cobranca/boleto/%s/%s?gerarPDF=Sim", numCIC, tipoCIC, ano, mes);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + getAuthorization());
        HttpEntity<?> requestBoleto = new HttpEntity<>(headers);

        ClientHttpRequestFactory requestFactory = getClientHttpRequestFactory();
        RestTemplate restTemplate = new RestTemplate(requestFactory);

        ResponseEntity<BoletoCobrancaPrResponse> responseBoletoEntity = restTemplate.exchange(url, HttpMethod.GET, requestBoleto, BoletoCobrancaPrResponse.class);
        if (responseBoletoEntity.getStatusCode() == HttpStatus.OK) {
            return new ByteArrayInputStream(Base64.getDecoder().decode(responseBoletoEntity.getBody().getResultado().getPdf()));
        }
        return null;
    }

    @Override
    public String downloadBoleto(Cobranca cobranca, String s) {
//        String numCIC = cobranca.getFinanceira().getDocumento();
        String numCIC = "57723801000100";
        String tipoCIC = cobranca.getFinanceira().getDocumento().length() > 11 ? "2" : "1";
        Calendar data = Calendar.getInstance();
        data.setTime(cobranca.getDataInicio());

//        String ano = String.valueOf(data.get(Calendar.YEAR));
//        String mes = String.valueOf(data.get(Calendar.MONTH) + 1);
        String ano = "2022";
        String mes = "5";

        String url = getUrl() + String.format("/v1/agente/%s/%s/cobranca/boleto/%s/%s/inativa?gerarPDF=Sim", numCIC, tipoCIC, ano, mes);
        getLogger().info(url);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + getAuthorization());
        getLogger().info(getAuthorization());
        HttpEntity<?> requestBoleto = new HttpEntity<>(headers);

//        RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
//
//        ResponseEntity<BoletoCobrancaPrResponse> responseBoletoEntity = restTemplate.exchange(url, HttpMethod.GET, requestBoleto, BoletoCobrancaPrResponse.class);
//        if (responseBoletoEntity.getStatusCode() == HttpStatus.OK) {
//            return responseBoletoEntity.getBody().getResultado().getPdf();
//        } else {
//            getLogger().error("Status " + responseBoletoEntity.getStatusCode());
//        }
        return null;
    }

    @Override
    public String downloadBoletoDefault(String documentoFinanceira, Calendar data) {
        return "";
    }

    @Override
    public RelatorioDeCobrancaResponse relatorioDeCobrancaResponse(Financeira financeira, Date periodoInicio, Date periodoFim) {

        if (Objects.nonNull(financeira) && Objects.nonNull(periodoFim) && Objects.nonNull(periodoInicio)) {
            RelatorioDeCobrancaResponse response = new RelatorioDeCobrancaResponse();
            String token = getAuthorization();
            String dataInicio = PlaceconUtil.getDataFormatada(periodoInicio, "YYYY-MM-dd");
            String dataFim = PlaceconUtil.getDataFormatada(periodoFim, "YYYY-MM-dd");
            String url = montarUrlRelatorioDeCobranca(financeira, dataInicio, dataFim);
            RestTemplate restTemplate = new RestTemplate(getClientHttpRequestFactory());
            ResponseEntity<RelatorioDeCobrancaResponse> responseEntity = null;

            int paginas = 1;

            for (int contador = 1; contador <= paginas; contador++) {
                paginas++;
                responseEntity = restTemplate.exchange(url + contador, HttpMethod.GET, getHeaders(token), RelatorioDeCobrancaResponse.class);
                if (Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody().getResultado()))
                    paginas = contador + 2;
                else response.getResultado().addAll(responseEntity.getBody().getResultado());

            }

            return response;

        }
        return null;
    }

    @Override
    protected ClientHttpRequestFactory getClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(getConnectionTimeout());
        return requestFactory;
    }

}
