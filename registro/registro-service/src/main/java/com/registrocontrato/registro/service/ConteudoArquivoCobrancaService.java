package com.registrocontrato.registro.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.registrocontrato.registro.entity.ArquivoCobranca;
import com.registrocontrato.registro.entity.ConteudoArquivoCobranca;
import com.registrocontrato.registro.repository.ConteudoArquivoCobrancaRepository;

@Service
public class ConteudoArquivoCobrancaService {

	@Autowired
	private ConteudoArquivoCobrancaRepository repository;
	
	public List<ConteudoArquivoCobranca>findByArquivoOrderByIdAsc(ArquivoCobranca arq){
		return repository.findByArquivoOrderByIdAsc(arq);
	}
	
	public ConteudoArquivoCobranca findByid(Long id) {
		ConteudoArquivoCobranca c = repository.findOne(id);
		if (c != null) c.getArquivo().getId();
		return c;
	}

	public ConteudoArquivoCobranca findFirstByArquivo(ArquivoCobranca arq) {
		return repository.findFirstByArquivo(arq);
	}
}
