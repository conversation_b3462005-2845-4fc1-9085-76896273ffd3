package com.registrocontrato.registro.service.detran.ce.client.response.apontamentos;

import com.registrocontrato.registro.service.detran.ce.client.response.ResponsePadraoCe;

public class FindApontamentosPendentesResponse extends ResponsePadraoCe {

    private FindApontamentoPendenteResponse objetoRetorno;

    public FindApontamentoPendenteResponse getObjetoRetorno() {
        return objetoRetorno;
    }

    public void setObjetoRetorno(FindApontamentoPendenteResponse objetoRetorno) {
        this.objetoRetorno = objetoRetorno;
    }
}
