//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2020.09.01 às 11:33:34 PM BRT 
//


package com.registrocontrato.registro.service.detran.rs.client;

import java.math.BigInteger;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de GadELoteTarifasDetalheTarifa complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="GadELoteTarifasDetalheTarifa"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="seqPagto" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="codTarifa" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/&gt;
 *         &lt;element name="situacao" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="dataSituacao" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="placa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="chassi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="nroRestrSng" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="origTransSng" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="diaJulianoTransSng" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="codTransSng" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *         &lt;element name="nroSeqSng" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GadELoteTarifasDetalheTarifa", propOrder = {
    "seqPagto",
    "codTarifa",
    "situacao",
    "dataSituacao",
    "placa",
    "chassi",
    "nroRestrSng",
    "origTransSng",
    "diaJulianoTransSng",
    "codTransSng",
    "nroSeqSng"
})
public class GadELoteTarifasDetalheTarifa {

    protected BigInteger seqPagto;
    protected Long codTarifa;
    protected BigInteger situacao;
    protected BigInteger dataSituacao;
    protected String placa;
    protected String chassi;
    protected BigInteger nroRestrSng;
    protected String origTransSng;
    protected BigInteger diaJulianoTransSng;
    protected BigInteger codTransSng;
    protected BigInteger nroSeqSng;

    /**
     * Obtém o valor da propriedade seqPagto.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getSeqPagto() {
        return seqPagto;
    }

    /**
     * Define o valor da propriedade seqPagto.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setSeqPagto(BigInteger value) {
        this.seqPagto = value;
    }

    /**
     * Obtém o valor da propriedade codTarifa.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getCodTarifa() {
        return codTarifa;
    }

    /**
     * Define o valor da propriedade codTarifa.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setCodTarifa(Long value) {
        this.codTarifa = value;
    }

    /**
     * Obtém o valor da propriedade situacao.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getSituacao() {
        return situacao;
    }

    /**
     * Define o valor da propriedade situacao.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setSituacao(BigInteger value) {
        this.situacao = value;
    }

    /**
     * Obtém o valor da propriedade dataSituacao.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataSituacao() {
        return dataSituacao;
    }

    /**
     * Define o valor da propriedade dataSituacao.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataSituacao(BigInteger value) {
        this.dataSituacao = value;
    }

    /**
     * Obtém o valor da propriedade placa.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlaca() {
        return placa;
    }

    /**
     * Define o valor da propriedade placa.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlaca(String value) {
        this.placa = value;
    }

    /**
     * Obtém o valor da propriedade chassi.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChassi() {
        return chassi;
    }

    /**
     * Define o valor da propriedade chassi.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChassi(String value) {
        this.chassi = value;
    }

    /**
     * Obtém o valor da propriedade nroRestrSng.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNroRestrSng() {
        return nroRestrSng;
    }

    /**
     * Define o valor da propriedade nroRestrSng.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNroRestrSng(BigInteger value) {
        this.nroRestrSng = value;
    }

    /**
     * Obtém o valor da propriedade origTransSng.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrigTransSng() {
        return origTransSng;
    }

    /**
     * Define o valor da propriedade origTransSng.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrigTransSng(String value) {
        this.origTransSng = value;
    }

    /**
     * Obtém o valor da propriedade diaJulianoTransSng.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDiaJulianoTransSng() {
        return diaJulianoTransSng;
    }

    /**
     * Define o valor da propriedade diaJulianoTransSng.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDiaJulianoTransSng(BigInteger value) {
        this.diaJulianoTransSng = value;
    }

    /**
     * Obtém o valor da propriedade codTransSng.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCodTransSng() {
        return codTransSng;
    }

    /**
     * Define o valor da propriedade codTransSng.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCodTransSng(BigInteger value) {
        this.codTransSng = value;
    }

    /**
     * Obtém o valor da propriedade nroSeqSng.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNroSeqSng() {
        return nroSeqSng;
    }

    /**
     * Define o valor da propriedade nroSeqSng.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNroSeqSng(BigInteger value) {
        this.nroSeqSng = value;
    }

}
