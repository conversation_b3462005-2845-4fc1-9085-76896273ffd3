//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.05.20 at 12:15:28 PM BRT 
//


package com.registrocontrato.registro.service.detran.ba.client;

import java.math.BigDecimal;
import java.math.BigInteger;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for cadastrarContratos_Input complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="cadastrarContratos_Input">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="codigoUsuario" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="senhaUsuario" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoOperacao" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="indicaNovoUsado" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="placaVeiculo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ufVeiculo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="chassi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroMotor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cnpjCredor" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="cpfCnpjDevedor" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="tipoDocumento" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="nomeDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="nomeLogDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroImovelDeve" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="compImovDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="bairroImovelDev" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="codigoMunDev" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="ufImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cepImovelDevedor" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="dddDevedor" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="telefoneDevedor" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="numeroContrato" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataContrato" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="numeroAditivo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dataAditivo" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="quantidadeParcel" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="taxaJurosMes" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="taxaJurosAno" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="indicativoJurosM" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="indicativoMoraDi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="taxaJuroMoratMes" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="valorTaxaContrat" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="valorTotalFinanc" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="valorIof" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="valorParcela" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="dataVencPrimeira" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="dataVencUltima" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="dataLiberacaoCre" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="ufLiberacaoCredi" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="municipioLibCred" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="indices" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroGrupoConso" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="numeroCotaConsor" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="taxaJuroMulta" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="valorMoraDia" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="indicadorPenalid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="indicadorComissa" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="valorComissao" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="penalidade" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="totalTaxaAdmin" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="indicativoTxMult" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fundoReservaPrev" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="indTxMultaMora" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="taxaMultaMora" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="tipoOperacao" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="numeroRestSng" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="numeroRegisgtro" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="dataRegistro" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="horaRegistro" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="codigoTaxa" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *         &lt;element name="nossoNumero" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cadastrarContratos_Input", propOrder = {
    "codigoUsuario",
    "senhaUsuario",
    "codigoOperacao",
    "indicaNovoUsado",
    "placaVeiculo",
    "ufVeiculo",
    "chassi",
    "numeroMotor",
    "cnpjCredor",
    "cpfCnpjDevedor",
    "tipoDocumento",
    "nomeDevedor",
    "nomeLogDevedor",
    "numeroImovelDeve",
    "compImovDevedor",
    "bairroImovelDev",
    "codigoMunDev",
    "ufImovelDevedor",
    "cepImovelDevedor",
    "dddDevedor",
    "telefoneDevedor",
    "numeroContrato",
    "dataContrato",
    "numeroAditivo",
    "dataAditivo",
    "quantidadeParcel",
    "taxaJurosMes",
    "taxaJurosAno",
    "indicativoJurosM",
    "indicativoMoraDi",
    "taxaJuroMoratMes",
    "valorTaxaContrat",
    "valorTotalFinanc",
    "valorIof",
    "valorParcela",
    "dataVencPrimeira",
    "dataVencUltima",
    "dataLiberacaoCre",
    "ufLiberacaoCredi",
    "municipioLibCred",
    "indices",
    "numeroGrupoConso",
    "numeroCotaConsor",
    "taxaJuroMulta",
    "valorMoraDia",
    "indicadorPenalid",
    "indicadorComissa",
    "valorComissao",
    "penalidade",
    "totalTaxaAdmin",
    "indicativoTxMult",
    "fundoReservaPrev",
    "indTxMultaMora",
    "taxaMultaMora",
    "tipoOperacao",
    "numeroRestSng",
    "numeroRegisgtro",
    "dataRegistro",
    "horaRegistro",
    "codigoTaxa",
    "nossoNumero"
})
public class CadastrarContratosInput {

    protected BigInteger codigoUsuario;
    protected String senhaUsuario;
    protected String codigoOperacao;
    protected String indicaNovoUsado;
    protected String placaVeiculo;
    protected String ufVeiculo;
    protected String chassi;
    protected String numeroMotor;
    protected BigInteger cnpjCredor;
    protected BigInteger cpfCnpjDevedor;
    protected BigInteger tipoDocumento;
    protected String nomeDevedor;
    protected String nomeLogDevedor;
    protected String numeroImovelDeve;
    protected String compImovDevedor;
    protected String bairroImovelDev;
    protected BigInteger codigoMunDev;
    protected String ufImovelDevedor;
    protected BigInteger cepImovelDevedor;
    protected BigInteger dddDevedor;
    protected BigInteger telefoneDevedor;
    protected String numeroContrato;
    protected BigInteger dataContrato;
    protected String numeroAditivo;
    protected BigInteger dataAditivo;
    protected BigInteger quantidadeParcel;
    @XmlElement(required = true)
    protected BigDecimal taxaJurosMes;
    @XmlElement(required = true)
    protected BigDecimal taxaJurosAno;
    protected String indicativoJurosM;
    protected String indicativoMoraDi;
    @XmlElement(required = true)
    protected BigDecimal taxaJuroMoratMes;
    @XmlElement(required = true)
    protected BigDecimal valorTaxaContrat;
    @XmlElement(required = true)
    protected BigDecimal valorTotalFinanc;
    @XmlElement(required = true)
    protected BigDecimal valorIof;
    @XmlElement(required = true)
    protected BigDecimal valorParcela;
    protected BigInteger dataVencPrimeira;
    protected BigInteger dataVencUltima;
    protected BigInteger dataLiberacaoCre;
    protected String ufLiberacaoCredi;
    protected String municipioLibCred;
    protected String indices;
    protected String numeroGrupoConso;
    protected BigInteger numeroCotaConsor;
    @XmlElement(required = true)
    protected BigDecimal taxaJuroMulta;
    @XmlElement(required = true)
    protected BigDecimal valorMoraDia;
    protected String indicadorPenalid;
    protected String indicadorComissa;
    @XmlElement(required = true)
    protected BigDecimal valorComissao;
    protected String penalidade;
    @XmlElement(required = true)
    protected BigDecimal totalTaxaAdmin;
    protected String indicativoTxMult;
    @XmlElement(required = true)
    protected BigDecimal fundoReservaPrev;
    protected String indTxMultaMora;
    @XmlElement(required = true)
    protected BigDecimal taxaMultaMora;
    protected BigInteger tipoOperacao;
    protected BigInteger numeroRestSng;
    protected BigInteger numeroRegisgtro;
    protected BigInteger dataRegistro;
    protected BigInteger horaRegistro;
    protected BigInteger codigoTaxa;
    protected BigInteger nossoNumero;

    /**
     * Gets the value of the codigoUsuario property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCodigoUsuario() {
        return codigoUsuario;
    }

    /**
     * Sets the value of the codigoUsuario property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCodigoUsuario(BigInteger value) {
        this.codigoUsuario = value;
    }

    /**
     * Gets the value of the senhaUsuario property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSenhaUsuario() {
        return senhaUsuario;
    }

    /**
     * Sets the value of the senhaUsuario property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSenhaUsuario(String value) {
        this.senhaUsuario = value;
    }

    /**
     * Gets the value of the codigoOperacao property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoOperacao() {
        return codigoOperacao;
    }

    /**
     * Sets the value of the codigoOperacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoOperacao(String value) {
        this.codigoOperacao = value;
    }

    /**
     * Gets the value of the indicaNovoUsado property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicaNovoUsado() {
        return indicaNovoUsado;
    }

    /**
     * Sets the value of the indicaNovoUsado property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicaNovoUsado(String value) {
        this.indicaNovoUsado = value;
    }

    /**
     * Gets the value of the placaVeiculo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlacaVeiculo() {
        return placaVeiculo;
    }

    /**
     * Sets the value of the placaVeiculo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlacaVeiculo(String value) {
        this.placaVeiculo = value;
    }

    /**
     * Gets the value of the ufVeiculo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfVeiculo() {
        return ufVeiculo;
    }

    /**
     * Sets the value of the ufVeiculo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfVeiculo(String value) {
        this.ufVeiculo = value;
    }

    /**
     * Gets the value of the chassi property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChassi() {
        return chassi;
    }

    /**
     * Sets the value of the chassi property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChassi(String value) {
        this.chassi = value;
    }

    /**
     * Gets the value of the numeroMotor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroMotor() {
        return numeroMotor;
    }

    /**
     * Sets the value of the numeroMotor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroMotor(String value) {
        this.numeroMotor = value;
    }

    /**
     * Gets the value of the cnpjCredor property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCnpjCredor() {
        return cnpjCredor;
    }

    /**
     * Sets the value of the cnpjCredor property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCnpjCredor(BigInteger value) {
        this.cnpjCredor = value;
    }

    /**
     * Gets the value of the cpfCnpjDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCpfCnpjDevedor() {
        return cpfCnpjDevedor;
    }

    /**
     * Sets the value of the cpfCnpjDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCpfCnpjDevedor(BigInteger value) {
        this.cpfCnpjDevedor = value;
    }

    /**
     * Gets the value of the tipoDocumento property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTipoDocumento() {
        return tipoDocumento;
    }

    /**
     * Sets the value of the tipoDocumento property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTipoDocumento(BigInteger value) {
        this.tipoDocumento = value;
    }

    /**
     * Gets the value of the nomeDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeDevedor() {
        return nomeDevedor;
    }

    /**
     * Sets the value of the nomeDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeDevedor(String value) {
        this.nomeDevedor = value;
    }

    /**
     * Gets the value of the nomeLogDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNomeLogDevedor() {
        return nomeLogDevedor;
    }

    /**
     * Sets the value of the nomeLogDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNomeLogDevedor(String value) {
        this.nomeLogDevedor = value;
    }

    /**
     * Gets the value of the numeroImovelDeve property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroImovelDeve() {
        return numeroImovelDeve;
    }

    /**
     * Sets the value of the numeroImovelDeve property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroImovelDeve(String value) {
        this.numeroImovelDeve = value;
    }

    /**
     * Gets the value of the compImovDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompImovDevedor() {
        return compImovDevedor;
    }

    /**
     * Sets the value of the compImovDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompImovDevedor(String value) {
        this.compImovDevedor = value;
    }

    /**
     * Gets the value of the bairroImovelDev property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBairroImovelDev() {
        return bairroImovelDev;
    }

    /**
     * Sets the value of the bairroImovelDev property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBairroImovelDev(String value) {
        this.bairroImovelDev = value;
    }

    /**
     * Gets the value of the codigoMunDev property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCodigoMunDev() {
        return codigoMunDev;
    }

    /**
     * Sets the value of the codigoMunDev property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCodigoMunDev(BigInteger value) {
        this.codigoMunDev = value;
    }

    /**
     * Gets the value of the ufImovelDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfImovelDevedor() {
        return ufImovelDevedor;
    }

    /**
     * Sets the value of the ufImovelDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfImovelDevedor(String value) {
        this.ufImovelDevedor = value;
    }

    /**
     * Gets the value of the cepImovelDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCepImovelDevedor() {
        return cepImovelDevedor;
    }

    /**
     * Sets the value of the cepImovelDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCepImovelDevedor(BigInteger value) {
        this.cepImovelDevedor = value;
    }

    /**
     * Gets the value of the dddDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDddDevedor() {
        return dddDevedor;
    }

    /**
     * Sets the value of the dddDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDddDevedor(BigInteger value) {
        this.dddDevedor = value;
    }

    /**
     * Gets the value of the telefoneDevedor property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTelefoneDevedor() {
        return telefoneDevedor;
    }

    /**
     * Sets the value of the telefoneDevedor property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTelefoneDevedor(BigInteger value) {
        this.telefoneDevedor = value;
    }

    /**
     * Gets the value of the numeroContrato property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroContrato() {
        return numeroContrato;
    }

    /**
     * Sets the value of the numeroContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroContrato(String value) {
        this.numeroContrato = value;
    }

    /**
     * Gets the value of the dataContrato property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataContrato() {
        return dataContrato;
    }

    /**
     * Sets the value of the dataContrato property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataContrato(BigInteger value) {
        this.dataContrato = value;
    }

    /**
     * Gets the value of the numeroAditivo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroAditivo() {
        return numeroAditivo;
    }

    /**
     * Sets the value of the numeroAditivo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroAditivo(String value) {
        this.numeroAditivo = value;
    }

    /**
     * Gets the value of the dataAditivo property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataAditivo() {
        return dataAditivo;
    }

    /**
     * Sets the value of the dataAditivo property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataAditivo(BigInteger value) {
        this.dataAditivo = value;
    }

    /**
     * Gets the value of the quantidadeParcel property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getQuantidadeParcel() {
        return quantidadeParcel;
    }

    /**
     * Sets the value of the quantidadeParcel property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setQuantidadeParcel(BigInteger value) {
        this.quantidadeParcel = value;
    }

    /**
     * Gets the value of the taxaJurosMes property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTaxaJurosMes() {
        return taxaJurosMes;
    }

    /**
     * Sets the value of the taxaJurosMes property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTaxaJurosMes(BigDecimal value) {
        this.taxaJurosMes = value;
    }

    /**
     * Gets the value of the taxaJurosAno property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTaxaJurosAno() {
        return taxaJurosAno;
    }

    /**
     * Sets the value of the taxaJurosAno property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTaxaJurosAno(BigDecimal value) {
        this.taxaJurosAno = value;
    }

    /**
     * Gets the value of the indicativoJurosM property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicativoJurosM() {
        return indicativoJurosM;
    }

    /**
     * Sets the value of the indicativoJurosM property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicativoJurosM(String value) {
        this.indicativoJurosM = value;
    }

    /**
     * Gets the value of the indicativoMoraDi property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicativoMoraDi() {
        return indicativoMoraDi;
    }

    /**
     * Sets the value of the indicativoMoraDi property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicativoMoraDi(String value) {
        this.indicativoMoraDi = value;
    }

    /**
     * Gets the value of the taxaJuroMoratMes property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTaxaJuroMoratMes() {
        return taxaJuroMoratMes;
    }

    /**
     * Sets the value of the taxaJuroMoratMes property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTaxaJuroMoratMes(BigDecimal value) {
        this.taxaJuroMoratMes = value;
    }

    /**
     * Gets the value of the valorTaxaContrat property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorTaxaContrat() {
        return valorTaxaContrat;
    }

    /**
     * Sets the value of the valorTaxaContrat property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorTaxaContrat(BigDecimal value) {
        this.valorTaxaContrat = value;
    }

    /**
     * Gets the value of the valorTotalFinanc property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorTotalFinanc() {
        return valorTotalFinanc;
    }

    /**
     * Sets the value of the valorTotalFinanc property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorTotalFinanc(BigDecimal value) {
        this.valorTotalFinanc = value;
    }

    /**
     * Gets the value of the valorIof property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorIof() {
        return valorIof;
    }

    /**
     * Sets the value of the valorIof property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorIof(BigDecimal value) {
        this.valorIof = value;
    }

    /**
     * Gets the value of the valorParcela property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorParcela() {
        return valorParcela;
    }

    /**
     * Sets the value of the valorParcela property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorParcela(BigDecimal value) {
        this.valorParcela = value;
    }

    /**
     * Gets the value of the dataVencPrimeira property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataVencPrimeira() {
        return dataVencPrimeira;
    }

    /**
     * Sets the value of the dataVencPrimeira property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataVencPrimeira(BigInteger value) {
        this.dataVencPrimeira = value;
    }

    /**
     * Gets the value of the dataVencUltima property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataVencUltima() {
        return dataVencUltima;
    }

    /**
     * Sets the value of the dataVencUltima property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataVencUltima(BigInteger value) {
        this.dataVencUltima = value;
    }

    /**
     * Gets the value of the dataLiberacaoCre property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataLiberacaoCre() {
        return dataLiberacaoCre;
    }

    /**
     * Sets the value of the dataLiberacaoCre property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataLiberacaoCre(BigInteger value) {
        this.dataLiberacaoCre = value;
    }

    /**
     * Gets the value of the ufLiberacaoCredi property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUfLiberacaoCredi() {
        return ufLiberacaoCredi;
    }

    /**
     * Sets the value of the ufLiberacaoCredi property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUfLiberacaoCredi(String value) {
        this.ufLiberacaoCredi = value;
    }

    /**
     * Gets the value of the municipioLibCred property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMunicipioLibCred() {
        return municipioLibCred;
    }

    /**
     * Sets the value of the municipioLibCred property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMunicipioLibCred(String value) {
        this.municipioLibCred = value;
    }

    /**
     * Gets the value of the indices property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndices() {
        return indices;
    }

    /**
     * Sets the value of the indices property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndices(String value) {
        this.indices = value;
    }

    /**
     * Gets the value of the numeroGrupoConso property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNumeroGrupoConso() {
        return numeroGrupoConso;
    }

    /**
     * Sets the value of the numeroGrupoConso property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNumeroGrupoConso(String value) {
        this.numeroGrupoConso = value;
    }

    /**
     * Gets the value of the numeroCotaConsor property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNumeroCotaConsor() {
        return numeroCotaConsor;
    }

    /**
     * Sets the value of the numeroCotaConsor property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNumeroCotaConsor(BigInteger value) {
        this.numeroCotaConsor = value;
    }

    /**
     * Gets the value of the taxaJuroMulta property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTaxaJuroMulta() {
        return taxaJuroMulta;
    }

    /**
     * Sets the value of the taxaJuroMulta property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTaxaJuroMulta(BigDecimal value) {
        this.taxaJuroMulta = value;
    }

    /**
     * Gets the value of the valorMoraDia property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorMoraDia() {
        return valorMoraDia;
    }

    /**
     * Sets the value of the valorMoraDia property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorMoraDia(BigDecimal value) {
        this.valorMoraDia = value;
    }

    /**
     * Gets the value of the indicadorPenalid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicadorPenalid() {
        return indicadorPenalid;
    }

    /**
     * Sets the value of the indicadorPenalid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicadorPenalid(String value) {
        this.indicadorPenalid = value;
    }

    /**
     * Gets the value of the indicadorComissa property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicadorComissa() {
        return indicadorComissa;
    }

    /**
     * Sets the value of the indicadorComissa property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicadorComissa(String value) {
        this.indicadorComissa = value;
    }

    /**
     * Gets the value of the valorComissao property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getValorComissao() {
        return valorComissao;
    }

    /**
     * Sets the value of the valorComissao property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setValorComissao(BigDecimal value) {
        this.valorComissao = value;
    }

    /**
     * Gets the value of the penalidade property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPenalidade() {
        return penalidade;
    }

    /**
     * Sets the value of the penalidade property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPenalidade(String value) {
        this.penalidade = value;
    }

    /**
     * Gets the value of the totalTaxaAdmin property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalTaxaAdmin() {
        return totalTaxaAdmin;
    }

    /**
     * Sets the value of the totalTaxaAdmin property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalTaxaAdmin(BigDecimal value) {
        this.totalTaxaAdmin = value;
    }

    /**
     * Gets the value of the indicativoTxMult property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndicativoTxMult() {
        return indicativoTxMult;
    }

    /**
     * Sets the value of the indicativoTxMult property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndicativoTxMult(String value) {
        this.indicativoTxMult = value;
    }

    /**
     * Gets the value of the fundoReservaPrev property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getFundoReservaPrev() {
        return fundoReservaPrev;
    }

    /**
     * Sets the value of the fundoReservaPrev property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setFundoReservaPrev(BigDecimal value) {
        this.fundoReservaPrev = value;
    }

    /**
     * Gets the value of the indTxMultaMora property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIndTxMultaMora() {
        return indTxMultaMora;
    }

    /**
     * Sets the value of the indTxMultaMora property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIndTxMultaMora(String value) {
        this.indTxMultaMora = value;
    }

    /**
     * Gets the value of the taxaMultaMora property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTaxaMultaMora() {
        return taxaMultaMora;
    }

    /**
     * Sets the value of the taxaMultaMora property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTaxaMultaMora(BigDecimal value) {
        this.taxaMultaMora = value;
    }

    /**
     * Gets the value of the tipoOperacao property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getTipoOperacao() {
        return tipoOperacao;
    }

    /**
     * Sets the value of the tipoOperacao property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setTipoOperacao(BigInteger value) {
        this.tipoOperacao = value;
    }

    /**
     * Gets the value of the numeroRestSng property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNumeroRestSng() {
        return numeroRestSng;
    }

    /**
     * Sets the value of the numeroRestSng property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNumeroRestSng(BigInteger value) {
        this.numeroRestSng = value;
    }

    /**
     * Gets the value of the numeroRegisgtro property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNumeroRegisgtro() {
        return numeroRegisgtro;
    }

    /**
     * Sets the value of the numeroRegisgtro property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNumeroRegisgtro(BigInteger value) {
        this.numeroRegisgtro = value;
    }

    /**
     * Gets the value of the dataRegistro property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getDataRegistro() {
        return dataRegistro;
    }

    /**
     * Sets the value of the dataRegistro property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setDataRegistro(BigInteger value) {
        this.dataRegistro = value;
    }

    /**
     * Gets the value of the horaRegistro property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getHoraRegistro() {
        return horaRegistro;
    }

    /**
     * Sets the value of the horaRegistro property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setHoraRegistro(BigInteger value) {
        this.horaRegistro = value;
    }

    /**
     * Gets the value of the codigoTaxa property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getCodigoTaxa() {
        return codigoTaxa;
    }

    /**
     * Sets the value of the codigoTaxa property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setCodigoTaxa(BigInteger value) {
        this.codigoTaxa = value;
    }

    /**
     * Gets the value of the nossoNumero property.
     * 
     * @return
     *     possible object is
     *     {@link BigInteger }
     *     
     */
    public BigInteger getNossoNumero() {
        return nossoNumero;
    }

    /**
     * Sets the value of the nossoNumero property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigInteger }
     *     
     */
    public void setNossoNumero(BigInteger value) {
        this.nossoNumero = value;
    }

}
