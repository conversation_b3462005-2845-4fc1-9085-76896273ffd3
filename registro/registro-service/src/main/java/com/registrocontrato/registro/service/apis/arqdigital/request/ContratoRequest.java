package com.registrocontrato.registro.service.apis.arqdigital.request;

import java.util.ArrayList;
import java.util.List;

public class ContratoRequest {

    private String numeroContrato;

    private DevedorRequest devedor;

    private EnderecoDevedorRequest enderecoDevedor;

    private List<VeiculoRequest> veiculos = new ArrayList<>();

    private TipoDocumentoRequest tipoDocumento;

    private List<CamposRequest> campos = new ArrayList<>();

    public String getNumeroContrato() {
        return numeroContrato;
    }

    public void setNumeroContrato(String numeroContrato) {
        this.numeroContrato = numeroContrato;
    }

    public DevedorRequest getDevedor() {
        return devedor;
    }

    public void setDevedor(DevedorRequest devedor) {
        this.devedor = devedor;
    }

    public EnderecoDevedorRequest getEnderecoDevedor() {
        return enderecoDevedor;
    }

    public void setEnderecoDevedor(EnderecoDevedorRequest enderecoDevedor) {
        this.enderecoDevedor = enderecoDevedor;
    }

    public List<VeiculoRequest> getVeiculos() {
        return veiculos;
    }

    public void setVeiculos(List<VeiculoRequest> veiculos) {
        this.veiculos = veiculos;
    }

    public TipoDocumentoRequest getTipoDocumento() {
        return tipoDocumento;
    }

    public void setTipoDocumento(TipoDocumentoRequest tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }

    public List<CamposRequest> getCampos() {
        return campos;
    }

    public void setCampos(List<CamposRequest> campos) {
        this.campos = campos;
    }
}
