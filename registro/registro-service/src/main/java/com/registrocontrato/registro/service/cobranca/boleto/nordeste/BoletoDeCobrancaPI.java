package com.registrocontrato.registro.service.cobranca.boleto.nordeste;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.service.ArquivoCobrancaService;
import com.registrocontrato.registro.service.cobranca.BoletoService;
import com.registrocontrato.registro.service.cobranca.boleto.CobrancaComBoletoDetranIntegrada;
import com.registrocontrato.seguranca.service.FinanceiraService;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class BoletoDeCobrancaPI extends CobrancaComBoletoDetranIntegrada {

    public BoletoDeCobrancaPI(BoletoService boletoService, FinanceiraService financeiraService, ArquivoCobrancaService arquivoCobrancaService) {
        super(boletoService, financeiraService, arquivoCobrancaService);
    }

    @Override
    protected String getImagePath() {
        return "/templates/assets/images/PI.png";
    }

    @Override
    public Uf getUf() {
        return Uf.PI;
    }

    @Override
    public List<String> getCnpjFinanceiras() {
        return Collections.emptyList();
    }
}
