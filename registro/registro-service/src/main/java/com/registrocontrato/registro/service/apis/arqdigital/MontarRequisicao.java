package com.registrocontrato.registro.service.apis.arqdigital;

import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.service.apis.arqdigital.request.*;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

public class MontarRequisicao {

    private Contrato contrato;

    public ContratoRequest montarRequest(Contrato contrato) {

        ContratoRequest contratoRequest = new ContratoRequest();
        contratoRequest.setNumeroContrato(Objects.nonNull(contrato.getNumeroContrato()) ? contrato.getNumeroContrato() : null);
        contratoRequest.setDevedor(montarRequisicaoDevedor(contrato));
        contratoRequest.setVeiculos(montarRequisicaoVeiculo(contrato));
        contratoRequest.setEnderecoDevedor(montarRequisicaoEndereco(contrato));
        contratoRequest.setTipoDocumento(new TipoDocumentoRequest(contrato.getTipoRestricao()));
        contratoRequest.setCampos(montarRequisicaoCampos(contrato));
        return contratoRequest;
    }

    private List<CamposRequest> montarRequisicaoCampos(Contrato contrato) {
        ArrayList<CamposRequest> campos = new ArrayList<>();
        montarCampoEValor(contrato).forEach((tipoCampos, o) -> {
            CamposRequest request = new CamposRequest(tipoCampos.toString(), Objects.nonNull(o) ? o.toString() : null);
            campos.add(request);
        });
        return campos;
    }

    private HashMap<TipoCampos, Object> montarCampoEValor(Contrato contrato) {

        HashMap<TipoCampos, Object> campos = new HashMap<>();
        campos.put(TipoCampos.DC_CODIGO_AGENTE, contrato.getNumeroContrato());
        campos.put(TipoCampos.DF_CLAUSULA, contrato.getClausulaPenalVrg());
        campos.put(TipoCampos.DF_CORRECAO, new BigDecimal(BigInteger.ZERO));
        campos.put(TipoCampos.DF_DATA_ASSINATURA, DateFormatUtils.format(contrato.getDataContrato(),"dd/MM/yyyy"));
        campos.put(TipoCampos.DF_DATA_VIG_CONTRATO, DateFormatUtils.format(contrato.getDataVencimentoUltimaParcela(), "dd/MM/yyyy"));
        campos.put(TipoCampos.DF_LOCAL_ASSINATURA, contrato.getMunicipioLiberacao().getDescricao());
        campos.put(TipoCampos.DF_QTD_MESES, contrato.getQuantidadeMeses());
        campos.put(TipoCampos.DF_UF_FINANCIAMENTO, contrato.getUfRegistro());
        campos.put(TipoCampos.DF_TOTAL, contrato.getValorTotalDivida());
        campos.put(TipoCampos.DF_TAXA_CONTRATO, contrato.getValorTaxaContrato());
        campos.put(TipoCampos.DF_COMISSAO, contrato.getPercentualComissao());
        campos.put(TipoCampos.DF_INDICATIVO_COMISSAO, contrato.getIndicadorComissao());
        campos.put(TipoCampos.DF_PENALIDADE, contrato.getDescricaoPenalidade());
        campos.put(TipoCampos.DF_INDICATIVO_PENALIDADE, contrato.getIndicadorPenalidade());
        campos.put(TipoCampos.DF_TAXA_MORA_DIA, contrato.getValorTaxaMoraDia());
        campos.put(TipoCampos.DF_INDICATIVO_MORA, contrato.getIndicadorTaxaMoraDia());
        campos.put(TipoCampos.DF_TAXA_JUROS_MULTA, contrato.getValorTaxaMulta());
        campos.put(TipoCampos.DF_INDICATIVO_MULTA, contrato.getIndicadorTaxaMulta());
        campos.put(TipoCampos.DF_DT_ADITIVO, contrato.getDataAditivoContrato());
        campos.put(TipoCampos.DF_VALORES_ENCARGOS, contrato.getValorIOF());
        campos.put(TipoCampos.DF_COTA_CONSORCIO, contrato.getNumeroCotaConsorcio());
        campos.put(TipoCampos.DF_GRUPO_CONSORCIO, contrato.getNumeroGrupoConsorcio());
        campos.put(TipoCampos.DF_INDICE, contrato.getSiglaIndiceFinaceiro().getDescricao());
        campos.put(TipoCampos.DF_MUNICIPIO, contrato.getMunicipioLiberacao().getDescricao());
        campos.put(TipoCampos.DF_CODIGO_MUNICIPIO, contrato.getMunicipioLiberacao().getCodigoDenatran());
        campos.put(TipoCampos.DF_LIBERACAO_CRED, DateFormatUtils.format(contrato.getDataLiberacaoCredito(), "dd/MM/yyyy"));
        campos.put(TipoCampos.DF_VENCTO_ULTIMA, DateFormatUtils.format(contrato.getDataVencimentoUltimaParcela(), "dd/MM/yyyy"));
        campos.put(TipoCampos.DF_VENCTO_PRIMEIRA, DateFormatUtils.format(contrato.getDataVencimentoPrimeiraParcela(), "dd/MM/yyyy"));
        campos.put(TipoCampos.DF_PARCELA, contrato.getValorParcela());
        campos.put(TipoCampos.DF_IOF, contrato.getValorIOF());
        campos.put(TipoCampos.DF_TAXA_JUROS_ANO, contrato.getValorTaxaJurosAno());
        campos.put(TipoCampos.DF_TAXA_JUROS_MES, contrato.getValorTaxaJurosMes());
        campos.put(TipoCampos.DF_QTD_PARCELAS, contrato.getQuantidadeMeses());
        return campos;
    }

    private EnderecoDevedorRequest montarRequisicaoEndereco(Contrato contrato) {
        EnderecoDevedorRequest enderecoDevedorRequest = new EnderecoDevedorRequest();
        enderecoDevedorRequest.setBairro(Objects.nonNull(contrato.getBairroDevedor()) ? contrato.getBairroDevedor() : null);
        enderecoDevedorRequest.setCep(Objects.nonNull(contrato.getCepDevedor()) ? contrato.getCepDevedor() : null);
        enderecoDevedorRequest.setNumero(Objects.nonNull(contrato.getNumeroEnderecoDevedor()) ? contrato.getNumeroEnderecoDevedor() : null);
        enderecoDevedorRequest.setLogradouro(Objects.nonNull(contrato.getEnderecoDevedor()) ? contrato.getEnderecoDevedor() : null);
        enderecoDevedorRequest.setUf(Objects.nonNull(contrato.getUfEnderecoDevedor()) ? contrato.getUfEnderecoDevedor() : null);
        enderecoDevedorRequest.setComplemento(Objects.nonNull(contrato.getComplementoEnderecoDevedor()) ? contrato.getComplementoEnderecoDevedor() : null);
        enderecoDevedorRequest.setCidade(Objects.nonNull(contrato.getMunicipioDevedor()) ? contrato.getMunicipioDevedor().getDescricao() : null);
        return enderecoDevedorRequest;
    }

    private List<VeiculoRequest> montarRequisicaoVeiculo(Contrato contrato) {
        List<VeiculoRequest> veiculos = new ArrayList<>();
        contrato.getVeiculos().forEach(veiculo -> {
            VeiculoRequest v = new VeiculoRequest();
            v.setRemarcado(Objects.nonNull(veiculo.getChassiRemarcado()) ? veiculo.getChassiRemarcado() : null);
            v.setChassi(Objects.nonNull(veiculo.getNumeroChassi()) ? veiculo.getNumeroChassi() : null);
            v.setGravame(Objects.nonNull(veiculo.getNumeroGravame()) ? veiculo.getNumeroGravame() : null);
            v.setMarca(Objects.nonNull(veiculo.getMarca()) ? veiculo.getMarca().getDescricao() : null);
            v.setModelo(Objects.nonNull(veiculo.getModelo()) ? veiculo.getModelo().getDescricao() : null);
            v.setGrupo(Objects.nonNull(veiculo.getTipo()) ? veiculo.getTipo().getDescricao() : null);
            v.setAno(Objects.nonNull(veiculo.getAnoFabricacao()) ? veiculo.getAnoFabricacao() : null);
            v.setAnoModelo(Objects.nonNull(veiculo.getAnoModelo()) ? veiculo.getAnoModelo() : null);
            v.setCor(Objects.nonNull(veiculo.getCor()) ? veiculo.getCor() : null);
            v.setPlaca(Objects.nonNull(veiculo.getPlaca()) ? veiculo.getPlaca() : null);
            v.setRenavam(Objects.nonNull(veiculo.getNumeroRenavam()) ? veiculo.getNumeroRenavam() : null);
            v.setCpfCnpjTerceiroGarantidor(Objects.nonNull(contrato.getCpfCnpjGarantidorFinanciado()) ? contrato.getCpfCnpjGarantidorFinanciado() : null);
            v.setNomeTerceiroGarantidor(Objects.nonNull(contrato.getNomeGarantidorFinanciado()) ? contrato.getNomeGarantidorFinanciado() : null);
            v.setGrupo(Objects.nonNull(veiculo.getTipo()) ? GrupoAdapter.adaptaGrupoVeiculo(veiculo.getTipo()) : null);
            v.setUfPlaca(Objects.nonNull(veiculo.getUf()) ? veiculo.getUf() : null);
            v.setTerceiroGarantidor(Objects.nonNull(contrato.getCpfCnpjGarantidorFinanciado()) ? true : false);
            v.setTarifaEspecial(false);
            v.setCombustivel(null);
            veiculos.add(v);
        });
        return veiculos;
    }

    private DevedorRequest montarRequisicaoDevedor(Contrato contrato) {
        DevedorRequest devedorRequest = new DevedorRequest();
        devedorRequest.setDdd(Objects.nonNull(contrato.getDddDevedor()) ? contrato.getDddDevedor() : null);
        devedorRequest.setNumero(Objects.nonNull(contrato.getTelefoneDevedor()) ? contrato.getTelefoneDevedor() : null);
        devedorRequest.setCpfCnpj(Objects.nonNull(contrato.getCpfCnpjDevedorFinanciado()) ? contrato.getCpfCnpjDevedorFinanciado() : null);
        devedorRequest.setEmail(Objects.nonNull(contrato.getEmailDevedor()) ? contrato.getEmailDevedor() : null);
        devedorRequest.setNome(Objects.nonNull(contrato.getNomeDevedorFinanciado()) ? contrato.getNomeDevedorFinanciado() : null);
        if (isPessoaFisica(contrato)) {
            devedorRequest.setTipo(TipoPessoa.PESSOA_FISICA);
        } else {
            devedorRequest.setTipo(TipoPessoa.PESSOA_JURIDICA);
            devedorRequest.setRazaoSocial(Objects.nonNull(contrato.getNomeDevedorFinanciado()) ? contrato.getNomeDevedorFinanciado() : null);
        }
        return devedorRequest;
    }

    private Boolean isPessoaFisica(Contrato contrato) {
        return contrato.getCpfCnpjDevedorFinanciado().length() <= 11;
    }

    public Contrato getContrato() {
        return contrato;
    }

    public void setContrato(Contrato contrato) {
        this.contrato = contrato;
    }
}
