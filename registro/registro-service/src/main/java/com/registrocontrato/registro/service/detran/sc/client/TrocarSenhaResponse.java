//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.03.16 at 09:13:47 PM BRT 
//


package com.registrocontrato.registro.service.detran.sc.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="TrocarSenhaResult" type="{http://webservicesh.sc.gov.br/detran/RegistroContrato}ResultadoTrocarSenha" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "trocarSenhaResult"
})
@XmlRootElement(name = "TrocarSenhaResponse")
public class TrocarSenhaResponse {

    @XmlElement(name = "TrocarSenhaResult")
    protected ResultadoTrocarSenha trocarSenhaResult;

    /**
     * Gets the value of the trocarSenhaResult property.
     * 
     * @return
     *     possible object is
     *     {@link ResultadoTrocarSenha }
     *     
     */
    public ResultadoTrocarSenha getTrocarSenhaResult() {
        return trocarSenhaResult;
    }

    /**
     * Sets the value of the trocarSenhaResult property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultadoTrocarSenha }
     *     
     */
    public void setTrocarSenhaResult(ResultadoTrocarSenha value) {
        this.trocarSenhaResult = value;
    }

}
