//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2020.09.01 às 11:33:34 PM BRT 
//


package com.registrocontrato.registro.service.detran.rs.client;

import java.math.BigInteger;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlID;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.CollapsedStringAdapter;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Classe Java de GadELoteTarifasListaPedido complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="GadELoteTarifasListaPedido"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="infContrato"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="cnpjAgente" type="{http://vei.detran.rs.gov.br/}TCnpjVar"/&gt;
 *                   &lt;element name="dataIniPeriodo" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
 *                   &lt;element name="dataFimPeriodo" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
 *                 &lt;/sequence&gt;
 *                 &lt;attribute name="Id" type="{http://www.w3.org/2001/XMLSchema}ID" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element ref="{http://www.w3.org/2000/09/xmldsig#}Signature" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GadELoteTarifasListaPedido", propOrder = {
    "infContrato",
    "signature"
})
public class GadELoteTarifasListaPedido {

    @XmlElement(required = true)
    protected GadELoteTarifasListaPedido.InfContrato infContrato;
    @XmlElement(name = "SignatureMailgunDTO", namespace = "http://www.w3.org/2000/09/xmldsig#")
    protected SignatureType signature;

    /**
     * Obtém o valor da propriedade infContrato.
     * 
     * @return
     *     possible object is
     *     {@link GadELoteTarifasListaPedido.InfContrato }
     *     
     */
    public GadELoteTarifasListaPedido.InfContrato getInfContrato() {
        return infContrato;
    }

    /**
     * Define o valor da propriedade infContrato.
     * 
     * @param value
     *     allowed object is
     *     {@link GadELoteTarifasListaPedido.InfContrato }
     *     
     */
    public void setInfContrato(GadELoteTarifasListaPedido.InfContrato value) {
        this.infContrato = value;
    }

    /**
     * Obtém o valor da propriedade signature.
     * 
     * @return
     *     possible object is
     *     {@link SignatureType }
     *     
     */
    public SignatureType getSignature() {
        return signature;
    }

    /**
     * Define o valor da propriedade signature.
     * 
     * @param value
     *     allowed object is
     *     {@link SignatureType }
     *     
     */
    public void setSignature(SignatureType value) {
        this.signature = value;
    }


    /**
     * <p>Classe Java de anonymous complex type.
     * 
     * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
     * 
     * <pre>
     * &lt;complexType&gt;
     *   &lt;complexContent&gt;
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
     *       &lt;sequence&gt;
     *         &lt;element name="cnpjAgente" type="{http://vei.detran.rs.gov.br/}TCnpjVar"/&gt;
     *         &lt;element name="dataIniPeriodo" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
     *         &lt;element name="dataFimPeriodo" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
     *       &lt;/sequence&gt;
     *       &lt;attribute name="Id" type="{http://www.w3.org/2001/XMLSchema}ID" /&gt;
     *     &lt;/restriction&gt;
     *   &lt;/complexContent&gt;
     * &lt;/complexType&gt;
     * </pre>
     * 
     * 
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
        "cnpjAgente",
        "dataIniPeriodo",
        "dataFimPeriodo"
    })
    public static class InfContrato {

        @XmlElement(required = true)
        protected String cnpjAgente;
        @XmlElement(required = true)
        protected BigInteger dataIniPeriodo;
        @XmlElement(required = true)
        protected BigInteger dataFimPeriodo;
        @XmlAttribute(name = "Id")
        @XmlJavaTypeAdapter(CollapsedStringAdapter.class)
        @XmlID
        @XmlSchemaType(name = "ID")
        protected String id;

        /**
         * Obtém o valor da propriedade cnpjAgente.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getCnpjAgente() {
            return cnpjAgente;
        }

        /**
         * Define o valor da propriedade cnpjAgente.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setCnpjAgente(String value) {
            this.cnpjAgente = value;
        }

        /**
         * Obtém o valor da propriedade dataIniPeriodo.
         * 
         * @return
         *     possible object is
         *     {@link BigInteger }
         *     
         */
        public BigInteger getDataIniPeriodo() {
            return dataIniPeriodo;
        }

        /**
         * Define o valor da propriedade dataIniPeriodo.
         * 
         * @param value
         *     allowed object is
         *     {@link BigInteger }
         *     
         */
        public void setDataIniPeriodo(BigInteger value) {
            this.dataIniPeriodo = value;
        }

        /**
         * Obtém o valor da propriedade dataFimPeriodo.
         * 
         * @return
         *     possible object is
         *     {@link BigInteger }
         *     
         */
        public BigInteger getDataFimPeriodo() {
            return dataFimPeriodo;
        }

        /**
         * Define o valor da propriedade dataFimPeriodo.
         * 
         * @param value
         *     allowed object is
         *     {@link BigInteger }
         *     
         */
        public void setDataFimPeriodo(BigInteger value) {
            this.dataFimPeriodo = value;
        }

        /**
         * Obtém o valor da propriedade id.
         * 
         * @return
         *     possible object is
         *     {@link String }
         *     
         */
        public String getId() {
            return id;
        }

        /**
         * Define o valor da propriedade id.
         * 
         * @param value
         *     allowed object is
         *     {@link String }
         *     
         */
        public void setId(String value) {
            this.id = value;
        }

    }

}
