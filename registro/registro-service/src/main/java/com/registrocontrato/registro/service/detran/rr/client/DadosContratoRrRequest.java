package com.registrocontrato.registro.service.detran.rr.client;

public class DadosContratoRrRequest {

	private String numRestricao;
	private String numContrato;
	private String placa;
	private String ufCredito;
	private String nomeFinanciado;
	private String remarcacao;
	private String totalFinanciamento;
	private String valorTaxaDeMora;
	private String tipoRestricao;
	private String ufLicenciamento;
	private String numRegistroContrato;
	private String valorTaxaDeMulta;
	private String codAgente;
	private String indicadorTaxaJurosMulta;
	private String valorIOF;
	private String penalidade;
	private String numDataAditivo;
	private String dataContrato;
	private String indices;
	private String anoModelo;
	private String numAditivoContrato;
	private String indicativoComissao;
	private String chassi;
	private String dataVencPrimeiraParcela;
	private String ufPlaca;
	private String indicadorTaxaMoraDia;
	private String anoFabricacao;
	private String nomeAgente;
	private String valorComissao;
	private String numRegistroAditivo;
	private String renavam;
	private String dataLiberacaoCredito;
	private String taxaContrato;
	private String valorParcela;
	private String docAgente;
	private String taxaJurosMes;
	private String dataVencUltimaParcela;
	private String taxaJurosAno;
	private String numGrupoConsorcio;
	private String numCotaConsorcio;
	private String indicativoPenalidade;
	private String qtdParcelas;
	private String docFinanciado;
	private String cidadeCredito;

	public String getNumRestricao() {
		return numRestricao;
	}

	public void setNumRestricao(String numRestricao) {
		this.numRestricao = numRestricao;
	}

	public String getNumContrato() {
		return numContrato;
	}

	public void setNumContrato(String numContrato) {
		this.numContrato = numContrato;
	}

	public String getPlaca() {
		return placa;
	}

	public void setPlaca(String placa) {
		this.placa = placa;
	}

	public String getUfCredito() {
		return ufCredito;
	}

	public void setUfCredito(String ufCredito) {
		this.ufCredito = ufCredito;
	}

	public String getNomeFinanciado() {
		return nomeFinanciado;
	}

	public void setNomeFinanciado(String nomeFinanciado) {
		this.nomeFinanciado = nomeFinanciado;
	}

	public String getRemarcacao() {
		return remarcacao;
	}

	public void setRemarcacao(String remarcacao) {
		this.remarcacao = remarcacao;
	}

	public String getTotalFinanciamento() {
		return totalFinanciamento;
	}

	public void setTotalFinanciamento(String totalFinanciamento) {
		this.totalFinanciamento = totalFinanciamento;
	}

	public String getValorTaxaDeMora() {
		return valorTaxaDeMora;
	}

	public void setValorTaxaDeMora(String valorTaxaDeMora) {
		this.valorTaxaDeMora = valorTaxaDeMora;
	}

	public String getTipoRestricao() {
		return tipoRestricao;
	}

	public void setTipoRestricao(String tipoRestricao) {
		this.tipoRestricao = tipoRestricao;
	}

	public String getUfLicenciamento() {
		return ufLicenciamento;
	}

	public void setUfLicenciamento(String ufLicenciamento) {
		this.ufLicenciamento = ufLicenciamento;
	}

	public String getNumRegistroContrato() {
		return numRegistroContrato;
	}

	public void setNumRegistroContrato(String numRegistroContrato) {
		this.numRegistroContrato = numRegistroContrato;
	}

	public String getValorTaxaDeMulta() {
		return valorTaxaDeMulta;
	}

	public void setValorTaxaDeMulta(String valorTaxaDeMulta) {
		this.valorTaxaDeMulta = valorTaxaDeMulta;
	}

	public String getCodAgente() {
		return codAgente;
	}

	public void setCodAgente(String codAgente) {
		this.codAgente = codAgente;
	}

	public String getIndicadorTaxaJurosMulta() {
		return indicadorTaxaJurosMulta;
	}

	public void setIndicadorTaxaJurosMulta(String indicadorTaxaJurosMulta) {
		this.indicadorTaxaJurosMulta = indicadorTaxaJurosMulta;
	}

	public String getValorIOF() {
		return valorIOF;
	}

	public void setValorIOF(String valorIOF) {
		this.valorIOF = valorIOF;
	}

	public String getPenalidade() {
		return penalidade;
	}

	public void setPenalidade(String penalidade) {
		this.penalidade = penalidade;
	}

	public String getNumDataAditivo() {
		return numDataAditivo;
	}

	public void setNumDataAditivo(String numDataAditivo) {
		this.numDataAditivo = numDataAditivo;
	}

	public String getDataContrato() {
		return dataContrato;
	}

	public void setDataContrato(String dataContrato) {
		this.dataContrato = dataContrato;
	}

	public String getIndices() {
		return indices;
	}

	public void setIndices(String indices) {
		this.indices = indices;
	}

	public String getAnoModelo() {
		return anoModelo;
	}

	public void setAnoModelo(String anoModelo) {
		this.anoModelo = anoModelo;
	}

	public String getNumAditivoContrato() {
		return numAditivoContrato;
	}

	public void setNumAditivoContrato(String numAditivoContrato) {
		this.numAditivoContrato = numAditivoContrato;
	}

	public String getIndicativoComissao() {
		return indicativoComissao;
	}

	public void setIndicativoComissao(String indicativoComissao) {
		this.indicativoComissao = indicativoComissao;
	}

	public String getChassi() {
		return chassi;
	}

	public void setChassi(String chassi) {
		this.chassi = chassi;
	}

	public String getDataVencPrimeiraParcela() {
		return dataVencPrimeiraParcela;
	}

	public void setDataVencPrimeiraParcela(String dataVencPrimeiraParcela) {
		this.dataVencPrimeiraParcela = dataVencPrimeiraParcela;
	}

	public String getUfPlaca() {
		return ufPlaca;
	}

	public void setUfPlaca(String ufPlaca) {
		this.ufPlaca = ufPlaca;
	}

	public String getIndicadorTaxaMoraDia() {
		return indicadorTaxaMoraDia;
	}

	public void setIndicadorTaxaMoraDia(String indicadorTaxaMoraDia) {
		this.indicadorTaxaMoraDia = indicadorTaxaMoraDia;
	}

	public String getAnoFabricacao() {
		return anoFabricacao;
	}

	public void setAnoFabricacao(String anoFabricacao) {
		this.anoFabricacao = anoFabricacao;
	}

	public String getNomeAgente() {
		return nomeAgente;
	}

	public void setNomeAgente(String nomeAgente) {
		this.nomeAgente = nomeAgente;
	}

	public String getValorComissao() {
		return valorComissao;
	}

	public void setValorComissao(String valorComissao) {
		this.valorComissao = valorComissao;
	}

	public String getNumRegistroAditivo() {
		return numRegistroAditivo;
	}

	public void setNumRegistroAditivo(String numRegistroAditivo) {
		this.numRegistroAditivo = numRegistroAditivo;
	}

	public String getRenavam() {
		return renavam;
	}

	public void setRenavam(String renavam) {
		this.renavam = renavam;
	}

	public String getDataLiberacaoCredito() {
		return dataLiberacaoCredito;
	}

	public void setDataLiberacaoCredito(String dataLiberacaoCredito) {
		this.dataLiberacaoCredito = dataLiberacaoCredito;
	}

	public String getTaxaContrato() {
		return taxaContrato;
	}

	public void setTaxaContrato(String taxaContrato) {
		this.taxaContrato = taxaContrato;
	}

	public String getValorParcela() {
		return valorParcela;
	}

	public void setValorParcela(String valorParcela) {
		this.valorParcela = valorParcela;
	}

	public String getDocAgente() {
		return docAgente;
	}

	public void setDocAgente(String docAgente) {
		this.docAgente = docAgente;
	}

	public String getTaxaJurosMes() {
		return taxaJurosMes;
	}

	public void setTaxaJurosMes(String taxaJurosMes) {
		this.taxaJurosMes = taxaJurosMes;
	}

	public String getDataVencUltimaParcela() {
		return dataVencUltimaParcela;
	}

	public void setDataVencUltimaParcela(String dataVencUltimaParcela) {
		this.dataVencUltimaParcela = dataVencUltimaParcela;
	}

	public String getTaxaJurosAno() {
		return taxaJurosAno;
	}

	public void setTaxaJurosAno(String taxaJurosAno) {
		this.taxaJurosAno = taxaJurosAno;
	}

	public String getNumGrupoConsorcio() {
		return numGrupoConsorcio;
	}

	public void setNumGrupoConsorcio(String numGrupoConsorcio) {
		this.numGrupoConsorcio = numGrupoConsorcio;
	}

	public String getNumCotaConsorcio() {
		return numCotaConsorcio;
	}

	public void setNumCotaConsorcio(String numCotaConsorcio) {
		this.numCotaConsorcio = numCotaConsorcio;
	}

	public String getIndicativoPenalidade() {
		return indicativoPenalidade;
	}

	public void setIndicativoPenalidade(String indicativoPenalidade) {
		this.indicativoPenalidade = indicativoPenalidade;
	}

	public String getQtdParcelas() {
		return qtdParcelas;
	}

	public void setQtdParcelas(String qtdParcelas) {
		this.qtdParcelas = qtdParcelas;
	}

	public String getDocFinanciado() {
		return docFinanciado;
	}

	public void setDocFinanciado(String docFinanciado) {
		this.docFinanciado = docFinanciado;
	}

	public String getCidadeCredito() {
		return cidadeCredito;
	}

	public void setCidadeCredito(String cidadeCredito) {
		this.cidadeCredito = cidadeCredito;
	}

}
