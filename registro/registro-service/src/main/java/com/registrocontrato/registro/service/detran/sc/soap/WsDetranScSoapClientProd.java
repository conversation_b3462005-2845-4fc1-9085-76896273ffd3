package com.registrocontrato.registro.service.detran.sc.soap;

import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.TipoMensagem;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.sc.client.*;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.registro.service.util.PDFUtil;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

import java.io.FileInputStream;
import java.util.Objects;

@Component
@Profile({"prod", "hot","prod-rj"})
public class WsDetranScSoapClientProd extends WsDetranSC {

	public WsDetranScSoapClientProd(MensagemRetornoRepository mensagemRetornoRepository,
									RegistroEnvioService registroEnvioService,
									FinanceiraRepository financeiraRepository,
									AcessoSenhaService acessoSenhaService,
									UsuarioService usuarioService) {
		super(mensagemRetornoRepository, registroEnvioService, financeiraRepository, acessoSenhaService, usuarioService);
	}

	@Override
	public MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo) {
		long start = System.nanoTime();
		RegistrarContrato request = putRequest(contrato, veiculo);
		RegistrarContratoResponse response = null;
		MensagemRetornoDTO mensagemRetorno = null;
		try {
			getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());

			getWebServiceTemplate().setMarshaller(marshaller());
			getWebServiceTemplate().setUnmarshaller(marshaller());

			response = (RegistrarContratoResponse) getWebServiceTemplate().marshalSendAndReceive(
					getUrl().trim(), request, webServiceMessageCallback(getUriRegistrarContrato()));

			if (response == null) {
				return getErroPadrao(contrato.getUfRegistro());
			}

			if (response.getRegistrarContratoResult().getCodRetorno() == 0) {
				mensagemRetorno = getSucesso(contrato.getUfRegistro());
				veiculo.setNumeroRegistroDetran(String.valueOf(response.getRegistrarContratoResult().getSequencialContrato()));
				mensagemRetorno.setNumeroDetran(veiculo.getNumeroRegistroDetran());
				return mensagemRetorno;
			}

			mensagemRetorno = new MensagemRetornoDTO();
			mensagemRetorno.setCodigo(String.valueOf(response.getRegistrarContratoResult().getCodRetorno()));
			mensagemRetorno.setDescricao(response.getRegistrarContratoResult().getDescRetorno());
			return mensagemRetorno;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return getErroPadrao(contrato.getUfRegistro());
		}
		finally {
			registrarLog(contrato, start, request, response);
		}
	}

	@Override
	public MensagemRetornoDTO consultarNumeroDetran(String chassi, int remarcado, String cnpjAgente,
			int gravame, int tipoGravame) {
		ConsultarSequencialContrato request = putRequest(chassi, remarcado, cnpjAgente, gravame, tipoGravame);
		ConsultarSequencialContratoResponse response = null;
		MensagemRetornoDTO mensagemRetorno = null;
		try {
			
			getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());
			
			getWebServiceTemplate().setMarshaller(marshaller());
			getWebServiceTemplate().setUnmarshaller(marshaller());
			
			response = (ConsultarSequencialContratoResponse) getWebServiceTemplate().marshalSendAndReceive(
					getUrl().trim(), request, webServiceMessageCallback(getUriConsultarSequencial()));
			
			if (response == null) {
				return getErroPadrao(Uf.SC);
			}
			
			if (response.getConsultarSequencialContratoResult().getCodRetorno() == 0) {
				mensagemRetorno = getSucesso(Uf.SC);
				mensagemRetorno.setNumeroDetran(String.valueOf(response.getConsultarSequencialContratoResult().getSequencialContrato()));
				return mensagemRetorno;
			}

			mensagemRetorno = new MensagemRetornoDTO();
			mensagemRetorno.setCodigo(String.valueOf(response.getConsultarSequencialContratoResult().getCodRetorno()));
			mensagemRetorno.setDescricao(response.getConsultarSequencialContratoResult().getDescRetorno());
			return mensagemRetorno;
		} 
		catch (HttpClientErrorException e) {
			MensagemRetornoDTO retorno = getErroPadrao(Uf.SC);
			retorno.setDescricao(e.getResponseBodyAsString());
			return retorno;
		} 
		catch (Exception e) {
			logger.error(e.getMessage(), e);
			MensagemRetornoDTO m =  getErroPadrao(Uf.SC);
			m.setDescricao(ExceptionUtils.getStackTrace(e));
			return m;
		}
	}

	@Override
	public int anexarArquivo(Contrato contrato, Veiculo veiculo, String referenciaArquivo) {
		long start = System.nanoTime();
		AnexarArquivo request = null;
		AnexarArquivoResponse response = null;
		try {
			String pathArquivo = getFileDirRead() + referenciaArquivo;
			FileSystemResource f = new FileSystemResource(pathArquivo);

			logger.info("Arquivo para envio DETRAN SC: " + f.getFilename());
			logger.info("Local: " + pathArquivo);

			byte[] arquivo = new byte[(int) f.contentLength()];

			FileInputStream fis = new FileInputStream(f.getFile());
			fis.read(arquivo);
			fis.close();

			Integer numeroSequencial = PlaceconUtil.getValorInteger(veiculo.getNumeroRegistroDetran());
			if (numeroSequencial == null) {
				logger.warn("Falha na leitura do número de registro do contrato");
				return 998;
			}
			request = putRequestAnexo(numeroSequencial, arquivo);

			getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());
			getWebServiceTemplate().setMarshaller(marshaller());
			getWebServiceTemplate().setUnmarshaller(marshaller());

			response = (AnexarArquivoResponse) getWebServiceTemplate().marshalSendAndReceive(getUrl(), request, webServiceMessageCallback("AnexarArquivo"));

			if (response != null)
				return response.getAnexarArquivoResult().getCodRetorno();
			return 999;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return 999;
		} finally {
			// OS ARQUIVOS DE CONTRATOS NÃO PRECISAM SER REGISTRADOS NO LOG
			if (Objects.nonNull(request) && Objects.nonNull(request.getDados()))
				request.getDados().setArquivoContrato(null);
			registrarLog(contrato, start, request, response);
		}
	}

	@Override
	public MensagemRetornoDTO alterarSenha(String novaSenha) {
		TrocarSenha request = putRequest(novaSenha);
		TrocarSenhaResponse response = null;
		MensagemRetornoDTO mensagemRetorno = null;
		try {
			getWebServiceTemplate().setMessageSender(httpComponentsMessageSender());
			
			getWebServiceTemplate().setMarshaller(marshaller());
			getWebServiceTemplate().setUnmarshaller(marshaller());
			
			response = (TrocarSenhaResponse) getWebServiceTemplate().marshalSendAndReceive(
					getUrl().trim(), request, webServiceMessageCallback(null));
			
			if (response == null) {
				return getErroPadrao(Uf.SC);
			}
			
			if (response.getTrocarSenhaResult().getCodRetorno() == 0) {
				//mensagemRetorno = getSucesso(contrato.getUfRegistro());
				mensagemRetorno = new MensagemRetornoDTO();
				mensagemRetorno.setTipoMensagem(TipoMensagem.DETRAN);
				mensagemRetorno.setSucesso(true);
				mensagemRetorno.setDescricao("OK");
				mensagemRetorno.setUf(Uf.SC);
				mensagemRetorno.setCodigo("000");
				return mensagemRetorno;
			}

			mensagemRetorno = new MensagemRetornoDTO();
			mensagemRetorno.setCodigo(String.valueOf(response.getTrocarSenhaResult().getCodRetorno()));
			mensagemRetorno.setDescricao(response.getTrocarSenhaResult().getDescRetorno());
			return mensagemRetorno;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return getErroPadrao(Uf.SC);
		} 
	}
}
