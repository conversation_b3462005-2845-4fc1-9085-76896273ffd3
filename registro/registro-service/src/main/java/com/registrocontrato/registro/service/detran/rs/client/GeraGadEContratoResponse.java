//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2020.09.01 às 11:33:34 PM BRT 
//


package com.registrocontrato.registro.service.detran.rs.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de geraGadEContratoResponse complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="geraGadEContratoResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="gadEContratoResponse" type="{http://vei.detran.rs.gov.br/}ContratoRetorno" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "geraGadEContratoResponse", propOrder = {
    "gadEContratoResponse"
})
public class GeraGadEContratoResponse {

    protected ContratoRetorno gadEContratoResponse;

    /**
     * Obtém o valor da propriedade gadEContratoResponse.
     * 
     * @return
     *     possible object is
     *     {@link ContratoRetorno }
     *     
     */
    public ContratoRetorno getGadEContratoResponse() {
        return gadEContratoResponse;
    }

    /**
     * Define o valor da propriedade gadEContratoResponse.
     * 
     * @param value
     *     allowed object is
     *     {@link ContratoRetorno }
     *     
     */
    public void setGadEContratoResponse(ContratoRetorno value) {
        this.gadEContratoResponse = value;
    }

}
