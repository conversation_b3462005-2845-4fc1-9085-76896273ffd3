package com.registrocontrato.registro.repository;

import com.registrocontrato.infra.service.BaseRepository;
import com.registrocontrato.registro.entity.Credenciamento;
import com.registrocontrato.registro.entity.PrecoComposto;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PrecoCompostoRepository extends BaseRepository<PrecoComposto> {

    Optional<PrecoComposto> findAllByValorParametro(String parametro);

    List<PrecoComposto> findPrecoCompostoByCredenciamento(Credenciamento credenciamento);

}
