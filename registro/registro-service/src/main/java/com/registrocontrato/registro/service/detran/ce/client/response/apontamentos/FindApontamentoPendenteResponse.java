package com.registrocontrato.registro.service.detran.ce.client.response.apontamentos;

import com.registrocontrato.registro.service.detran.ce.client.response.BasePage;

import java.util.List;

public class FindApontamentoPendenteResponse extends BasePage {

    private List<FindApontamentoResponse> apontamentos;

    public List<FindApontamentoResponse> getApontamentos() {
        return apontamentos;
    }

    public void setApontamentos(List<FindApontamentoResponse> apontamentos) {
        this.apontamentos = apontamentos;
    }
}
