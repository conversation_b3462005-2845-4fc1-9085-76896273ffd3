package com.registrocontrato.registro.service.detran.sc.soap;

import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import com.registrocontrato.registro.service.RegistroEnvioService;
import com.registrocontrato.registro.service.detran.DefinirCredenciaisAPIDetran;
import com.registrocontrato.registro.service.detran.WsDetranDefault;
import com.registrocontrato.registro.service.detran.sc.client.*;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.service.AcessoSenhaService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.WebServiceMessage;
import org.springframework.ws.client.core.WebServiceMessageCallback;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.SoapHeader;
import org.springframework.ws.soap.SoapMessage;
import org.springframework.ws.transport.http.HttpComponentsMessageSender;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.Marshaller;
import javax.xml.transform.TransformerException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

public abstract class WsDetranSC extends WebServiceGatewaySupport implements WsDetranDefault, DefinirCredenciaisAPIDetran {

    @Value("${detran.sc.default.uri:null}")
    private String DEFAULT_URI;

    @Value("${detran.sc.registrarcontrato:null}")
    private String URI_REGISTRAR_CONTRATO;

    @Value("${detran.sc.consultarsequencial:null}")
    private String URI_CONSULTAR_SEQUENCIAL;

    @Value("${detran.sc.usuario:null}")
    private String USUARIO;

    @Value("${detran.sc.senha:null}")
    private String SENHA;

    @Value("${detran.sc.context.path:null}")
    private String CLIENT_PATH;

    @Value("${file.dir-read:null}")
    private String FILE_DIR_READ;

    @Value("${detran.readTimeout:20000}")
    private Integer readTimeout;

    @Value("${detran.connectionTimeout:20000}")
    private Integer connectionTimeout;

    protected final MensagemRetornoRepository mensagemRetornoRepository;
    protected final RegistroEnvioService registroEnvioService;
    protected final FinanceiraRepository financeiraRepository;
    protected final AcessoSenhaService acessoSenhaService;
    private final UsuarioService usuarioService;

    public WsDetranSC(MensagemRetornoRepository mensagemRetornoRepository,
                      RegistroEnvioService registroEnvioService,
                      FinanceiraRepository financeiraRepository,
                      AcessoSenhaService acessoSenhaService,
                      UsuarioService usuarioService) {
        this.mensagemRetornoRepository = mensagemRetornoRepository;
        this.registroEnvioService = registroEnvioService;
        this.financeiraRepository = financeiraRepository;
        this.acessoSenhaService = acessoSenhaService;
        this.usuarioService = usuarioService;
    }

    public abstract MensagemRetornoDTO comunicarContratoFinanceiroVeiculo(Contrato contrato, Veiculo veiculo);

    public abstract MensagemRetornoDTO alterarSenha(String novaSenha);

    public abstract MensagemRetornoDTO consultarNumeroDetran(String chassi, int remarcado, String cnpjAgente, int gravame, int tipoGravame);

    public abstract int anexarArquivo(Contrato contrato, Veiculo veiculo, String referenciaArquivo);

    @PostConstruct
    void init() {
        getLogger().info("Credenciais Detran-" + getUf());
        configurarCredenciaisAPI(getUf());
    }

    public HttpComponentsMessageSender httpComponentsMessageSender() {
        HttpComponentsMessageSender sender = new HttpComponentsMessageSender();
        sender.setReadTimeout(readTimeout);
        sender.setConnectionTimeout(connectionTimeout);
        return sender;
    }

    protected Jaxb2Marshaller marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath(CLIENT_PATH);
        return marshaller;
    }

    protected WebServiceMessageCallback webServiceMessageCallback(String acao) {
        return new WebServiceMessageCallback() {
            @Override
            public void doWithMessage(final WebServiceMessage message) throws IOException, TransformerException {
                try {
                    final SoapHeader soapHeader = ((SoapMessage) message).getSoapHeader();
                    ((SoapMessage) message).setSoapAction("http://webservicesh.sc.gov.br/detran/RegistroContrato/" + acao);

                    ObjectFactory factory = new ObjectFactory();

                    Credenciais c = factory.createCredenciais();
                    c.setUsuario(USUARIO);
                    c.setSenha(SENHA);

                    JAXBElement<Credenciais> headers = factory.createCredenciais(c);

                    JAXBContext context = JAXBContext.newInstance(Credenciais.class);


                    Marshaller marshaller = context.createMarshaller();
                    marshaller.marshal(headers, soapHeader.getResult());


                    final ByteArrayOutputStream stream = new ByteArrayOutputStream();
                    ((SoapMessage) message).writeTo(stream);
                    getLogger().debug(new String(stream.toByteArray(), "utf-8"));
                } catch (final Exception e) {
                    e.printStackTrace();
                }
            }
        };
    }

    protected ConsultarSequencialContrato putRequest(String chassi, int remarcacao, String cnpjAgente, Integer gravame, int tipoGravame) {
        ConsultarSequencialContrato c = new ConsultarSequencialContrato();
        c.setDados(new DadosConsultaSequencial());
        c.getDados().setChassi(chassi);
        c.getDados().setCNPJAgente(cnpjAgente);
        c.getDados().setNumGravame(gravame);
        c.getDados().setRemarcacao(remarcacao);
        c.getDados().setTipoGravame(tipoGravame);
        return c;
    }

    protected TrocarSenha putRequest(String novaSenha) {
        TrocarSenha t = new TrocarSenha();
        t.setDados(new DadosTrocaSenha());
        t.getDados().setSenhaAntiga(SENHA);
        t.getDados().setSenhaNova(novaSenha);
        t.getDados().setUsuario(USUARIO);
        return t;
    }

    protected AnexarArquivo putRequestAnexo(int numeroSequencial, byte[] arquivo) {
        AnexarArquivo anexar = new AnexarArquivo();
        anexar.setDados(new DadosAnexarArquivo());
        anexar.getDados().setSequencialContrato(numeroSequencial);
        anexar.getDados().setArquivoContrato(arquivo);
        return anexar;
    }

    protected RegistrarContrato putRequest(Contrato contrato, Veiculo veiculo) {
        Financeira financeira = financeiraRepository.findOne(contrato.getFinanceira().getId());
        DadosRegistroContrato dados = new DadosRegistroContrato();

        Integer flagTransacao = 1; //default envio de contrato
        if (contrato.getAlteracao() == Boolean.TRUE) {
            flagTransacao = contrato.getDataAditivoContrato() != null ? 4 : 2;
        }
        else if (contrato.getDataAditivoContrato() != null) {
            flagTransacao = 3;
        }
        dados.setTipoOperacao(flagTransacao);

        if (veiculo.getNumeroRegistroDetran() != null) {
            dados.setSequencialContrato(Integer.parseInt(veiculo.getNumeroRegistroDetran()));
        }
        else if (contrato.getId() != null && veiculo.getNumeroRegistroDetran() == null){
            MensagemRetornoDTO m = consultarNumeroDetran(veiculo.getNumeroChassi(),
                    veiculo.getChassiRemarcado() == Boolean.TRUE ? 1 : 2, financeira.getDocumento(),
                    Integer.parseInt(veiculo.getNumeroGravame()), Integer.parseInt(contrato.getTipoRestricao().getCodigo()));
            if (m.getSucesso() == Boolean.TRUE) {
                dados.setSequencialContrato(Integer.parseInt(m.getNumeroDetran()));
            }
        }
        dados.setNumContratoOrigem(String.valueOf(contrato.getNumeroRegistroEletronico()));
        dados.setNumAditivoOrigem(String.valueOf(contrato.getNumeroRegistroEletronicoOrigem()));

        dados.setChassi(veiculo.getNumeroChassi().trim());
        dados.setRemarcacao(veiculo.getChassiRemarcado() ? 1 : 2);
        dados.setUFLicenciamento("SC");
        dados.setUFPlaca(veiculo.getUf() == null ? null : veiculo.getUf().toString());
        dados.setPlaca(veiculo.getPlaca());
        if (veiculo.getNumeroRenavam() != null) {
            dados.setRENAVAM(Long.parseLong(veiculo.getNumeroRenavam()));
        }

        dados.setAnoFabricacao(veiculo.getAnoFabricacao());
        dados.setAnoModelo(veiculo.getAnoModelo());

        dados.setNomeAgente(financeira.getNome());
        dados.setCNPJAgente(financeira.getDocumento());
        dados.setNumContrato(contrato.getNumeroContrato());
        dados.setDataContrato(Integer.parseInt(PlaceconUtil.formataData(contrato.getDataContrato())));
        dados.setQtdParcelas(contrato.getQuantidadeMeses());
        dados.setNumGravame(Integer.parseInt(veiculo.getNumeroGravame()));
        dados.setTipoGravame(Integer.parseInt(contrato.getTipoRestricao().getCodigo()));
        dados.setTaxaJuroMes(contrato.getValorTaxaJurosMes() == null ? 0 : (int)(contrato.getValorTaxaJurosMes().doubleValue() * 1000));
        dados.setTaxaJuroAno(contrato.getValorTaxaJurosAno() == null ? 0 : (int)(contrato.getValorTaxaJurosAno().doubleValue() * 1000));
        dados.setTaxaJuroMulta(contrato.getValorTaxaMulta() != null && contrato.getValorTaxaMulta().doubleValue() > 0 ? "SIM" : "NAO");
        dados.setTaxaMoraDia(contrato.getValorTaxaMoraDia() != null && contrato.getValorTaxaMoraDia().doubleValue() > 0 ? "SIM" : "NAO");
        dados.setTaxaMulta(contrato.getValorTaxaMulta() != null ? (int)(contrato.getValorTaxaMulta().doubleValue() * 100) : 0);
        dados.setTaxaMora(contrato.getValorTaxaMoraDia() != null ? (int)(contrato.getValorTaxaMoraDia().doubleValue() * 100) : 0);
        dados.setIndicativoPenalidade(contrato.getIndicadorPenalidade() == Boolean.TRUE ? "SIM" : "NAO");
        dados.setPenalidade(contrato.getDescricaoPenalidade());
        dados.setIndicativoComissao(contrato.getIndicadorComissao() == Boolean.TRUE ? "SIM" : "NAO");
        dados.setComissao(contrato.getPercentualComissao() != null ? (int)(contrato.getPercentualComissao().doubleValue() * 100) : 0 );
        dados.setValorTaxaContrato(contrato.getValorTaxaContrato() != null ? (int)(contrato.getValorTaxaContrato().doubleValue() * 100) : 0);
        dados.setValorTotalFinanciamento((int)(contrato.getValorTotalDivida().doubleValue() * 100));
        dados.setValorIOF(contrato.getValorIOF() != null ? (int)(contrato.getValorIOF().doubleValue() * 100) : 0);
        dados.setValorParcela((int)(contrato.getValorParcela().doubleValue() * 100));
        dados.setDataVectoPrimeiraParcela(Integer.parseInt(PlaceconUtil.formataData(contrato.getDataVencimentoPrimeiraParcela())));
        dados.setDataVectoUltimaParcela(Integer.parseInt(PlaceconUtil.formataData(contrato.getDataVencimentoUltimaParcela())));
        dados.setDataLiberacaoCredito(Integer.parseInt(PlaceconUtil.formataData(contrato.getDataLiberacaoCredito())));
        dados.setUFLiberacaoCredito(contrato.getUfLiberacaoCredito().toString());
        dados.setMunicipioLiberacaoCredito(contrato.getMunicipioLiberacao().getDescricao());
        dados.setIndice(contrato.getSiglaIndiceFinaceiro() == null ? null : contrato.getSiglaIndiceFinaceiro().toString());
        dados.setNumGrupoConsorcio(contrato.getNumeroGrupoConsorcio());
        if (contrato.getNumeroCotaConsorcio() != null) {
            dados.setNumCotaConsorcio(contrato.getNumeroCotaConsorcio().intValue());
        }
        dados.setNumAditivo(contrato.getNumeroAditivoContrato());
        if (contrato.getDataAditivoContrato() != null) {
            dados.setDataAditivo(Integer.parseInt(PlaceconUtil.formataData(contrato.getDataAditivoContrato())));
        }
        dados.setNomeLogradouroAgente(financeira.getEndereco());
        dados.setNumImovelAgente(financeira.getNumero() != null && !financeira.getNumero().trim().equals("") ? financeira.getNumero() : "0");

        dados.setComplementoImovelAgente(financeira.getComplemento());
        dados.setBairroAgente(financeira.getBairro());
        dados.setNomeMunicipioAgente(financeira.getMunicipio().getDescricao());
        dados.setUFAgente(financeira.getUfEndereco().toString());
        dados.setCEPAgente(Integer.parseInt(PlaceconUtil.retiraFormatacao(financeira.getCep())));
        String telefone = financeira.getTelefoneComercialRepresentante();
        dados.setDDDAgente(Integer.parseInt(PlaceconUtil.getDDD(telefone)));
        dados.setTelefoneAgente(PlaceconUtil.getNumeroTelefone(telefone));
        dados.setCPFCNPJDevedor(contrato.getCpfCnpjDevedorFinanciado());
        dados.setNomeDevedor(contrato.getNomeDevedorFinanciado());
        dados.setNomeLogradouroDevedor(contrato.getEnderecoDevedor());
        dados.setNumImovelDevedor(contrato.getNumeroEnderecoDevedor() != null && !contrato.getNumeroEnderecoDevedor().trim().equals("") ? contrato.getNumeroEnderecoDevedor() : "0");
        dados.setComplementoImovelDevedor(contrato.getComplementoEnderecoDevedor());
        dados.setBairroDevedor(contrato.getBairroDevedor());
        dados.setNomeMunicipioDevedor(contrato.getMunicipioDevedor().getDescricao());
        dados.setUFDevedor(contrato.getUfEnderecoDevedor().toString());
        dados.setCEPDevedor(Integer.parseInt(contrato.getCepDevedor()));
        dados.setDDDDevedor(contrato.getDddDevedor());
        dados.setTelefoneDevedor(String.valueOf(contrato.getTelefoneDevedor()));

        RegistrarContrato r = new RegistrarContrato();
        r.setDados(dados);
        return r;
    }

    @Override
    public String getUrl() {
        return DEFAULT_URI;
    }

    public String getUriRegistrarContrato() {
        return URI_REGISTRAR_CONTRATO;
    }

    public String getUriConsultarSequencial() {
        return URI_CONSULTAR_SEQUENCIAL;
    }

    public String getUsuario() {
        return USUARIO;
    }

    public String getSenha() {
        return SENHA;
    }

    public String getClientPath() {
        return CLIENT_PATH;
    }

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public Integer getConnectionTimeout() {
        return connectionTimeout;
    }

    public String getFileDirRead() {
        return FILE_DIR_READ;
    }

    public void setFileDirRead(String FILE_DIR_READ) {
        this.FILE_DIR_READ = FILE_DIR_READ;
    }

    @Override
    public RegistroEnvioService getRegistroEnvioService() {
        return registroEnvioService;
    }

    @Override
    public UsuarioService getUsuarioService() {
        return usuarioService;
    }

    @Override
    public MensagemRetornoRepository getMensagemRetornoRepository() {
        return mensagemRetornoRepository;
    }

    @Override
    public void setUsuario(String usuario) {
        this.USUARIO = usuario;
    }

    @Override
    public void setSenha(String senha) {
        this.SENHA = senha;
    }

    @Override
    public AcessoSenhaService getAcessoSenhaService() {
        return acessoSenhaService;
    }

    @Override
    public Uf getUf() {
        return Uf.SC;
    }
}
