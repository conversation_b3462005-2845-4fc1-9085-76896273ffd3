package com.registrocontrato.registro.service.apis.arqdigital.request;

public enum TipoCampos {

    DC_CODIGO_AGENTE, DF_CLAUSULA, DF_CORRECAO, DF_DATA_ASSINATURA, DF_DATA_VIG_CONTRATO, DF_LOCAL_ASSINATURA, DF_QTD_MESES, DF_UF_FINANCIAMENTO, DF_TOTAL, DF_TAXA_CONTRATO, DF_COMISSAO, DF_INDICATIVO_COMISSAO, DF_PENALIDADE, DF_INDICATIVO_PENALIDADE, DF_TAXA_MORA_DIA, DF_INDICATIVO_MORA, DF_TAXA_JUROS_MULTA, DF_INDICATIVO_MULTA, DF_DT_ADITIVO, DF_VALORES_ENCARGOS, DF_COTA_CONSORCIO, DF_GRUPO_CONSORCIO, DF_INDICE, DF_MUNICIPIO, DF_CODIGO_MUNICIPIO, DF_LIBERACAO_CRED, DF_VENCTO_ULTIMA, DF_VENCTO_PRIMEIRA, DF_PARCELA, DF_IOF, DF_TAXA_JUROS_ANO, DF_TAXA_JUROS_MES, CAMPO_FILHO_DF, DF_QTD_PARCELAS;
}
