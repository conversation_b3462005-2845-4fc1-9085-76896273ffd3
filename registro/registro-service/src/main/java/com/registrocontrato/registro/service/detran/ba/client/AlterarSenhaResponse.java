//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, vhudson-jaxb-ri-2.1-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2020.05.20 at 12:15:28 PM BRT 
//


package com.registrocontrato.registro.service.detran.ba.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="alterarSenhaResult" type="{http://www.wsdetrancontrato.com/}alterarSenha_Output" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "alterarSenhaResult"
})
@XmlRootElement(name = "alterarSenhaResponse")
public class AlterarSenhaResponse {

    protected AlterarSenhaOutput alterarSenhaResult;

    /**
     * Gets the value of the alterarSenhaResult property.
     * 
     * @return
     *     possible object is
     *     {@link AlterarSenhaOutput }
     *     
     */
    public AlterarSenhaOutput getAlterarSenhaResult() {
        return alterarSenhaResult;
    }

    /**
     * Sets the value of the alterarSenhaResult property.
     * 
     * @param value
     *     allowed object is
     *     {@link AlterarSenhaOutput }
     *     
     */
    public void setAlterarSenhaResult(AlterarSenhaOutput value) {
        this.alterarSenhaResult = value;
    }

}
