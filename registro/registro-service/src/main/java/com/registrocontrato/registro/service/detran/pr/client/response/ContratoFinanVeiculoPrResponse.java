package com.registrocontrato.registro.service.detran.pr.client.response;

import java.util.ArrayList;
import java.util.List;

public class ContratoFinanVeiculoPrResponse {

	private String resultado;

	private List<MensagenPrResponse> mensagens = new ArrayList<>();

	public String getResultado() {
		return resultado;
	}

	public void setResultado(String resultado) {
		this.resultado = resultado;
	}

	public List<MensagenPrResponse> getMensagens() {
		return mensagens;
	}

	public void setMensagens(List<MensagenPrResponse> mensagens) {
		this.mensagens = mensagens;
	}
	
	public void addMensagens(MensagenPrResponse mensagem) {
		this.mensagens.add(mensagem);
	}

}
