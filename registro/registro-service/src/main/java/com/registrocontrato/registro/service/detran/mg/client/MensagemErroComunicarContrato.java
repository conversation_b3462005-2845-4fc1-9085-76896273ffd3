package com.registrocontrato.registro.service.detran.mg.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
		"flagTransacao",
	    "chassi",
	    "identificaRemarcacao",
	    "ufLicenciamento",
	    "ufPlaca",
	    "placa",
	    "renavam",
	    "anoFabricacao",
	    "anoModelo",
	    "codigoAgente",
	    "nomeAgente",
	    "cnpjAgente",
	    "numeroContrato",
	    "dataContrato",
	    "quantidadeParcela",
	    "numeroRestricao",
	    "tipoGravame",
	    "cpfCnpjDevedor",
	    "nomeDevedor",
	    "taxaJurosMes",
	    "taxaJurosAno",
	    "taxaJurosMulta",
	    "taxaJurosMoraDia",
	    "valorTaxaContrato",
	    "valorTotalFinanciamento",
	    "valorIof",
	    "valorParcela",
	    "dataVencimentoPrimeiraParcela",
	    "dataVencimentoUltimaParcela",
	    "dataLiberacaoCredito",
	    "ufLiberacaoCredito",
	    "cidadeLiberacaoCredito",
	    "indices",
	    "numeroGrupoConsorcio",
	    "numeroCotaConsorcio",
	    "numeroRegistroContratoSircof",
	    "numeroAditivoContrato",
	    "dataAditivoContrato",
	    "numeroRegistroAditivoSircof",
	    "nomeLogradouroCredor",
	    "numeroImovelCredor",
	    "complementoImovelCredor",
	    "bairroImovelCredor",
	    "codigoMunicipioCredor",
	    "ufImovelCredor",
	    "cepImovelCredor",
	    "dddTelefoneCredor",
	    "numeroTelefoneCredor",
	    "nomeLogradouroDevedor",
	    "numeroImovelDevedor",
	    "complementoImovelDevedor",
	    "bairroImovelDevedor",
	    "codigoMunicipioDevedor",
	    "ufImovelDevedor",
	    "cepImovelDevedor",
	    "dddImovelDevedor",
	    "numeroTelefoneDevedor",
	    "taxaMultaContratoAditivo",
	    "taxaMoraContratoAditivo",
	    "indicativoPenalidade",
	    "penalidade",
	    "indicativoComissao",
	    "comissao",
	    "cnpjCpfOperadoraRegistroContrato",
	    "nomeOperadoraContrato"
})
public class MensagemErroComunicarContrato {

	 protected Integer flagTransacao;
	protected String chassi;
    protected String identificaRemarcacao;
    protected String ufLicenciamento;
    protected String ufPlaca;
    protected String placa;
    protected String renavam;
    protected String anoFabricacao;
    protected String anoModelo;
    protected String codigoAgente;
    protected String nomeAgente;
    protected String cnpjAgente;
    protected String numeroContrato;
    protected String dataContrato;
    protected String quantidadeParcela;
    protected String numeroRestricao;
    protected String tipoGravame;
    protected String cpfCnpjDevedor;
    protected String nomeDevedor;
    protected String taxaJurosMes;
    protected String taxaJurosAno;
    protected String taxaJurosMulta;
    protected String taxaJurosMoraDia;
    protected String valorTaxaContrato;
    protected String valorTotalFinanciamento;
    protected String valorIof;
    protected String valorParcela;
    protected String dataVencimentoPrimeiraParcela;
    protected String dataVencimentoUltimaParcela;
    protected String dataLiberacaoCredito;
    protected String ufLiberacaoCredito;
    protected String cidadeLiberacaoCredito;
    protected String indices;
    protected String numeroGrupoConsorcio;
    protected String numeroCotaConsorcio;
    protected String numeroRegistroContratoSircof;
    protected String numeroAditivoContrato;
    protected String dataAditivoContrato;
    protected String numeroRegistroAditivoSircof;
    protected String nomeLogradouroCredor;
    protected String numeroImovelCredor;
    protected String complementoImovelCredor;
    protected String bairroImovelCredor;
    protected String codigoMunicipioCredor;
    protected String ufImovelCredor;
    protected String cepImovelCredor;
    protected String dddTelefoneCredor;
    protected String numeroTelefoneCredor;
    protected String nomeLogradouroDevedor;
    protected String numeroImovelDevedor;
    protected String complementoImovelDevedor;
    protected String bairroImovelDevedor;
    protected String codigoMunicipioDevedor;
    protected String ufImovelDevedor;
    protected String cepImovelDevedor;
    protected String dddImovelDevedor;
    protected String numeroTelefoneDevedor;
    protected String taxaMultaContratoAditivo;
    protected String taxaMoraContratoAditivo;
    protected String indicativoPenalidade;
    protected String penalidade;
    protected String indicativoComissao;
    protected String comissao;
    protected String cnpjCpfOperadoraRegistroContrato;
    protected String nomeOperadoraContrato;
	public String getChassi() {
		return chassi;
	}
	public void setChassi(String chassi) {
		this.chassi = chassi;
	}
	
	public String getUfLicenciamento() {
		return ufLicenciamento;
	}
	public void setUfLicenciamento(String ufLicenciamento) {
		this.ufLicenciamento = ufLicenciamento;
	}
	public String getUfPlaca() {
		return ufPlaca;
	}
	public void setUfPlaca(String ufPlaca) {
		this.ufPlaca = ufPlaca;
	}
	public String getPlaca() {
		return placa;
	}
	public void setPlaca(String placa) {
		this.placa = placa;
	}
	public String getRenavam() {
		return renavam;
	}
	public void setRenavam(String renavam) {
		this.renavam = renavam;
	}
	
	public String getNomeAgente() {
		return nomeAgente;
	}
	public void setNomeAgente(String nomeAgente) {
		this.nomeAgente = nomeAgente;
	}
	public String getCnpjAgente() {
		return cnpjAgente;
	}
	public void setCnpjAgente(String cnpjAgente) {
		this.cnpjAgente = cnpjAgente;
	}
	public String getNumeroContrato() {
		return numeroContrato;
	}
	public void setNumeroContrato(String numeroContrato) {
		this.numeroContrato = numeroContrato;
	}
	
	public String getTipoGravame() {
		return tipoGravame;
	}
	public void setTipoGravame(String tipoGravame) {
		this.tipoGravame = tipoGravame;
	}
	public String getCpfCnpjDevedor() {
		return cpfCnpjDevedor;
	}
	public void setCpfCnpjDevedor(String cpfCnpjDevedor) {
		this.cpfCnpjDevedor = cpfCnpjDevedor;
	}
	public String getNomeDevedor() {
		return nomeDevedor;
	}
	public void setNomeDevedor(String nomeDevedor) {
		this.nomeDevedor = nomeDevedor;
	}
	public String getTaxaJurosMes() {
		return taxaJurosMes;
	}
	public void setTaxaJurosMes(String taxaJurosMes) {
		this.taxaJurosMes = taxaJurosMes;
	}
	public String getTaxaJurosAno() {
		return taxaJurosAno;
	}
	public void setTaxaJurosAno(String taxaJurosAno) {
		this.taxaJurosAno = taxaJurosAno;
	}
	public String getTaxaJurosMulta() {
		return taxaJurosMulta;
	}
	public void setTaxaJurosMulta(String taxaJurosMulta) {
		this.taxaJurosMulta = taxaJurosMulta;
	}
	public String getTaxaJurosMoraDia() {
		return taxaJurosMoraDia;
	}
	public void setTaxaJurosMoraDia(String taxaJurosMoraDia) {
		this.taxaJurosMoraDia = taxaJurosMoraDia;
	}
	public String getValorTaxaContrato() {
		return valorTaxaContrato;
	}
	public void setValorTaxaContrato(String valorTaxaContrato) {
		this.valorTaxaContrato = valorTaxaContrato;
	}
	public String getValorTotalFinanciamento() {
		return valorTotalFinanciamento;
	}
	public void setValorTotalFinanciamento(String valorTotalFinanciamento) {
		this.valorTotalFinanciamento = valorTotalFinanciamento;
	}
	public String getValorIof() {
		return valorIof;
	}
	public void setValorIof(String valorIof) {
		this.valorIof = valorIof;
	}
	public String getValorParcela() {
		return valorParcela;
	}
	public void setValorParcela(String valorParcela) {
		this.valorParcela = valorParcela;
	}
	
	public String getUfLiberacaoCredito() {
		return ufLiberacaoCredito;
	}
	public void setUfLiberacaoCredito(String ufLiberacaoCredito) {
		this.ufLiberacaoCredito = ufLiberacaoCredito;
	}
	public String getCidadeLiberacaoCredito() {
		return cidadeLiberacaoCredito;
	}
	public void setCidadeLiberacaoCredito(String cidadeLiberacaoCredito) {
		this.cidadeLiberacaoCredito = cidadeLiberacaoCredito;
	}
	public String getIndices() {
		return indices;
	}
	public void setIndices(String indices) {
		this.indices = indices;
	}
	public String getNumeroGrupoConsorcio() {
		return numeroGrupoConsorcio;
	}
	public void setNumeroGrupoConsorcio(String numeroGrupoConsorcio) {
		this.numeroGrupoConsorcio = numeroGrupoConsorcio;
	}
	public String getNumeroCotaConsorcio() {
		return numeroCotaConsorcio;
	}
	public void setNumeroCotaConsorcio(String numeroCotaConsorcio) {
		this.numeroCotaConsorcio = numeroCotaConsorcio;
	}
	public String getNumeroRegistroContratoSircof() {
		return numeroRegistroContratoSircof;
	}
	public void setNumeroRegistroContratoSircof(String numeroRegistroContratoSircof) {
		this.numeroRegistroContratoSircof = numeroRegistroContratoSircof;
	}
	public String getNumeroAditivoContrato() {
		return numeroAditivoContrato;
	}
	public void setNumeroAditivoContrato(String numeroAditivoContrato) {
		this.numeroAditivoContrato = numeroAditivoContrato;
	}
	
	public String getNumeroRegistroAditivoSircof() {
		return numeroRegistroAditivoSircof;
	}
	public void setNumeroRegistroAditivoSircof(String numeroRegistroAditivoSircof) {
		this.numeroRegistroAditivoSircof = numeroRegistroAditivoSircof;
	}
	public String getNomeLogradouroCredor() {
		return nomeLogradouroCredor;
	}
	public void setNomeLogradouroCredor(String nomeLogradouroCredor) {
		this.nomeLogradouroCredor = nomeLogradouroCredor;
	}
	public String getNumeroImovelCredor() {
		return numeroImovelCredor;
	}
	public void setNumeroImovelCredor(String numeroImovelCredor) {
		this.numeroImovelCredor = numeroImovelCredor;
	}
	public String getComplementoImovelCredor() {
		return complementoImovelCredor;
	}
	public void setComplementoImovelCredor(String complementoImovelCredor) {
		this.complementoImovelCredor = complementoImovelCredor;
	}
	public String getBairroImovelCredor() {
		return bairroImovelCredor;
	}
	public void setBairroImovelCredor(String bairroImovelCredor) {
		this.bairroImovelCredor = bairroImovelCredor;
	}
	public String getCodigoMunicipioCredor() {
		return codigoMunicipioCredor;
	}
	public void setCodigoMunicipioCredor(String codigoMunicipioCredor) {
		this.codigoMunicipioCredor = codigoMunicipioCredor;
	}
	public String getUfImovelCredor() {
		return ufImovelCredor;
	}
	public void setUfImovelCredor(String ufImovelCredor) {
		this.ufImovelCredor = ufImovelCredor;
	}
	public String getDddTelefoneCredor() {
		return dddTelefoneCredor;
	}
	public void setDddTelefoneCredor(String dddTelefoneCredor) {
		this.dddTelefoneCredor = dddTelefoneCredor;
	}
	public String getNomeLogradouroDevedor() {
		return nomeLogradouroDevedor;
	}
	public void setNomeLogradouroDevedor(String nomeLogradouroDevedor) {
		this.nomeLogradouroDevedor = nomeLogradouroDevedor;
	}
	public String getNumeroImovelDevedor() {
		return numeroImovelDevedor;
	}
	public void setNumeroImovelDevedor(String numeroImovelDevedor) {
		this.numeroImovelDevedor = numeroImovelDevedor;
	}
	public String getComplementoImovelDevedor() {
		return complementoImovelDevedor;
	}
	public void setComplementoImovelDevedor(String complementoImovelDevedor) {
		this.complementoImovelDevedor = complementoImovelDevedor;
	}
	public String getBairroImovelDevedor() {
		return bairroImovelDevedor;
	}
	public void setBairroImovelDevedor(String bairroImovelDevedor) {
		this.bairroImovelDevedor = bairroImovelDevedor;
	}
	public String getUfImovelDevedor() {
		return ufImovelDevedor;
	}
	public void setUfImovelDevedor(String ufImovelDevedor) {
		this.ufImovelDevedor = ufImovelDevedor;
	}
	
	public String getDddImovelDevedor() {
		return dddImovelDevedor;
	}
	public void setDddImovelDevedor(String dddImovelDevedor) {
		this.dddImovelDevedor = dddImovelDevedor;
	}
	
	public String getTaxaMultaContratoAditivo() {
		return taxaMultaContratoAditivo;
	}
	public void setTaxaMultaContratoAditivo(String taxaMultaContratoAditivo) {
		this.taxaMultaContratoAditivo = taxaMultaContratoAditivo;
	}
	public String getTaxaMoraContratoAditivo() {
		return taxaMoraContratoAditivo;
	}
	public void setTaxaMoraContratoAditivo(String taxaMoraContratoAditivo) {
		this.taxaMoraContratoAditivo = taxaMoraContratoAditivo;
	}
	public String getIndicativoPenalidade() {
		return indicativoPenalidade;
	}
	public void setIndicativoPenalidade(String indicativoPenalidade) {
		this.indicativoPenalidade = indicativoPenalidade;
	}
	public String getPenalidade() {
		return penalidade;
	}
	public void setPenalidade(String penalidade) {
		this.penalidade = penalidade;
	}
	public String getIndicativoComissao() {
		return indicativoComissao;
	}
	public void setIndicativoComissao(String indicativoComissao) {
		this.indicativoComissao = indicativoComissao;
	}
	public String getComissao() {
		return comissao;
	}
	public void setComissao(String comissao) {
		this.comissao = comissao;
	}
	public String getCnpjCpfOperadoraRegistroContrato() {
		return cnpjCpfOperadoraRegistroContrato;
	}
	public void setCnpjCpfOperadoraRegistroContrato(String cnpjCpfOperadoraRegistroContrato) {
		this.cnpjCpfOperadoraRegistroContrato = cnpjCpfOperadoraRegistroContrato;
	}
	public String getNomeOperadoraContrato() {
		return nomeOperadoraContrato;
	}
	public void setNomeOperadoraContrato(String nomeOperadoraContrato) {
		this.nomeOperadoraContrato = nomeOperadoraContrato;
	}
	public String getIdentificaRemarcacao() {
		return identificaRemarcacao;
	}
	public void setIdentificaRemarcacao(String identificaRemarcacao) {
		this.identificaRemarcacao = identificaRemarcacao;
	}
	public String getAnoFabricacao() {
		return anoFabricacao;
	}
	public void setAnoFabricacao(String anoFabricacao) {
		this.anoFabricacao = anoFabricacao;
	}
	public String getAnoModelo() {
		return anoModelo;
	}
	public void setAnoModelo(String anoModelo) {
		this.anoModelo = anoModelo;
	}
	public String getCodigoAgente() {
		return codigoAgente;
	}
	public void setCodigoAgente(String codigoAgente) {
		this.codigoAgente = codigoAgente;
	}
	public String getDataContrato() {
		return dataContrato;
	}
	public void setDataContrato(String dataContrato) {
		this.dataContrato = dataContrato;
	}
	public String getQuantidadeParcela() {
		return quantidadeParcela;
	}
	public void setQuantidadeParcela(String quantidadeParcela) {
		this.quantidadeParcela = quantidadeParcela;
	}
	public String getNumeroRestricao() {
		return numeroRestricao;
	}
	public void setNumeroRestricao(String numeroRestricao) {
		this.numeroRestricao = numeroRestricao;
	}
	public String getDataVencimentoPrimeiraParcela() {
		return dataVencimentoPrimeiraParcela;
	}
	public void setDataVencimentoPrimeiraParcela(String dataVencimentoPrimeiraParcela) {
		this.dataVencimentoPrimeiraParcela = dataVencimentoPrimeiraParcela;
	}
	public String getDataVencimentoUltimaParcela() {
		return dataVencimentoUltimaParcela;
	}
	public void setDataVencimentoUltimaParcela(String dataVencimentoUltimaParcela) {
		this.dataVencimentoUltimaParcela = dataVencimentoUltimaParcela;
	}
	public String getDataLiberacaoCredito() {
		return dataLiberacaoCredito;
	}
	public void setDataLiberacaoCredito(String dataLiberacaoCredito) {
		this.dataLiberacaoCredito = dataLiberacaoCredito;
	}
	public String getDataAditivoContrato() {
		return dataAditivoContrato;
	}
	public void setDataAditivoContrato(String dataAditivoContrato) {
		this.dataAditivoContrato = dataAditivoContrato;
	}
	public String getCepImovelCredor() {
		return cepImovelCredor;
	}
	public void setCepImovelCredor(String cepImovelCredor) {
		this.cepImovelCredor = cepImovelCredor;
	}
	public String getNumeroTelefoneCredor() {
		return numeroTelefoneCredor;
	}
	public void setNumeroTelefoneCredor(String numeroTelefoneCredor) {
		this.numeroTelefoneCredor = numeroTelefoneCredor;
	}
	public String getCodigoMunicipioDevedor() {
		return codigoMunicipioDevedor;
	}
	public void setCodigoMunicipioDevedor(String codigoMunicipioDevedor) {
		this.codigoMunicipioDevedor = codigoMunicipioDevedor;
	}
	public String getCepImovelDevedor() {
		return cepImovelDevedor;
	}
	public void setCepImovelDevedor(String cepImovelDevedor) {
		this.cepImovelDevedor = cepImovelDevedor;
	}
	public String getNumeroTelefoneDevedor() {
		return numeroTelefoneDevedor;
	}
	public void setNumeroTelefoneDevedor(String numeroTelefoneDevedor) {
		this.numeroTelefoneDevedor = numeroTelefoneDevedor;
	}
    
    
	public Integer getFlagTransacao() {
		return flagTransacao;
	}
	
	public void setFlagTransacao(Integer flagTransacao) {
		this.flagTransacao = flagTransacao;
	}
	
    
}
