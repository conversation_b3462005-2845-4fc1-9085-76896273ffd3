//
// Este arquivo foi gerado pela Arquitetura JavaTM para Implementação de Referência (JAXB) de Bind XML, v2.2.11 
// Consulte <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Todas as modificações neste arquivo serão perdidas após a recompilação do esquema de origem. 
// Gerado em: 2018.05.14 às 06:31:46 PM BRT 
//


package com.registrocontrato.registro.service.detran.sp.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de consultarGravameResponse complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="consultarGravameResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="RetornoConsultaGravame" type="{http://ws.sircof.gever.detran.prodesp.sp.gov.br/}RetornoConsultaGravame" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "consultarGravameResponse", propOrder = {
    "retornoConsultaGravame"
})
public class ConsultarGravameResponse {

    @XmlElement(name = "RetornoConsultaGravame")
    protected RetornoConsultaGravame retornoConsultaGravame;

    /**
     * Obtém o valor da propriedade retornoConsultaGravame.
     * 
     * @return
     *     possible object is
     *     {@link RetornoConsultaGravame }
     *     
     */
    public RetornoConsultaGravame getRetornoConsultaGravame() {
        return retornoConsultaGravame;
    }

    /**
     * Define o valor da propriedade retornoConsultaGravame.
     * 
     * @param value
     *     allowed object is
     *     {@link RetornoConsultaGravame }
     *     
     */
    public void setRetornoConsultaGravame(RetornoConsultaGravame value) {
        this.retornoConsultaGravame = value;
    }

}
