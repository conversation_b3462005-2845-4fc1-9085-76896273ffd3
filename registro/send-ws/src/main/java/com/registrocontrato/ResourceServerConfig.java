package com.registrocontrato;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;

import javax.sql.DataSource;

@Configuration
@EnableResourceServer
public class ResourceServerConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private DataSource dataSource;

    private static final String[] AUTH_WHITELIST = {
            "/swagger-ui.html",
            "/index.html",
            "/v3/api-docs/**",
            "/swagger-ui/**"
    };

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {

        auth.jdbcAuthentication().dataSource(dataSource)
                .passwordEncoder(passwordEncoder())
                .usersByUsernameQuery(
                        "select cpfapi as username, passwordapi as password, 1 as enabled "
                                + "from seguranca.usuario where cpfapi = ?")
                .authoritiesByUsernameQuery(
                        "select distinct u.cpfapi as username, p.nome as authority from seguranca.usuario_permissoes up "
                                + "inner join seguranca.usuario u on u.id = up.usuario_id "
                                + "inner join seguranca.permissao p on p.id = up.permissoes_id "
                                + "where p.ativo = 'S' and p.sistema = 'REGISTRO' and u.cpfapi=? and p.nome = 'COMUNICAR_CONTRATO_REST' ")
                .groupAuthoritiesByUsername("select distinct g.id, g.nome as group_name, o.nome as authority "
                        + "from seguranca.usuario u inner join seguranca.membros_grupo mg on mg.usuario_id = u.id "
                        + "inner join seguranca.grupo g on g.id = mg.grupos_id "
                        + "inner join seguranca.permissoes_grupo pg on pg.grupo_id = g.id "
                        + "inner join seguranca.permissao o on o.id = pg.permissoes_id "
                        + "where o.ativo = 'S' and o.sistema = 'REGISTRO' and u.cpfapi=?  and o.nome = 'COMUNICAR_CONTRATO_REST'");

        auth
                .inMemoryAuthentication()
                .withUser("monitor")
                .password("tksVSq7NROWlgfb81cigoiPPZrI7h65z8zWOsuZp1Yb0AbNrqIR09ZURhpEOHCl9")
                .roles("MONITORAMENTO");
    }

    public BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests().antMatchers(HttpMethod.GET, "/actuator/health").hasRole("MONITORAMENTO")
                .and()
                .requestMatchers()
                .antMatchers(HttpMethod.GET, "/actuator/health")
                .and()
                .authorizeRequests().anyRequest().authenticated().and().csrf().disable().httpBasic();

        http.requestMatchers()
                .antMatchers(AUTH_WHITELIST)
                .and()
                .authorizeRequests()
                .antMatchers("/swagger-ui.html?url=**").denyAll()
                .and()
                .authorizeRequests()
                .anyRequest().authenticated()
                .and()
                .httpBasic();

    }

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers(HttpMethod.POST, "/api/v1.0/autenticacao");
    }
}
