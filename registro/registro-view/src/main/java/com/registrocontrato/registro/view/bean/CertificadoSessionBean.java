package com.registrocontrato.registro.view.bean;

import java.io.IOException;
import java.io.InputStream;
import java.util.Date;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.context.annotation.SessionScope;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.infra.bean.BaseBean;
import com.registrocontrato.registro.entity.Assinatura;

import br.com.certisign.certisignon.tools.certificados.NewCripto2;

@Controller
@SessionScope
public class CertificadoSessionBean extends BaseBean {

	private static final long serialVersionUID = 1L;

	private String chaveCertificado;

	private String nome;

	@Value("${sign.url.return}")
	private String urlRetorno;

	private String decriptado;

	private ObjectMapper mapper = new ObjectMapper();

	public String loadCertificate() {
		try {
			if (chaveCertificado != null) {
				InputStream chave = getExternalContext().getResourceAsStream("/WEB-INF/5380.pk");
				String schave = IOUtils.toString(chave, "UTF-8");
				decriptado = NewCripto2.decrypt(chaveCertificado, schave);
				decriptado = decriptado.replace("CertificadoBean : ", "");
				decriptado = decriptado.replace("'", "\"");
				nome = getAssinatura().getNome();
				addMessageWarn("Certificado carregado com sucesso.");
			} else {
				addMessageWarn("Erro ao ler o certificado. Procure o suporte.");
			}
		} catch (Exception e) {
			String message = e.getMessage();
			if (e.getMessage().equals("Input length must be multiple of 16 when decrypting with padded cipher")) {
				message = "Erro ao ler o certificado. Reinicie o seu navegador e tente novamente.";
			}
			addMessageError(message);
		}
		return "/index.xhtml";
	}
	
	public Assinatura getAssinatura() {
		try {
			if(decriptado != null){
				Assinatura assinatura = mapper.readValue(decriptado, Assinatura.class);
				assinatura.setUsuario(getUsername());
				assinatura.setData(new Date());
				return assinatura;
			}
		} catch (IOException e) {
			logger.error(e.getMessage(), e);
		}
		return null;
	}
	
	public Boolean getLoaded() {
		return this.nome != null;
	}

	public String getChaveCertificado() {
		return chaveCertificado;
	}

	public void setChaveCertificado(String chaveCertificado) {
		this.chaveCertificado = chaveCertificado;
	}
	
	public String getNome() {
		return nome;
	}

	public String getUrlRetorno() {
		return urlRetorno;
	}

}
