package com.registrocontrato.registro.view.bean.rsng;

import com.registrocontrato.commons.ws.rsng.InserirRemessaRsngService;
import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.rsng.ArquivoRemessaRsng;
import com.registrocontrato.infra.entity.rsng.ArquivoRemessaRsngDTO;
import com.registrocontrato.infra.entity.rsng.ItemArquivoRemessaRsng;
import com.registrocontrato.infra.entity.rsng.TemplateRsng;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.rsng.ArquivoRemessaRsngService;
import com.registrocontrato.registro.service.rsng.TemplateRemessaRsngService;
import com.registrocontrato.registro.view.bean.HelperSessionBean;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;

import static com.registrocontrato.infra.util.PlaceconUtil.addRetToNameFile;

@Controller
@ViewScope
public class InserirRemessaRsngBean extends BaseCrud<ArquivoRemessaRsng, ArquivoRemessaRsngDTO> {
    private static final long serialVersionUID = 1L;

    private static Logger logger = LoggerFactory.getLogger(ContratoService.class);

    private Long idTemplate;

    private String fileName;

    private Future<String> resultadoRemessa;

    @Value("${file.remessa.dir-read:null}")
    private String FILE_REMESSA_DIR_READ;

    @Autowired
    private InserirRemessaRsngService remessaService;

    @Autowired
    protected ArquivoRemessaRsngService service;

    @Autowired
    private TemplateRemessaRsngService templateRemessaRsngService;

    @Autowired
    private HelperSessionBean helperSessionBean;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private FinanceiraService financeiraService;

    private List<Financeira> financeiras;

    private Financeira financeira;

    @Override
    public void search() {
        ArquivoRemessaRsngDTO filter = getFilter();
        filter.setUsuario(getUsername());
        filter.setId(getIdToEdit());

        if (filter.getDataFim() != null && filter.getDataInicio() != null) {
            if (filter.getDataFim().before(filter.getDataInicio())) {
                addMessageError("A data inicial deve ser anterior a data final");
                return;
            }
        }

        List<ItemArquivoRemessaRsng> registros = service.findItens(filter);
        getEntity().setRegistros(registros);
        super.search();
    }

    @Override
    public void postInitialization() {
        super.postInitialization();
        ArquivoRemessaRsngDTO filter = (ArquivoRemessaRsngDTO) getFilter();
        filter.setUsuario(getUsername());

        Usuario usuario = usuarioService.findByCpfFinanceiras(getUsername());
        if (usuario.isPerfilFinanceira()) {
            financeiras = usuario.getFinanceiras();
            if (financeiras.size() == 1) {
                if (getEntity().getRegistros().size() >= 1)
                    getEntity().getRegistros().get(0).setFinanceira(financeiraService.findOne(financeiras.get(0).getId()));
            }
        } else {
            financeiras = financeiraService.findAtivos();
        }
    }

    @Override
    public void loadDetails() {
        super.loadDetails();

        if (getEntity() != null) {
            getEntity().setRegistros(service.getRegistros(getEntity(), getUsername()));
            idTemplate = getEntity().getTemplateRemessa().getId();
        }
    }

    public void remessaArquivo(ArquivoRemessaRsng ar) {
        this.entity = ar;
    }

    @AuditContrato(action = "Importação de Contrato por Remessa")
    public void handleFileUploadContratos(FileUploadEvent event) {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Arrays.asList("txt", "csv", "xls", "xlsx"));
            fileName = file.getFileName();
            entity = remessaService.save(fileName, event.getFile().getInputStream(), idTemplate);
            resultadoRemessa = remessaService.saveItensRemessaAssync(fileName, event.getFile().getInputStream(), idTemplate, getUsername(), entity);
        } catch (Exception e) {
            remessaService.encerrar(entity.getId());
            logger.error(e.getMessage(), e);
            addMessageError(e.getMessage() != null ? e.getMessage() : "Houve um erro no sistema. Favor entrar em contato com suporte");
        }
    }

    public List<TemplateRsng> getTemplates() {
        return templateRemessaRsngService.findAtivosFinanceira(helperSessionBean.getFinanceiras());
    }

    @Override
    public BaseService<ArquivoRemessaRsng, ArquivoRemessaRsngDTO> getService() {
        return service;
    }

    public Long getIdTemplate() {
        return idTemplate;
    }

    public void setIdTemplate(Long idTemplate) {
        this.idTemplate = idTemplate;
    }

    public void recarregar() {
        if (entity != null && entity.getId() != null) {
            entity = service.findOne(entity.getId());
            entity.setRegistros(service.getRegistros(getEntity(), getUsername()));
        }
    }

    public Future<String> getResultadoRemessa() {
        return resultadoRemessa;
    }

    public String getFileName() {
        return fileName;
    }

    public StreamedContent getFile() {
        try {
            InputStream stream = new FileInputStream(FILE_REMESSA_DIR_READ + entity.getHash());
            return DefaultStreamedContent.builder()
                    .contentType("text/plain")
                    .name(entity.getNome())
                    .stream(() -> stream).build();

        } catch (FileNotFoundException e) {
            addMessageError("Arquivo não disponível");
        }
        return null;
    }

    public StreamedContent getFileReturn() {
        try {
            InputStream stream = remessaService.gerarArquivoRetorno(entity);
            return DefaultStreamedContent.builder()
                    .contentType("text/plain")
                    .name(addRetToNameFile(entity.getNome()))
                    .stream(() -> stream).build();
        } catch (ServiceException e) {
            logger.error("Erro ao gerar arquivo de retorno", e);
        }

        return null;
    }

    public List<Financeira> getFinanceiras() {
        return financeiras;
    }

    public void setFinanceiras(List<Financeira> financeiras) {
        this.financeiras = financeiras;
    }

    public Financeira getFinanceira() {
        return financeira;
    }

    public void setFinanceira(Financeira financeira) {
        this.financeira = financeira;
    }
}
