package com.registrocontrato.registro.view.bean;

import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.registro.entity.Assinatura;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.service.dto.ContratoDTO;

@Controller
@ViewScope
public class AnulacaoContratoBean extends ContratoBaseBean<Contrato, ContratoDTO> {

	private static final long serialVersionUID = 1L;

	@Autowired
	private CertificadoSessionBean certificadoSessionBean;

	@Override
	public void postInitialization() {
		setFilter(new ContratoDTO());
	}

	@Override
	public void loadDetails() {
		super.loadDetails();
		if (getEntity().getSituacao() != Situacao.ATIVO && getEntity().getSituacao() != Situacao.ANULADO) {
			unauthorized();
		}
	}

	@Override
	public void search() {
		ContratoDTO filter = (ContratoDTO) getFilter();
		filter.setSituacao(Situacao.ATIVO);
		filter.setUsuario(getUsername());

		setList(new LazyDataModel<Contrato>() {

			private static final long serialVersionUID = 1L;

			@Override
			public int count(Map<String, FilterMeta> filterBy) {
				return 0;
			}

			@Override
			public List<Contrato> load(int first, int pageSize, Map<String, SortMeta> sortBy, Map<String, FilterMeta> filterBy) {
				Page<Contrato> page = service.findAll(first, getSize(), filter);
				setRowCount((int) page.getTotalElements());
				setPageSize(getSize());
				return page.getContent();
			}
		});
	}

	@AuditContrato(action = "Anulação de Contrato")
	public String anular() {
		try {
			service.anular(getEntity());
			addMessageInfo("Contrato anulado com sucesso");
		} catch (Exception e) {
			addMessageError("Erro ao realizar anulação do contrato.");
		}
		return "/anulacao/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
	}
	
	@AuditContrato(action = "Anulacao de Contrato com Assinatura")
	public String anularAssinar() {
		try {
			Assinatura assinatura = certificadoSessionBean.getAssinatura();
			assinatura.setIdContrato(getEntity().getId());
			service.anularAssinar(getEntity(), assinatura);
			addMessageInfo("Contrato Anulado com assinatura.");
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		}
		return "/anulacao/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
	}

}