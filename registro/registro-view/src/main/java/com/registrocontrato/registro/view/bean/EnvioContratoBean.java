package com.registrocontrato.registro.view.bean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.registrocontrato.infra.util.PlaceconUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;

import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.registro.entity.Contrato;
import com.registrocontrato.registro.enums.Situacao;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.entity.Veiculo;
import com.registrocontrato.registro.service.dto.ContratoDTO;

@Controller
@ViewScope
public class EnvioContratoBean extends ContratoBaseBean<Contrato, ContratoDTO> {

	private static final List<Situacao> SITUACOES = Arrays.asList(Situacao.ERRO);

	private static final long serialVersionUID = 1L;
	
	private Contrato contratoOriginal;

	private List<Contrato> contratosSelecionados;

	@Override
	public void postInitialization() {
		super.postInitialization();
		ContratoDTO filter = (ContratoDTO) getFilter();
		filter.setSituacoes(SITUACOES);
		filter.setUsuario(getUsername());
	}

	@Override
	public void loadDetails() {
		super.loadDetails();
		if (!SITUACOES.contains(getEntity().getSituacao())) {
			unauthorized();
		}
		contratoOriginal = new Contrato();
		BeanUtils.copyProperties(entity, contratoOriginal);
	}
	
	@AuditContrato(action = "Registro de Contrato")
	public String send(Long id) {
		entity = service.findOne(id);
		return send();
	}
	
	@AuditContrato(action = "Registro de Contrato")
	public String send() {
		try {
			entity = service.transmitir(getEntity(), getUsername());
			if(entity.getSituacao() == Situacao.ATIVO) {
				addMessageInfo("Contrato Enviado com sucesso");
				return "/contrato/form-detail.xhtml?faces-redirect=true&id=" + getEntity().getId();
			} else {
				addMessageWarn("Os dados do contrato foram salvos, mas exitem pendências no envio ao DETRAN.");
				return "/contrato/form-update.xhtml?faces-redirect=true&id=" + getEntity().getId();
			}
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		} catch (Exception e) {
			addMessageError(e.getMessage());
		}
		return null;
	}

	@AuditContrato(action = "Registro de Contrato")
	public String sendAll() {
		getList().getWrappedData().forEach(c -> {
			try {
				service.transmitir(service.findOne(c.getId()), getUsername());
			} catch (ServiceException e) {
				logger.error(e);
			}
		});
		addMessageInfo("Contratos reenviados");
		return "/contrato/envio/list.xhtml?faces-redirect=true";
	}
	
	public void resetTipoContratoComplemento() {
		if(contratoOriginal != null) {
			if(getEntity().getTipoContrato() == TipoContrato.CESSAO_DIREITO_CREDOR) {
				getEntity().setNomeCessaoDireito(contratoOriginal.getFinanceira().getNome());
				getEntity().setCpfCnpjCessaoDireito(contratoOriginal.getFinanceira().getDocumento());
				getEntity().setFinanceira(null);
				redefinirDevedor();
			} else if (getEntity().getTipoContrato() == TipoContrato.CESSAO_DIREITO_DEVEDOR) {
				getEntity().setNomeCessaoDireito(contratoOriginal.getNomeDevedorFinanciado());
				getEntity().setCpfCnpjCessaoDireito(contratoOriginal.getCpfCnpjDevedorFinanciado());
				getEntity().setFinanceira(contratoOriginal.getFinanceira());
				getEntity().setCpfCnpjDevedorFinanciado(null);
				getEntity().setNomeDevedorFinanciado(null);
				getEntity().setCepDevedor(null);
				getEntity().setEnderecoDevedor(null);
				getEntity().setBairroDevedor(null);
				getEntity().setUfEnderecoDevedor(null);
				getEntity().setMunicipioDevedor(null);
				getEntity().setNumeroEnderecoDevedor(null);
				getEntity().setComplementoEnderecoDevedor(null);
				getEntity().setDddDevedor(null);
				getEntity().setTelefoneDevedor(null);
				getEntity().setEmailDevedor(null);
			} else if (getEntity().getTipoContrato() == TipoContrato.SUBSTITUICAO_GARANTIA) {
				getEntity().setFinanceira(contratoOriginal.getFinanceira());
				getEntity().setCpfCnpjCessaoDireito(null);
				getEntity().setNomeCessaoDireito(null);
				redefinirDevedor();
			}
		}
		loadDadosVeiculoCessaoDireito();
	}
	
	private void loadDadosVeiculoCessaoDireito() {
		if (contratoOriginal != null && entity.isCessaoDireito()) {
			entity.setVeiculos(new ArrayList<Veiculo>());
			for (Veiculo v : contratoOriginal.getVeiculos()) {
				Veiculo vClone = new Veiculo();
				BeanUtils.copyProperties(v, vClone);
				vClone.setContrato(entity);
				vClone.setId(null);
				vClone.setMarca(vClone.getModelo() != null ? vClone.getModelo().getMarca() : vClone.getMarca());
				vClone.setMensagemRetorno(null);
				vClone.setDataTransmissaoDETRAN(null);
				entity.getVeiculos().add(vClone);
			}
		}
	}

	private void redefinirDevedor() {
		getEntity().setCpfCnpjDevedorFinanciado(contratoOriginal.getCpfCnpjDevedorFinanciado());
		getEntity().setNomeDevedorFinanciado(contratoOriginal.getNomeDevedorFinanciado());
		getEntity().setCepDevedor(contratoOriginal.getCepDevedor());
		getEntity().setEnderecoDevedor(contratoOriginal.getEnderecoDevedor());
		getEntity().setBairroDevedor(contratoOriginal.getBairroDevedor());
		getEntity().setUfEnderecoDevedor(contratoOriginal.getUfEnderecoDevedor());
		getEntity().setMunicipioDevedor(contratoOriginal.getMunicipioDevedor());
		getEntity().setNumeroEnderecoDevedor(contratoOriginal.getNumeroEnderecoDevedor());
		getEntity().setComplementoEnderecoDevedor(contratoOriginal.getComplementoEnderecoDevedor());
		getEntity().setDddDevedor(contratoOriginal.getDddDevedor());
		getEntity().setTelefoneDevedor(contratoOriginal.getTelefoneDevedor());
		getEntity().setEmailDevedor(contratoOriginal.getEmailDevedor());
	}

	public String deleteSelected(){
		int excluidos = 0;
		if (PlaceconUtil.isListaVaziaOuNula(contratosSelecionados)) {
			addMessageError("Selecione pelo menos um contrato para exclusão");
		} else {
			for (Contrato c : contratosSelecionados){
				try {
					getService().delete(c.getId());
					excluidos++;
				} catch (ServiceException e) {
					logger.error(e);
				}
			}
			addMessageInfo(String.format("%d Contratos Excluidos de %d selecionados.", excluidos, contratosSelecionados.size()));
		}
		return "/contrato/envio/list.xhtml?faces-redirect=true";
	}

	public List<Contrato> getContratosSelecionados() {
		return contratosSelecionados;
	}

	public void setContratosSelecionados(List<Contrato> contratosSelecionados) {
		this.contratosSelecionados = contratosSelecionados;
	}
}