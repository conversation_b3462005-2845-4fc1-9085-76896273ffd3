package com.registrocontrato.registro.view.bean;

import com.google.common.base.Strings;
import com.registrocontrato.commons.ws.rsng.ContratoRsngService;
import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.Financeira;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.util.PlaceconUtil;
import com.registrocontrato.registro.entity.*;
import com.registrocontrato.registro.enums.TipoContrato;
import com.registrocontrato.registro.enums.TipoPesquisaGravame;
import com.registrocontrato.registro.enums.TipoRestricao;
import com.registrocontrato.registro.repository.AnexoRepository;
import com.registrocontrato.registro.repository.DocumentoArrecadacaoRepository;
import com.registrocontrato.registro.service.ContratoService;
import com.registrocontrato.registro.service.MigracaoService;
import com.registrocontrato.registro.service.MunicipioService;
import com.registrocontrato.registro.service.detran.HandlerWsDetranClient;
import com.registrocontrato.registro.service.detran.sp.client.DetalheGravame;
import com.registrocontrato.registro.service.dto.ContratoDTO;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;
import com.registrocontrato.registro.service.dto.RetornoGravameDTO;
import com.registrocontrato.registro.service.validation.ContratoValidation;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.FinanceiraService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

public abstract class ContratoBaseBean<E, F> extends BaseCrud<Contrato, ContratoDTO> {

    private static final long serialVersionUID = 1L;

    @Value("${file.dir-read}")
    private String FILE_DIR_READ;

    private Boolean showMessageDocumento;

    @Autowired
    protected ContratoService service;

    @Autowired
    protected ContratoRsngService contratoRsngService;

    @Autowired
    protected AnexoRepository anexoRepository;

    @Autowired
    private ContratoValidation contratoValidation;

    @Autowired
    protected UsuarioService usuarioService;

    @Autowired
    private List<HandlerWsDetranClient> listaWsDetranClient;

    @Autowired
    private MunicipioService municipioService;

    @Autowired
    private FinanceiraService financeiraService;

    @Autowired
    private DocumentoArrecadacaoRepository documentoArrecadacaoRepository;

    @Autowired
    private MigracaoService migracaoService;

    private List<String> referenciasImagens;

    private String localidadeDevedor;

    private String localidadeGarantidor;

    private final List<Uf> ufsNumeroEndereco = Arrays.asList(Uf.RS);

    private List<Uf> estadosSemNumeroConfirmacao = Arrays.asList(Uf.SP, Uf.AP, Uf.RR, Uf.RN, Uf.MT, Uf.MG, Uf.PE, Uf.PB, Uf.AC);

    @Override
    public void loadDetails() {
        super.loadDetails();
        Usuario usuarioLogado = usuarioService.findByCpfFinanceiras(getUsername());
        if (usuarioLogado.isPerfilFinanceira()) {
            if (!usuarioLogado.getFinanceiras().contains(getEntity().getFinanceira())) {
                unauthorized();
            }
        }
        if (usuarioLogado.getUf() != null && !usuarioLogado.getUf().equals(getEntity().getUfRegistro())) {
            unauthorized();
        }

        for (Veiculo v : getEntity().getVeiculos()) {
            Boolean veiculoUsado = StringUtils.isNotBlank(v.getPlaca()) || StringUtils.isNotBlank(v.getNumeroRenavam()) || v.getUf() != null;
            v.setVeiculo0Km(!veiculoUsado);
        }
    }

    private Optional<HandlerWsDetranClient> consultarWsDetran(Uf estado) {
        return listaWsDetranClient
                .stream()
                .filter(wsDetranClient -> wsDetranClient.getUf().equals(estado))
                .findFirst();
    }

    public void consultarGravame(Veiculo veic) {
        try {
            if (entity.getUfRegistro() != null && veic.getNumeroChassi() != null && entity.getUfRegistro() != Uf.MS) {
                Optional<HandlerWsDetranClient> wsDetran = consultarWsDetran(entity.getUfRegistro());

                if (wsDetran.isPresent()) {

                    RetornoGravameDTO retorno = null;
                    if (migracaoService.isEnviarParaWinov(entity.getUfRegistro())) {
                        retorno = migracaoService.consultarGravameNaEquinix(
                                TipoPesquisaGravame.CHASSI,
                                entity.getFinanceira(),
                                veic.getNumeroChassi(),
                                veic.getNumeroGravame(),
                                entity.getUfRegistro()
                        );
                    } else {
                        retorno = wsDetran.get()
                                .consultarGravame(
                                        TipoPesquisaGravame.CHASSI,
                                        entity.getFinanceira(),
                                        veic.getNumeroChassi(),
                                        veic.getNumeroGravame());
                    }

//                    RetornoGravameDTO retorno = wsDetran.get()
//                            .consultarGravame(
//                                    TipoPesquisaGravame.CHASSI,
//                                    entity.getFinanceira(),
//                                    veic.getNumeroChassi(),
//                                    veic.getNumeroGravame());
                    if (retorno != null && retorno.getMensagem() != null && retorno.getDetalheGravame() != null && retorno.getMensagem().getCodigo().equals("0")) {
                        DetalheGravame detalhe = retorno.getDetalheGravame();
                        veic.setAnoFabricacao(Objects.nonNull(detalhe.getAnoFabricacao()) ? Integer.parseInt(detalhe.getAnoFabricacao()) : null);
                        veic.setAnoModelo(Objects.nonNull(detalhe.getAnoModelo()) ? Integer.parseInt(detalhe.getAnoModelo()) : null);
                        veic.setPlaca(Objects.nonNull(detalhe.getPlaca()) ? detalhe.getPlaca() : null);
                        veic.setChassiRemarcado(Objects.nonNull(detalhe.getIdenRemarcacao()) ? (detalhe.getIdenRemarcacao() == 1 ? true : false) : false);
                        veic.setUf(Objects.nonNull(detalhe.getUFPlaca()) ? (Uf.getEnum(detalhe.getUFPlaca())) : null);
                        veic.setNumeroGravame(Objects.nonNull(retorno.getDetalheGravame().getNumeroGravame()) ? retorno.getDetalheGravame().getNumeroGravame() : veic.getNumeroGravame());
                        veic.setNumeroRenavam(Objects.nonNull(detalhe.getRenavam()) ? detalhe.getRenavam() : null);
                        if (detalhe.getPlaca() != null || veic.getUf() != null || veic.getNumeroRenavam() != null) {
                            veic.setVeiculo0Km(Boolean.FALSE);
                        } else {
                            veic.setVeiculo0Km(Boolean.TRUE);
                        }
                        entity.setNomeDevedorFinanciado(detalhe.getNomeFinanciado());
                        entity.setNumeroContrato(detalhe.getNumeroContrato());
                        entity.setCpfCnpjDevedorFinanciado(detalhe.getNumeroDocumentoFinanciado());
                        buscarDadosCpfCnpjDevedor();
                    } else {
//                        buscarDadosChassi(veic);
                    }
                } else {
//                    buscarDadosChassi(veic);
                }
            }
        } catch (Exception e) {
            addMessageError(e.getMessage());
        }
    }

    private void buscarDadosChassi(Veiculo v) {
        Veiculo veiculo = service.getVeiculoByChassi(v.getNumeroChassi());
        if (Objects.nonNull(veiculo)) {
            v.setChassiRemarcado(Objects.nonNull(veiculo.getChassiRemarcado()) ? veiculo.getChassiRemarcado() : null);
            v.setVeiculo0Km(Objects.nonNull(veiculo.getVeiculo0Km()) ? veiculo.getVeiculo0Km() : null);
            v.setPlaca(Objects.nonNull(veiculo.getPlaca()) ? veiculo.getPlaca() : null);
            v.setUf(Objects.nonNull(veiculo.getUf()) ? veiculo.getUf() : null);
            v.setNumeroRenavam(Objects.nonNull(veiculo.getNumeroRenavam()) ? veiculo.getNumeroRenavam() : null);
            v.setTipo(Objects.nonNull(veiculo.getTipo()) ? veiculo.getTipo() : null);
            v.setAnoFabricacao(Objects.nonNull(veiculo.getAnoFabricacao()) ? veiculo.getAnoFabricacao() : null);
            v.setAnoModelo(Objects.nonNull(veiculo.getAnoModelo()) ? veiculo.getAnoModelo() : null);
            v.setMarca(Objects.nonNull(veiculo.getMarca()) ? veiculo.getMarca() : null);
            v.setModelo(Objects.nonNull(veiculo.getModelo()) ? veiculo.getModelo() : null);
        }
    }


    public void buscarDadosCpfCnpjDevedor() {
        if (entity.getCpfCnpjDevedorFinanciado() != null) {
            List<Contrato> lista = service.findDadosDevedorFinanciado(entity.getCpfCnpjDevedorFinanciado(), entity.getId());
            if (lista != null && !lista.isEmpty()) {
                Contrato c = lista.get(0);
                entity.setNomeDevedorFinanciado(c.getNomeDevedorFinanciado());
                entity.setCepDevedor(c.getCepDevedor());
                entity.setEnderecoDevedor(c.getEnderecoDevedor());
                entity.setNumeroEnderecoDevedor(c.getNumeroEnderecoDevedor());
                entity.setComplementoEnderecoDevedor(c.getComplementoEnderecoDevedor());
                entity.setBairroDevedor(c.getBairroDevedor());
                entity.setMunicipioDevedor(c.getMunicipioDevedor());
                entity.setUfEnderecoDevedor(c.getUfEnderecoDevedor());
                entity.setTelefoneDevedor(c.getTelefoneDevedor());
                entity.setEmailDevedor(c.getEmailDevedor());
                entity.setNumeroEnderecoDevedor(c.getNumeroEnderecoDevedor());
                entity.setDddDevedor(c.getDddDevedor());
            }
        }
    }

    public void validateChassiGravameContratoAtivo(Veiculo veic) {
        if (veic.getContrato().getTipoContrato() == TipoContrato.CONTRATO_PRINCIPAL &&
                veic.getNumeroChassi() != null && veic.getNumeroGravame() != null) {
            try {
                contratoValidation.validarChassiEmContratoAtivo(entity);
            } catch (ServiceException s) {
                addMessageError(s.getMessage());
            }
        }
    }

    public void resetTipoVrg() {
        if (getEntity().getTipoRestricao() != TipoRestricao.ARRENDAMENTO) {
            getEntity().setTipoVrg(null);
            getEntity().setValorVrg(null);
            getEntity().setClausulaPenalVrg(null);
        }
    }

    public void reset0Km(int index) {
        Veiculo veiculo = getEntity().getVeiculos().get(index);
        if (veiculo.getVeiculo0Km()) {
            veiculo.setPlaca(null);
            veiculo.setUf(null);
            veiculo.setNumeroRenavam(null);
        }
    }

    public void addVeiculo() {
        Veiculo veiculo = new Veiculo();
        veiculo.setVeiculo0Km(Boolean.TRUE);
        veiculo.setContrato(getEntity());
        getEntity().getVeiculos().add(veiculo);
    }

    public void removeVeiculo(int index) throws ServiceException {
        List<Veiculo> veiculos = getEntity().getVeiculos();

        if (veiculos.size() > 1) {
            Veiculo v = getEntity().getVeiculos().get(index);
            // Veículo registrado não pode ser excluído
            if (veiculoFoiRegistrado(v, getEntity())) {
                String mensagem = String.format("O Veículo de chassi %s foi registrado" +
                                " com o código de confirmação %s",
                        v.getNumeroChassi(), v.getNumeroRegistroDetran());
                addMessageError(mensagem);
                return;
            }

            // Veículo não registrado com Doc Arrecadação
            if (Objects.nonNull(v.getId())) {
                Optional<DocumentoArrecadacao> docArrecadacao = documentoArrecadacaoRepository.findDocumentoArrecadacaoByVeiculo(v);
                if (docArrecadacao.isPresent()) {
                    docArrecadacao.get().setVeiculo(null);
                    documentoArrecadacaoRepository.save(docArrecadacao.get());
                }
            }

            getEntity().getVeiculos().remove(index);
            addMessageInfo(String.format("Removido o veículo de chassi %s", v.getNumeroChassi()));
        }
    }

    private boolean veiculoFoiRegistrado(Veiculo v, Contrato c) {
        if (Objects.nonNull(v.getId()))
            return !Strings.isNullOrEmpty(v.getNumeroRegistroDetran()) || estadosSemNumeroConfirmacao.contains(c.getUfRegistro());
        return false;
    }

    public void removeVeiculoSubstituicao(int index) {
        substituirVeiculo(index);
        if (getEntity().getVeiculos().size() > 1) {
            getEntity().getVeiculos().remove(index);
        }
    }

    public void substituirVeiculo(int index) {
        Veiculo veiculo = getEntity().getVeiculos().get(index);
        if (getEntity().getChassiSubstituicao() == null) {
            getEntity().setChassiSubstituicao(veiculo.getNumeroChassi());
        } else if (veiculo.getNumeroChassi() != null) {
            getEntity().setChassiSubstituicao(getEntity().getChassiSubstituicao() + ";" + veiculo.getNumeroChassi());
        }

        Long id = veiculo.getId();
        veiculo = new Veiculo();
        veiculo.setId(id);
        veiculo.setVeiculo0Km(Boolean.TRUE);
        veiculo.setContrato(getEntity());
        getEntity().getVeiculos().set(index, veiculo);
    }

    public void deleteFile(int index) {
        getEntity().getAnexos().remove(index);
    }

    public void handleFileUpload(FileUploadEvent event) throws IOException {
        UploadedFile file = event.getFile();
        validacaoArquivosDefault(file, Arrays.asList("pdf", "png"));

        String fileName = event.getFile().getFileName().toUpperCase();
        if (fileName.endsWith("JPG") || fileName.endsWith("JPEG")) {
            if (event.getFile().getSize() < 200000) {
                addMessageError(String.format("Tamanho mínimo da imagem deve ser de 200Kb: %s", file.getFileName()));
                showMessageDocumento = true;
                return;
            }
            BufferedImage bi = ImageIO.read(new ByteArrayInputStream(event.getFile().getContent()));
            int width = bi.getWidth();
            int height = bi.getHeight();
            if (width < 1600 || height < 1024) {
                addMessageError(String.format("Resolução mínima da imagem deve ser de 1600 x 1024: %s", file.getFileName()));
                showMessageDocumento = true;
                return;
            }
        }

        showMessageDocumento = false;
        getEntity().getAnexos().add(new Anexo(file.getFileName(), getEntity(), event.getFile().getInputStream()));
    }

    public void download(Long id) throws IOException {
        Anexo entity = anexoRepository.findOne(id);
        String fileName = entity.getNomeArquivo();

        getExternalContext().responseReset();
        getExternalContext().setResponseContentType("application/pdf");
        getExternalContext().setResponseHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

        OutputStream output = getExternalContext().getResponseOutputStream();
        IOUtils.copy(new FileInputStream(FILE_DIR_READ + entity.getReferenciaArquivo()), output);
        getCurrentInstance().responseComplete();
    }

    public void findMunicipioDevedor() {
        if (localidadeDevedor != null && entity.getUfEnderecoDevedor() != null) {
            entity.setMunicipioDevedor(municipioService.findByUfAndDescricao(entity.getUfEnderecoDevedor(), localidadeDevedor));
        }
    }

    public void findMunicipioGarantidor() {
        if (localidadeGarantidor != null && entity.getUfEnderecoGarantidor() != null) {
            entity.setMunicipioGarantidor(municipioService.findByUfAndDescricao(entity.getUfEnderecoGarantidor(), localidadeGarantidor));
        }
    }

    public List<String> getReferenciasImagens() {
        if (referenciasImagens == null && getEntity() != null && getEntity().getAnexos() != null && getEntity().getPossuiImagemAnexo() == Boolean.TRUE) {
            referenciasImagens = new ArrayList<>();
            for (Anexo a : getEntity().getAnexos()) {
                if (!a.isPdf()) {
                    referenciasImagens.add(a.getReferenciaArquivo());
                }
            }
        }
        return referenciasImagens;
    }

    public Boolean getShowMessageDocumento() {
        return showMessageDocumento;
    }

    public void setShowMessageDocumento(Boolean showMessageDocumento) {
        this.showMessageDocumento = showMessageDocumento;
    }

    @Override
    public BaseService<Contrato, ContratoDTO> getService() {
        return service;
    }

    public String getLocalidadeDevedor() {
        return localidadeDevedor;
    }

    public void setLocalidadeDevedor(String localidade) {
        if (!StringUtils.isBlank(localidade)) {
            this.localidadeDevedor = PlaceconUtil.deAccent(localidade.toUpperCase());
        } else {
            this.localidadeDevedor = null;
        }
    }

    public String getLocalidadeGarantidor() {
        return localidadeGarantidor;
    }

    public void setLocalidadeGarantidor(String localidade) {
        if (!StringUtils.isBlank(localidade)) {
            this.localidadeGarantidor = PlaceconUtil.deAccent(localidade.toUpperCase());
        } else {
            this.localidadeGarantidor = null;
        }
    }

    public void definirLiberacaoCredito() {
        if (getEntity().getFinanceira() != null) {
            Financeira financeira = financeiraService.findOne(getEntity().getFinanceira().getId());
            getEntity().setFinanceira(financeira);
            getEntity().setMunicipioLiberacao(financeira.getMunicipio());
            getEntity().setUfLiberacaoCredito(financeira.getUfEndereco());
        }
    }

    public Boolean verificarUfSE() {
        return Objects.nonNull(getEntity().getUfRegistro()) && getEntity().getUfRegistro() == Uf.SE;
    }

    public Boolean isNumeroEnderecoDevedorObrigatorio() {
        return Objects.nonNull(getEntity().getUfRegistro()) && ufsNumeroEndereco.contains(getEntity().getUfRegistro());
    }
}
