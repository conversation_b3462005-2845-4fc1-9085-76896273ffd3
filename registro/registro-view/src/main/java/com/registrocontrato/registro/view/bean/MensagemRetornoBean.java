package com.registrocontrato.registro.view.bean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.infra.service.audit.AuditContrato;
import com.registrocontrato.registro.entity.MensagemRetorno;
import com.registrocontrato.registro.service.MensagemRetornoService;
import com.registrocontrato.registro.service.dto.MensagemRetornoDTO;


@Controller
@ViewScope
public class MensagemRetornoBean extends BaseCrud<MensagemRetorno, MensagemRetornoDTO>{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Autowired
	private MensagemRetornoService service;

	@Override
	public BaseService<MensagemRetorno, MensagemRetornoDTO> getService() {
		return service;
	}

	
	@AuditContrato(action = "Registro de Mensagem")
	public String save() {
		try {
			service.save(entity);
			addMessageSuccess();
			return "/mensagemretorno/list.xhtml";
		} catch (ServiceException e) {
			addMessageError(e.getMessage());
		} catch (Exception e) {
			addMessageError(e.getMessage());
		}

		return null;
	}
	
	

}
