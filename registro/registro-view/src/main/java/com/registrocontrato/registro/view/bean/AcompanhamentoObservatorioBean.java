package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.registro.dto.ProcessoCredenciamentoDTO;
import com.registrocontrato.registro.entity.ProcessoCredenciamento;
import com.registrocontrato.registro.service.AcompanhamentoObservatorioService;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.faces.application.FacesMessage;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Controller
@ViewScope
public class AcompanhamentoObservatorioBean extends BaseCrud<ProcessoCredenciamento, ProcessoCredenciamentoDTO> {

    private final AcompanhamentoObservatorioService service;

    private final UsuarioService usuarioService;

    private List<UploadedFile> lista = new ArrayList<>();

    public AcompanhamentoObservatorioBean(AcompanhamentoObservatorioService service, UsuarioService usuarioService) {
        this.service = service;
        this.usuarioService = usuarioService;
    }

    @Override
    public void postInitialization() {
        super.postInitialization();
        try {
        } catch (Exception e) {
            logger.warn("Não foi possível definir o usuário: " + e.getMessage());
        }
    }

    public void handleFileUpload(FileUploadEvent event) {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Collections.singletonList("pdf"));
            lista.add(file);
            addMessageInfo("Arquivo processado com sucesso");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            addMessageError(e.getMessage() != null ? e.getMessage() : "Houve um erro no sistema. Favor entrar em contato com suporte");
        }
    }

    public void removerArquivo() {
        if (!lista.isEmpty()) {
            logger.debug("Removendo arquivo com sucesso");
            lista.remove(0);
            addMessageInfo("Arquivo removido com sucesso");
        } else {
            addMessageError("Arquivo não encontrado para remoção");
        }
    }

    public String save() {
        getExternalContext().getFlash().setKeepMessages(true);
        try {
            logger.debug("Salvando entidade: " + entity);

            getService().save(entity);

            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_INFO, "Salvo com sucesso", ""));
            String uri = ((HttpServletRequest) getExternalContext().getRequest()).getServletPath();
            uri = uri.replace("form-add", "acompanhamento-place");
            uri = uri.replace("form-update", "acompanhamento-place");
            return uri + "?faces-redirect=true";
        } catch (ServiceException e) {
            logger.error("ServiceException ao salvar: ", e);
            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_ERROR, e.getMessage(), ""));
        } catch (org.springframework.transaction.UnexpectedRollbackException e) {
            logger.error("Transação foi revertida inesperadamente: ", e);
            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_ERROR, "Erro na transação. Verifique se todos os campos obrigatórios estão preenchidos.", ""));
        } catch (Exception e) {
            logger.error("Erro geral ao salvar: ", e);
            getCurrentInstance().addMessage("", new FacesMessage(FacesMessage.SEVERITY_ERROR, "Erro ao salvar: " + e.getMessage(), ""));
        }
        return null;
    }

    @Override
    public AcompanhamentoObservatorioService getService() {
        return service;
    }

    public List<UploadedFile> getLista() {
        return lista;
    }

    public void setLista(List<UploadedFile> lista) {
        this.lista = lista;
    }

    public ProcessoCredenciamento getEntity() {
        return entity;
    }
}
