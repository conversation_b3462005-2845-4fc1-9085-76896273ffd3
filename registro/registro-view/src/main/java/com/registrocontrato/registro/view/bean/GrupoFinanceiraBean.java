package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.entity.GrupoFinanceira;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.seguranca.repository.FinanceiraRepository;
import com.registrocontrato.seguranca.repository.GrupoFinanceiraRepository;
import com.registrocontrato.seguranca.service.GrupoFinanceiraService;
import com.registrocontrato.seguranca.service.dto.GrupoFinanceiraDTO;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.file.UploadedFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.io.ByteArrayInputStream;
import java.util.Arrays;

@Controller
@ViewScope
public class GrupoFinanceiraBean extends BaseCrud<GrupoFinanceira, GrupoFinanceiraDTO> {

    private static final long serialVersionUID = 1L;

    @Autowired
    private GrupoFinanceiraService service;

    private StreamedContent logoFinanceira;
    @Autowired
    private FinanceiraRepository financeiraRepository;

    @Autowired
    private GrupoFinanceiraRepository grupoFinanceiraRepository;

    public void handleFotoUpload(FileUploadEvent event) {
        try {
            UploadedFile file = event.getFile();
            validacaoArquivosDefault(file, Arrays.asList("jpg", "jpeg", "png"));
            getEntity().setLogoFinanceira(file.getContent());
            logoFinanceira = DefaultStreamedContent.builder()
                    .contentType("image/jpg")
                    .stream(() -> new ByteArrayInputStream(getEntity().getLogoFinanceira()))
                    .build();
        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }
    }

    @Override
    public void loadDetails() {
        entity = grupoFinanceiraRepository.findById(getIdToEdit());
        logoFinanceira = DefaultStreamedContent
                .builder().contentType("image/jpg")
                .stream(() -> {
                    try {
                        ByteArrayInputStream stream = new ByteArrayInputStream(getEntity().getLogoFinanceira());
                        return stream;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return null;
                }).build();
    }

    @Override
    public BaseService<GrupoFinanceira, GrupoFinanceiraDTO> getService() {
        return service;
    }

    public StreamedContent getLogoFinanceira() {
        return logoFinanceira;
    }

}
