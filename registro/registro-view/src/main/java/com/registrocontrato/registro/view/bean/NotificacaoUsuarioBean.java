package com.registrocontrato.registro.view.bean;

import com.registrocontrato.infra.bean.BaseCrud;
import com.registrocontrato.infra.exception.ServiceException;
import com.registrocontrato.infra.jsf.ViewScope;
import com.registrocontrato.infra.service.BaseService;
import com.registrocontrato.registro.entity.NotificacaoSistema;
import com.registrocontrato.registro.entity.NotificacaoSistemaUsuario;
import com.registrocontrato.registro.service.NotificacaoSistemaService;
import com.registrocontrato.registro.service.NotificacaoSistemaUsuarioService;
import com.registrocontrato.registro.service.dto.NotificacaoUsuarioDTO;
import com.registrocontrato.seguranca.entity.Cookies;
import com.registrocontrato.seguranca.entity.Usuario;
import com.registrocontrato.seguranca.service.UsuarioService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.primefaces.PrimeFaces;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@ViewScope
public class NotificacaoUsuarioBean extends BaseCrud<NotificacaoSistemaUsuario, NotificacaoUsuarioDTO> {

    private static final long serialVersionUID = 1L;

    private final Log logger = LogFactory.getLog(getClass());

    private Usuario user;

    private int total;

    private List<NotificacaoSistema> notificacoes;

    @Autowired
    private NotificacaoSistemaUsuarioService service;

    @Autowired
    private NotificacaoSistemaService notificaoSistemaService;

    @Autowired
    private UsuarioService usuarioService;

    private final String TERMOS_COOKIES = "Nós utilizamos cookies para garantir que você obtenha a melhor experiência em nosso site. Ao continuar navegando, você concorda com o nosso uso de cookies. ";
    private final String COOKIES_WIDGET = "cookieBar";

    @Override
    public BaseService<NotificacaoSistemaUsuario, NotificacaoUsuarioDTO> getService() {
        return service;
    }

    @Override
    public void postInitialization() {
        super.postInitialization();
        if (getUsername() != null) {
            user = usuarioService.findByCpf(getUsername());
            if (user != null) {
                getFilter().setUsuario(user);
                notificacoes = new ArrayList<>();
                List<NotificacaoSistema> aux = notificaoSistemaService.findNaoLidas(user, 20);
                List<NotificacaoSistemaUsuario> lidas = service.findLidasUsuario(user);

                aux.forEach(n -> {
                    NotificacaoSistemaUsuario x = service.findByNotificacaoAndUsuario(n, user.getCpf());
                    if (x == null) {
                        notificacoes.add(n);
                    } else {
                        try {
                            validateVisibilidade(n.getId());
                            notificacoes.add(n);
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                            try {
                                service.delete(x.getId());
                            } catch (com.registrocontrato.infra.exception.ServiceException e1) {
                                e1.printStackTrace();
                            }
                        }
                    }

                });
                total = notificacoes.size();
                if (total < 20) {
                    int totalAux = total;
                    for (NotificacaoSistemaUsuario n : lidas) {
                        try {
                            validateVisibilidade(n.getNotificacao().getId());
                            notificacoes.add(n.getNotificacao());
                            totalAux++;
                            if (totalAux == 20) {
                                break;
                            }
                        } catch (Exception e) {
                            logger.error(e.getMessage());
                            try {
                                service.delete(n.getId());
                            } catch (com.registrocontrato.infra.exception.ServiceException e1) {
                                e1.printStackTrace();
                            }
                        }
                    }

                }
                if (user.getUf() != null) {
                    getFilter().setUf(user.getUf());
                }
            }
        }
    }

    public List<NotificacaoSistema> getNotificacoes() {
        return notificacoes;
    }


    public int getTotal() {
        return total;
    }

    public Usuario getUser() {
        return user;
    }

    @Override
    public void loadDetails() {
        try {
            NotificacaoSistema notificacao = validarNotificacao();
            setEntity(service.findByNotificacaoAndUsuario(notificacao, user.getCpf()));
            if (getEntity() == null) {
                gravarNotificacaoUsuario(notificacao);
                removeLida();
            }

        } catch (ServiceException e) {
            addMessageError(e.getMessage());
        }

    }

    private void removeLida() {
        int indice = -1;
        for (NotificacaoSistema n : notificacoes) {
            if (n.getId() == getEntity().getNotificacao().getId()) {
                indice = notificacoes.indexOf(n);
                break;
            }
        }
        if (indice != -1) {
            total--;
        }
    }

    private void gravarNotificacaoUsuario(NotificacaoSistema notificacao) throws ServiceException {

        setEntity(new NotificacaoSistemaUsuario());
        getEntity().setCpf(user.getCpf());
        getEntity().setNotificacao(notificacao);
        try {
            service.save(getEntity());
        } catch (Exception e) {
            throw new ServiceException("Não foi possível gerar a notificação do usuário");
        }
    }

    private NotificacaoSistema validarNotificacao() throws ServiceException {
        validateVisibilidade(getIdToEdit());
        NotificacaoSistema notificacao = validateExistente();
        return notificacao;
    }

    private NotificacaoSistema validateExistente() throws ServiceException {
        NotificacaoSistema notificacao = notificaoSistemaService.findOne(getIdToEdit());
        if (notificacao == null) {
            throw new ServiceException("Notificação não localizada");
        }
        return notificacao;
    }

    private void validateVisibilidade(Long id) throws ServiceException {
        if (!notificaoSistemaService.isVisivel(user, id)) {
            throw new ServiceException("Usuário não tem acesso a esta notificação");
        }
    }

    public boolean usuarioAceitouCookies() {
        return user.getCookies() == null || user.getCookies().getDataAceite() == null;
    }

    public void aceitarTermos() {
        try {
            Cookies cookies = new Cookies();
            cookies.setDataAceite(new Date());
            cookies.setTermosAceitos(getTermos());
            usuarioService.saveCookies(user, cookies);
            PrimeFaces.current().executeScript(String.format("PF('%s').hide()", getCookiesWidget()));
        } catch (ServiceException se) {
            logger.error("Não foi possível salvar cookies do usuário: " + se.getMessage());
        }
    }

    public String getTermos() {
        return TERMOS_COOKIES;
    }

    public String getCookiesWidget() {
        return COOKIES_WIDGET;
    }

}
