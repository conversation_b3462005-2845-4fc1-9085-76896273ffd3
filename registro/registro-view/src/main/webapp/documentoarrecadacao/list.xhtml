<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Documento de Arrecadação</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
                                        <div class="row">
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Código</label>
                                                    <input jsf:id="codigo" type="text"
                                                           jsf:value="#{documentoArrecadacaoBean.filter.codigo}"
                                                           class="form-control"/>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default form-group-default-select2">
                                                    <label>Estado</label>
                                                    <select jsf:id="ufRegistro"
                                                            jsf:value="#{documentoArrecadacaoBean.filter.estado}"
                                                            class="select2 form-control full-width" jsf:label="Estado">
                                                        <f:selectItems value="#{helperBean.ufs}" var="i"/>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-4">
                                                <div class="form-group form-group-default">
                                                    <label>Chassi</label>
                                                    <input jsf:id="chassi" type="text"
                                                           jsf:value="#{documentoArrecadacaoBean.filter.chassi}"
                                                           class="form-control"/>
                                                </div>
                                            </div>
                                        </div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/documentoarrecadacao/list.xhtml" class="btn btn-default">Limpar</a>
												<sec:authorize ifAnyGranted="CADASTRAR_DOCUMENTO_ARRECADACAO">
													<a href="#{request.contextPath}/documentoarrecadacao/form-add.xhtml" class="btn btn-primary btn-cons">Novo</a>
												</sec:authorize>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{documentoArrecadacaoBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página <select jsf:id="registros" jsf:value="#{documentoArrecadacaoBean.size}" size="1" >
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{documentoArrecadacaoBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{documentoArrecadacaoBean.list}" paginator="true" rows="#{documentoArrecadacaoBean.size}" paginatorPosition="bottom"
										emptyMessage="Nenhum registro encontrado" currentPageReportTemplate="({currentPage} de {totalPages})"
										paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="UF">
											#{object.estado}
										</p:column>
										<p:column headerText="Financeira">
											#{object.financeira.nome}
										</p:column>
										<p:column headerText="Código">
											<a class="btn btn-link"
											   href="#{request.contextPath}/documentoarrecadacao/form-detail.xhtml?id=#{object.id}">#{object.codigo}</a>
										</p:column>
										<p:column headerText="Chassi">
											#{object.veiculo.numeroChassi}
										</p:column>
										<p:column headerText="Criação">
											<h:outputText value="#{object.dataCriacao}">
												<f:convertDateTime />
											</h:outputText>
										</p:column>
										<p:column headerText="Pagamento">
											<h:outputText value="#{object.dataPagamento}">
												<f:convertDateTime />
											</h:outputText>
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<sec:authorize ifAnyGranted="CONSULTAR_DOCUMENTO_ARRECADACAO">
												<a class="btn btn-link" title="Visualizar" href="#{request.contextPath}/documentoarrecadacao/form-detail.xhtml?id=#{object.id}"> <span class="fa fa-search" />
												</a>
											</sec:authorize>
											<sec:authorize ifAnyGranted="CADASTRAR_DOCUMENTO_ARRECADACAO">
												<a class="btn btn-link" title="Editar" href="#{request.contextPath}/documentoarrecadacao/form-update.xhtml?id=#{object.id}"> <span class="fa fa-edit" />
												</a>
											</sec:authorize>
											<sec:authorize ifAnyGranted="EXCLUIR_DOCUMENTO_ARRECADACAO">
												<h:commandLink styleClass="btn btn-link" title="Excluir" onclick="return confirm('Confirmar a exclusão?')"
													action="#{documentoArrecadacaoBean.delete(object.id)}">
													<span class="fa fa-remove" />
												</h:commandLink>
											</sec:authorize>
											<sec:authorize ifAnyGranted="CONSULTAR_DOCUMENTO_ARRECADACAO">
												<h:commandLink rendered="#{object.estado == 'BA'}"  styleClass="btn btn-link" title="Renovar Taxa" onclick="return confirm('Confirmar a renovação da taxa?')"
												action="#{documentoArrecadacaoBean.renovarTaxa(object)}">
													<span class="fa fa-refresh" />
												</h:commandLink>
											</sec:authorize>
											<sec:authorize ifAnyGranted="CONSULTAR_DOCUMENTO_ARRECADACAO">
												<h:commandLink rendered="#{object.estado == 'CE'}" actionListener="#{documentoArrecadacaoBean.fileDownload(object)}"  styleClass="btn btn-link" title="Download Boleto" ajax="false" onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
															   action="#{documentoArrecadacaoBean.fileDownload(object)}">
													<span class="mdi mdi-barcode text-primary" />
													<p:fileDownload value="#{documentoArrecadacaoBean.file}" />
												</h:commandLink>
											</sec:authorize>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>
