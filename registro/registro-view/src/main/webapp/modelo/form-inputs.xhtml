<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:sec="http://www.springframework.org/security/tags"
	xmlns:jsf="http://xmlns.jcp.org/jsf">
	
	<div class="row">
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default">
				<label>Código do DENATRAN</label>
				<input jsf:id="codigoDenatran" type="text" maxlength="5" jsf:value="#{modeloBean.entity.codigoDenatran}"
					jsf:label="Código do DENATRAN" class="form-control integer" disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group form-group-default required">
				<label>Ano</label>
				<input jsf:id="ano" type="text" maxlength="5" jsf:value="#{modeloBean.entity.ano}"
					required="true" jsf:required="true" jsf:label="Código do DENATRAN" class="form-control ano"
					disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-6">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>Marca</label>
				<select jsf:id="item" jsf:value="#{modeloBean.entity.marca}" jsf:label="Marca"
					class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:converter converterId="entityConverter"/>
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.marcas}" var="e" itemValue="#{e}" itemLabel="#{e.descricao}" />
                 </select>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-9">
			<div class="form-group form-group form-group-default required">
				<label>Descrição</label>
				<input jsf:id="descricao" type="text" maxlength="255" jsf:value="#{modeloBean.entity.descricao}"
					required="true" jsf:required="true" jsf:label="Descrição" class="form-control"
					disabled="#{disabled}" />
			</div>
		</div>
		<div class="col-lg-3">
			<div class="form-group form-group-default form-group-default-select2 required">
				<label>Ativo</label>
				<select jsf:id="ativo" jsf:value="#{modeloBean.entity.ativo}"
					class="form-control full-width select2" required="true" jsf:required="true" size="1"
					disabled="#{disabled}" jsf:label="Ativo">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
				</select>
			</div>
		</div>
	</div>
</ui:composition>
