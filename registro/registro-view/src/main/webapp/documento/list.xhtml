<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
	
		<f:metadata>
			<f:viewParam name="anexo" value="#{documentoBean.filter.possuiAnexo}" />
			<f:viewAction action="#{documentoBean.limpar()}" />
		</f:metadata>
	
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Documentos</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Nro do Registro Eletrônico</label>
													<input jsf:id="numeroRegistroEletronico" type="text"
														   jsf:value="#{documentoBean.filter.numeroRegistroEletronico}"
														   class="form-control first integer"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Número do Contrato</label>
													<input jsf:id="numeroContrato" type="text" 
														jsf:value="#{documentoBean.filter.numeroContrato}" class="form-control"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>CPF/CNPJ Nome do Devedor</label>
													<input jsf:id="cpfCnpjDevedorFinanciado" type="text"
														   jsf:value="#{documentoBean.filter.cpfCnpjDevedorFinanciado}"
														   jsf:validator="cpfCnpjValidator"  class="form-control doc"/>
												</div>
											</div>
											
										</div>
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Gravame</label>
													<input jsf:id="numeroGravame" type="text" maxlength="8"
														   jsf:value="#{documentoBean.filter.numeroGravame}"
														   class="form-control naoCola"
														   onkeypress="return /^-?[0-9]*$/.test(this.value+event.key)"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Chassi</label>
													<input jsf:id="chassi" type="text" class="form-control chassi"
														   jsf:value="#{documentoBean.filter.chassi}"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Placa</label>
													<input jsf:id="placa" type="text" 
														jsf:value="#{documentoBean.filter.placa}" class="form-control"/>
												</div>
											</div>
											
										</div>
										<div class="row">
											<div class="col-lg-4" >
												<div class="form-group form-group form-group-default">
													<label>Agente Financeiro</label>
													<select jsf:id="financeira" jsf:value="#{documentoBean.filter.financeira}" 
														class="form-control full-width select2" jsf:label="Agente Financeiro">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
														<f:converter converterId="financeiraConverter"/>
													</select>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>UF</label>
													<select jsf:id="uf" jsf:value="#{documentoBean.filter.ufRegistro}"
														class="form-control full-width select2" size="1" disabled="#{disabled}">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.ufs}" var="i" itemLabel="#{i}" />
													</select>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Possui Anexo</label>
													<select jsf:id="possuiAnexo" jsf:value="#{documentoBean.filter.possuiAnexo}"
														class="form-control full-width select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.simNao}" var="i" itemLabel="#{i.descricao}" />
													</select>
												</div>	
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<button type="submit" class="btn btn-default" jsf:immediate="true" jsf:action="#{documentoBean.limpar}">Limpar</button>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{documentoBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página
											<select jsf:id="registros" jsf:value="#{documentoBean.size}" size="1" >
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{documentoBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<div style="min-height: 40px;">
										<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{documentoBean.anexarGrupo}">Anexar em Grupo</button>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{documentoBean.list}" paginator="true" rows="#{documentoBean.size}"
										selection="#{documentoBean.contratosSelecionados}" rowKey="#{object.id}"
										paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="({currentPage} de {totalPages})"
										paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
										lazy="true" draggableColumns="true" resizableColumns="true" selectionPageOnly="false"
										tableStyleClass="table table-hover m-0">
										<p:column selectionMode="multiple" style="width:16px; text-align:center"/>
										<p:column headerText="Registro">
											<span class="fa fa-certificate" jsf:rendered="#{object.assinado == 'S'}" title="Assinado Digitalmente"/>
											<span class="fa fa-eye" jsf:rendered="#{object.aprovadoAuditoria == 'S'}" title="Aprovado na Auditoria"/>
											<span class="fa fa-eye-slash" jsf:rendered="#{object.aprovadoAuditoria == 'N'}" title="Reprovado na Auditoria"/>
											#{object.numeroRegistroEletronico}
										</p:column>
										<p:column headerText="Contrato">
											#{object.numeroContrato}
										</p:column>
										<p:column headerText="Devedor">
											#{object.cpfCnpjDevedorFinanciado}
										</p:column>
										<p:column headerText="Gravame">
											<ui:repeat var="v" value="#{object.veiculosLimitados}">
												#{v.numeroGravame} <br />
											</ui:repeat>
										</p:column>
										<p:column headerText="Chassi">
											<ui:repeat var="v" value="#{object.veiculosLimitados}">
												#{v.numeroChassi} <br />
											</ui:repeat>
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<sec:authorize ifAnyGranted="CADASTRAR_DOCUMENTO,EXCLUIR_DOCUMENTO">
												<a class="btn btn-link" title="Anexar"
													href="#{request.contextPath}/documento/form-update.xhtml?id=#{object.id}">
													<span class="fa fa-file-pdf-o" />
												</a>
											</sec:authorize>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>
