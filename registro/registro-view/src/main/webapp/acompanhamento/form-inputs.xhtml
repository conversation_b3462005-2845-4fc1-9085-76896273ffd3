<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:sec="http://www.springframework.org/security/tags"
	xmlns:jsf="http://xmlns.jcp.org/jsf">
	<div class="row">
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>UF</label>
				<select jsf:id="ufs" jsf:value="#{acompanhamentoObservatorioBean.entity.uf}" jsf:label="UF" jsf:required="true" required="required"
					class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.ufs}" var="e" itemValue="#{e}" itemLabel="#{e}" />
                 </select>
				<input type="hidden" name="#{_csrf.parameterName}"
					   value="#{_csrf.token}"/>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>Financeira</label>
				<select jsf:id="Financeira" jsf:value="#{acompanhamentoObservatorioBean.entity.financeira}" jsf:label="Financeira" jsf:required="true" required="required"
						class="form-control full-width select2" size="1" disabled="#{disabled}" jsf:converter="financeiraConverter">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperSessionBean.financeiras}" var="e" itemValue="#{e}" itemLabel="#{e.nome} - #{e.documento}" />
				</select>
				<input type="hidden" name="#{_csrf.parameterName}"
					   value="#{_csrf.token}"/>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="form-group form-group form-group-default form-group-default-select2 required">
				<label>Status</label>
				<select jsf:id="Status" jsf:value="#{acompanhamentoObservatorioBean.entity.situacaoProcessoCredenciamento}" jsf:label="Status" jsf:required="true" required="required"
						class="form-control full-width select2" size="1" disabled="#{disabled}">
					<f:selectItem itemLabel="Selecione" />
					<f:selectItems value="#{helperBean.situacoesProcessoCredenciamento}" var="e" itemValue="#{e}" itemLabel="#{e}" />
				</select>
				<input type="hidden" name="#{_csrf.parameterName}"
					   value="#{_csrf.token}"/>
			</div>
		</div>
		<div class="col-12">
			<div class="col-lg-12">
				<div class="m-b-20">
					<h6 class="font-14 mt-4">Upload do Relatório (PDF)</h6>
					<p:fileUpload listener="#{acompanhamentoObservatorioBean.handleFileUpload}"
								  class="required"
								  mode="advanced" dragDropSupport="true"
								  update="uploadedFiles messages"
								  sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/"
								  auto="true"
								  cancelLabel="Cancelar" uploadLabel="Upload"
								  label="Selecionar Arquivo"
								  fileLimit="1" multiple="false"
								  fileLimitMessage="Selecione no máximo 1 arquivo."
								  invalidFileMessage="Envie um arquivo PDF"
								  invalidSizeMessage="Tamanho máximo 10M"/>
					<input type="hidden" name="#{_csrf.parameterName}"
						   value="#{_csrf.token}"/>
				</div>
				<div class="col-lg-12 p-0 mb-3" jsf:id="uploadedFiles">
					<div class="table-responsive"
						 jsf:rendered="#{not empty acompanhamentoObservatorioBean.lista}">
						<p:dataTable value="#{acompanhamentoObservatorioBean.lista}" var="file"
									 emptyMessage="Nenhuma Linha Processada"
									 tableStyleClass="table table-hover m-0">
							<p:column headerText="Relatório B3">
								#{file.fileName}
							</p:column>
							<p:column headerText="Ações"
									  style="width:60px; text-align:center;">
								<p:button icon="pi pi-times"
										  action="#{acompanhamentoObservatorioBean.removerArquivo}"
										  update=""
										  style="padding:2px; margin:0; font-size:0.8em;"/>
							</p:column>
						</p:dataTable>
					</div>
				</div>
			</div>
		</div>
	</div>
</ui:composition>
