<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="content">
        <div class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Acompnhamento de Processo de Credenciamento</h4>
                    </div>
                    <div class="col-lg-12">
                        <div class="m-b-20">
                            <h6 class="font-14 mt-4">Pesquisar</h6>
                            <form jsf:id="form" jsf:prependId="false">
                                <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                            infoClass="alert alert-success alert-dismissable"
                                            errorClass="alert alert-danger alert-dismissable" />
                                <div class="row">
                                    <div class="col-lg-4">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <label>UF</label>
                                            <select jsf:id="uf" jsf:value="#{acompanhamentoObservatorioBean.filter.uf}"
                                                    class="form-control full-width select2" size="1" disabled="#{disabled}">
                                                <f:selectItem itemLabel="Selecione" />
                                                <f:selectItems value="#{helperBean.ufsCobranca}" var="i" itemLabel="#{i}" />
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <label>Situação</label>
                                            <select jsf:id="situacao" jsf:value="#{acompanhamentoObservatorioBean.filter.situacaoProcessoCredenciamento}"
                                                    class="form-control full-width select2" size="1" disabled="#{disabled}">
                                                <f:selectItem itemLabel="Selecione" />
                                                <f:selectItems value="#{helperBean.situacoesProcessoCredenciamento}" var="i" itemLabel="#{i}" />
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-4">
                                        <div class="form-group form-group-default form-group-default-select2">
                                            <label>Financeira</label>
                                            <select jsf:id="financeira" jsf:value="#{acompanhamentoObservatorioBean.filter.financeira}"
                                                    class="form-control full-width select2" size="1" disabled="#{disabled}">
                                                <f:selectItem itemLabel="Selecione" />
                                                <f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}" />
                                                <f:converter converterId="financeiraConverter"/>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-lg-12">
                                        <hr class="buttons" />
                                        <a href="#{request.contextPath}/acompanhamento/acompanhamento-place.xhtml" class="btn btn-default">Limpar</a>
                                        <sec:authorize ifAnyGranted="CADASTRAR_COBRANCA">
                                            <a href="#{request.contextPath}/acompanhamento/form-add.xhtml" class="btn btn-primary btn-cons">Novo</a>
                                        </sec:authorize>
                                        <button type="submit" class="btn btn-primary btn-cons" jsf:action="#{acompanhamentoObservatorioBean.search}">Pesquisar</button>
                                    </div>
                                </div>
                                <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="row" jsf:rendered="#{acompanhamentoObservatorioBean.list != null}">
                    <div class="table-responsive">
                        <form jsf:id="formDataTable" jsf:prependId="false">
                            <div style="min-height: 30px;">
                                <div style="float: right; padding: 0;">
                                    Resultados por página
                                    <select jsf:id="registros" jsf:value="#{acompanhamentoObservatorioBean.size}"
                                            size="1">
                                        <f:selectItem itemLabel="10" itemValue="10"/>
                                        <f:selectItem itemLabel="25" itemValue="25"/>
                                        <f:selectItem itemLabel="50" itemValue="50"/>
                                        <f:selectItem itemLabel="100" itemValue="100"/>
                                        <f:ajax execute="@this" render="formDataTable"
                                                listener="#{acompanhamentoObservatorioBean.search}"
                                                onevent="function(data){$.masks();}"/>
                                    </select>
                                </div>
                            </div>
                            <p:dataTable id="dataTable" var="object" value="#{acompanhamentoObservatorioBean.list}"
                                         paginator="true" rows="#{acompanhamentoObservatorioBean.size}"
                                         paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
                                         currentPageReportTemplate="({currentPage} de {totalPages})"
                                         paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
                                         lazy="true" draggableColumns="true" resizableColumns="true"
                                         tableStyleClass="table table-striped table-bordered table-hover  table-responsive-block">
                                <p:column headerText="Uf">
                                    #{object.uf}
                                </p:column>
                                <p:column headerText="Financeira">
                                    #{object.financeira.nome}
                                </p:column>
                                <p:column headerText="Status">
                                    #{object.situacaoProcessoCredenciamento.descricao}
                                </p:column>

                                <p:column headerText="Ações" styleClass="text-center">
                                    <a class="btn btn-link" title="Visualizar"
                                       href="#{request.contextPath}/acompanhamento/form-detail.xhtml?id=#{object.id}">
                                        <span class="fa fa-search"/>
                                    </a>
                                    <a class="btn btn-link" title="Editar"
                                       href="#{request.contextPath}/acompanhamento/form-update.xhtml?id=#{object.id}">
                                        <span class="fa fa-edit"/>
                                    </a>
                                    <h:commandLink styleClass="btn btn-link" title="Excluir"
                                                   onclick="return confirm('Confirma a exclusão?')"
                                                   action="#{acompanhamentoObservatorioBean.delete(object.id)}">
                                        <span class="fa fa-remove"/>
                                    </h:commandLink>
                                </p:column>
                            </p:dataTable>
                            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>

</ui:composition>
