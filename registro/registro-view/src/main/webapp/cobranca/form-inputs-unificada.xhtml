<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf">

    <div class="row">
        <div class="col-lg-2">
            <div class="form-group form-group form-group-default form-group-default-select2 required">
                <label>UF</label>
                <select jsf:id="uf" jsf:value="#{cobrancaBean.entity.estado}" jsf:label="UF" required="required"
                        jsf:required="false"
                        class="form-control full-width select2" size="1" disabled="disabled">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:selectItems value="#{helperBean.ufsCobranca}" var="e" itemValue="#{e}" itemLabel="#{e}"/>
                </select>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="form-group form-group form-group-default form-group-default-select2">
                <label>Financeira</label>
                <select jsf:id="financeiras" jsf:value="#{cobrancaBean.entity.financeira}"
                        class="form-control full-width select2" size="1" disabled="disabled" jsf:label="CNPJ">
                    <f:converter converterId="financeiraConverter"/>
                    <f:selectItem itemLabel="Selecione"/>
                    <f:selectItems value="#{helperSessionBean.financeiras}" var="i" itemValue="#{i}"
                                   itemLabel="#{i.documento} - #{i.nome}"/>
                </select>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="form-group form-group form-group-default">
                <label>Registro do pagamento</label>
                <p:calendar id="dataRegistroPagamento"
                            disabled="#{disabled eq 'disabled' or helperSessionBean.roleCanUpdatePagamento eq false}"
                            styleClass="form-control"
                            locale="pt_BR"
                            navigator="true"
                            yearRange="c-10:c+10"
                            value="#{cobrancaBean.entity.dataRegistroPagamento}" pattern="dd/MM/yyyy" mask="true">
                    <f:ajax event="dateSelect" execute="@this"/>
                    <f:ajax event="blur" execute="@this"/>
                </p:calendar>
            </div>
        </div>
        <div class="col-lg-2">
            <div class="form-group form-group-default form-group-default-select2">
                <label>Pagamento</label>
                <select jsf:id="pagamento" jsf:value="#{cobrancaBean.entity.pagamentoCobranca}"
                        class="form-control full-width select2" size="1"
                        jsf:disabled="#{disabled eq 'disabled' or helperSessionBean.roleCanUpdatePagamento eq false}"
                        jsf:label="Pagamento">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:ajax execute="@this"/>
                    <f:selectItems value="#{helperBean.pagamentoCobrancas}" var="i" itemLabel="#{i}"/>
                </select>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Início</label>
                <p:calendar id="dataInicio" styleClass="form-control" locale="pt_BR" disabled="true" navigator="true"
                            yearRange="c-10:c+10"
                            value="#{cobrancaBean.entity.dataInicio}" pattern="dd/MM/yyyy" mask="true"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Fim</label>
                <p:calendar id="dataFim" styleClass="form-control" locale="pt_BR" disabled="true" navigator="true"
                            yearRange="c-10:c+10"
                            value="#{cobrancaBean.entity.dataFim}" pattern="dd/MM/yyyy" mask="true"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Total de Registros</label>
                <input jsf:id="registros" type="text" jsf:value="#{cobrancaBean.entity.quantidadeRegistros}"
                       maxlength="7"
                       jsf:label="Registros" class="form-control integer" disabled="disabled"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor Total Cobrado</label>
                <input jsf:value="#{cobrancaBean.entity.valorCobranca}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Registros Principal</label>
                <input jsf:id="registroPrincipal" type="text" jsf:value="#{cobrancaBean.entity.quantidadePrincipal}"
                       maxlength="7"
                       jsf:label="Registros" class="form-control integer" disabled="disabled"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Registros Aditivo</label>
                <input jsf:id="registrosAditivos" type="text" jsf:value="#{cobrancaBean.entity.quantidadeAditivo}"
                       maxlength="7"
                       jsf:label="Registros" class="form-control integer" disabled="disabled"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor DETRAN Principal</label>
                <input jsf:id="valorCobrancaPrincipal" jsf:value="#{cobrancaBean.entity.valorDetranPrincipal}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor DETRAN Aditivo</label>
                <input jsf:id="valorCobrancaAditivo" jsf:value="#{cobrancaBean.entity.valorDetranAditivo}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor Total do DETRAN</label>
                <input jsf:id="valorDetran" jsf:value="#{cobrancaBean.entity.valorDetran}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor da Credenciada</label>
                <input jsf:id="valorCredenciada" jsf:value="#{cobrancaBean.entity.valorCredenciada}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor do Desconto</label>
                <input jsf:id="valorDesconto" jsf:value="#{cobrancaBean.entity.valorDesconto}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group-default form-group-default-select2 required">
                <label>Situação</label>
                <select jsf:id="situacaoFinanceira" jsf:value="#{cobrancaBean.entity.situacaoCobranca}"
                        class="form-control full-width select2" size="1" disabled="#{disabled}" required="required">
                    <f:selectItem itemLabel="Selecione"/>
                    <f:ajax execute="@this"/>
                    <f:selectItems value="#{helperBean.situacoesCobranca}" var="i" itemLabel="#{i}"/>
                </select>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Total de Registros RSNG</label>
                <input jsf:id="registrosSng" type="text" jsf:value="#{cobrancaBean.entity.quantidadeRegistrosSng}"
                       maxlength="7"
                       jsf:label="Registros" class="form-control integer" disabled="disabled"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Baixas RSNG</label>
                <input jsf:id="baixasSng" type="text" jsf:value="#{cobrancaBean.entity.quantidadeBaixaSng}"
                       maxlength="7"
                       jsf:label="Registros" class="form-control integer" disabled="disabled"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Cancelamento de Baixas RSNG</label>
                <input jsf:id="cancelamentoBaixasSng" type="text" jsf:value="#{cobrancaBean.entity.quantidadeCancelamentoBaixaSng}"
                       maxlength="7"
                       jsf:label="Registros" class="form-control integer" disabled="disabled"/>
            </div>
        </div>
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor Total RSNG</label>
                <input jsf:id="valorSng" jsf:value="#{cobrancaBean.entity.valorCobrancaSng}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Bilhetagem RSNG</label>
                <input jsf:id="bilhetagem" jsf:value="#{cobrancaBean.entity.gravame.valorTotal}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
    </div>
    <div class="row" jsf:rendered="#{cobrancaBean.entity.isUnificada()}">
        <div class="col-lg-3">
            <div class="form-group form-group form-group-default required">
                <label>Valor Detran reembolsável</label>
                <input jsf:id="valorDetranReembolsavel" jsf:value="#{cobrancaBean.entity.valorDetranReembolsavel}"
                       class="form-control" disabled="disabled" type="text">
                    <f:convertNumber pattern="#,##0.00" locale="pt_BR"/>
                </input>
            </div>
        </div>
    </div>
    <div class="panel panel-default" jsf:rendered="#{cobrancaBean.mostrarLinhaDigitavel(cobrancaBean.entity)}">
        <div class="panel-heading">
            <div class="panel-title">Linha Digitável do DETRAN</div>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-lg-6">
                    <p:commandLink immediate="true" styleClass="btn btn-link" value="Download" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
                        <p:fileDownload value="#{cobrancaBean.fileLinhaDigitavel}"/>
                        <span class="mdi mdi-barcode text-warning"/>
                    </p:commandLink>
                </div>
            </div>
        </div>
    </div>
    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">Boletos</div>
        </div>
        <div class="panel-body" jsf:id="labelBoleto">
            <div class="row">
                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadBoletoCredenciada}" mode="advanced"
                                  dragDropSupport="true"
                                  update="labelBoleto" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Boleto da Credenciada"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="Boleto deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                </div>
                <div class="col-lg-6">
                    <b>Boleto da Credenciada:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="Download" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.boletoEmitido}">
                        <p:fileDownload value="#{cobrancaBean.file}"/>
                        <span class="mdi mdi-barcode text-success"/>
                    </p:commandLink>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadBoletoDetran}" mode="advanced"
                                  dragDropSupport="true"
                                  update="labelBoleto" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Boleto de Reembolso"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="Boleto deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                </div>
                <div class="col-lg-6">
                    <b>Boleto de Reembolso:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="Download" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.boletoReembolsoEmitido}">
                        <p:fileDownload value="#{cobrancaBean.fileBoletoReembolso}"/>
                        <span class="mdi mdi-barcode text-primary"/>
                    </p:commandLink>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <b>Estados sem Reembolso: </b>
                    <p>#{cobrancaBean.estadosSemReembolso}</p>
                </div>
            </div>
            <div class="row">

                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadBoletoDetranCobrancaUnificada}"
                                  mode="advanced" dragDropSupport="true"
                                  update="messages labelBoleto" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload"
                                  label="Selecionar Boleto do DETRAN [Cliente]"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="Boleto deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M">
                        <f:attribute name="boletoDetranCliente" value="S"/>
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                    </p:fileUpload>
                </div>
                <div class="col-lg-6">
                    <b>Boleto do DETRAN [Cliente]:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="Download" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.boletoDetranUnificadaCliente}">
                        <p:fileDownload value="#{cobrancaBean.fileBoletoUnificadaDetranCliente}"/>
                        <span class="mdi mdi-barcode text-warning"/>
                    </p:commandLink>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6">
                    <b>Estados com Reembolso:</b>
                    <p>#{cobrancaBean.estadosComReembolso}</p>
                </div>
            </div>
            <div class="row">

                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadBoletoDetranCobrancaUnificada}"
                                  mode="advanced" dragDropSupport="true"
                                  update="messages labelBoleto" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload"
                                  label="Selecionar Boleto do DETRAN [Financeiro Place]"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="Boleto deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M">
                        <f:attribute name="boletoDetranFinanceiroPlace" value="S"/>
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                    </p:fileUpload>
                </div>
                <div class="col-lg-6">
                    <b>Boleto do DETRAN [Financeiro Place]:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="Download" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.boletoDetranUnificadaFinanceiroPlace}">
                        <p:fileDownload value="#{cobrancaBean.fileBoletoUnificadaDetranFinanceiroPlace}"/>
                        <span class="mdi mdi-barcode text-warning"/>
                    </p:commandLink>
                </div>
            </div>
        </div>
    </div>
    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">Nota Fiscal</div>
        </div>
        <div class="panel-body" jsf:id="labelNf">
            <div class="row">
                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadNF}" mode="advanced"
                                  dragDropSupport="true"
                                  update="labelNf" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Nota Fiscal"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="NF deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                </div>
                <div class="col-lg-6">
                    <b>Nota Fiscal:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link" value="#{cobrancaBean.entity.notaFiscal}"
                                   title="Download da NF" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.notaFiscal != null}">
                        <span class="mdi mdi-receipt text-success"/>
                        <p:fileDownload value="#{cobrancaBean.fileNf}"/>
                    </p:commandLink>
                </div>

                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadXMLNF}" mode="advanced"
                                  dragDropSupport="true"
                                  update="labelNf" sizeLimit="10000000" allowTypes="/(\.|\/)(xml)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar XML da Nota Fiscal"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="NF deve estar no formato .xml"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                </div>
                <div class="col-lg-6">
                    <b>XML da Nota Fiscal:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="#{cobrancaBean.entity.xmlNotaFiscal}"
                                   title="Download do XML da NF" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.xmlNotaFiscal != null}">
                        <span class="mdi mdi-receipt text-success"/>
                        <p:fileDownload value="#{cobrancaBean.fileXmlNf}"/>
                    </p:commandLink>
                </div>
                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadNotaReembolso}" mode="advanced"
                                  dragDropSupport="true"
                                  update="labelNf" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Nota Reembolso"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="NF deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                </div>
                <div class="col-lg-6">
                    <b>Nota de Reembolso:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="#{cobrancaBean.entity.notaReembolso}"
                                   title="Download da Nota de Reembolso" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.notaReembolso != null}">
                        <span class="mdi mdi-receipt text-primary"/>
                        <p:fileDownload value="#{cobrancaBean.fileNotaReembolso}"/>
                    </p:commandLink>
                </div>
            </div>
        </div>
    </div>


    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">SNG - Registro de Gravames</div>
        </div>
        <div class="panel-body" jsf:id="labelGNf">
            <div class="row">
                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadBoletoSng}" mode="advanced" dragDropSupport="true"
                                  update="messages labelGNf" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Boleto"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="Boleto deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                </div>
                <div class="col-lg-6">
                    <b>Boleto:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link"
                                   value="Download" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.boletoSngEmitido}">
                        <p:fileDownload value="#{cobrancaBean.fileSngBoleto}"/>
                        <span class="mdi mdi-barcode text-success"/>
                    </p:commandLink>
                </div>

                <div class="col-lg-6" jsf:rendered="#{disabled != 'disabled'}">
                    <p:fileUpload listener="#{cobrancaBean.handleFileUploadSngNF}" mode="advanced" dragDropSupport="true"
                                  update="messages labelGNf" sizeLimit="10000000" allowTypes="/(\.|\/)(pdf)$/" auto="true"
                                  rendered="#{disabled != 'disabled'}"
                                  cancelLabel="Cancelar" uploadLabel="Upload" label="Selecionar Nota Fiscal"
                                  fileLimit="1" multiple="false"
                                  fileLimitMessage="Selecione no máximo 1 arquivo."
                                  invalidFileMessage="NF deve estar no formato .pdf"
                                  invalidSizeMessage="Tamanho máximo 10M"/>
                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                </div>
                <div class="col-lg-6">
                    <b>Nota Fiscal:</b>
                    <p:commandLink immediate="true" styleClass="btn btn-link" value="#{cobrancaBean.entity.notaFiascalSng}"
                                   title="Download da NF" ajax="false"
                                   onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                   rendered="#{cobrancaBean.entity.notaFiascalSng != null}">
                        <span class="mdi mdi-receipt text-success"/>
                        <p:fileDownload value="#{cobrancaBean.fileSngNf}"/>
                    </p:commandLink>
                </div>
            </div>
        </div>
    </div>


    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">Observações</div>
        </div>
        <div class="panel-body" jsf:id="observacao">
            <div class="row">
                <div class="col-lg-12">
                    <div class="form-group form-group form-group-default form-group-default-select2">
                        <label>Descrição</label>
                        <textarea jsf:value="#{cobrancaBean.entity.observacao}" jsf:label="Descrição"
                                  style="height: 450px; font-family: monospace"
                                  class="form-control" disabled="#{disabled}">
                            <f:ajax execute="@this"/>
                        </textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default" jsf:rendered="#{cobrancaBean.entity.financeira.documento eq '02916265000160'}">
        <div class="panel-heading">
            <div class="panel-title">Número do Pedido</div>
        </div>
        <div class="panel-body" jsf:id="labelpedido">
            <div class="row">
                <div class="col-lg-6">
                    <label>Número do Pedido:</label>
                    <input jsf:id="numeroPedido" jsf:value="#{cobrancaBean.entity.numeroPedido}"
                           class="form-control" disabled="#{disabled}" type="text">
                        <f:ajax execute="@this"/>
                    </input>
                </div>
            </div>
        </div>
    </div>
</ui:composition>
