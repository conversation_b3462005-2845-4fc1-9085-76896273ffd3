<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Mensagem de Retorno</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-12">
												<div class="form-group form-group-default">
													<label>Descrição</label> <input jsf:id="descricaoMensagem" type="text" jsf:value="#{mensagemRetornoBean.filter.descricao}" class="form-control" />
												</div>
											</div>
										</div>
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Código</label> <input jsf:id="codigoMensagem" type="text" jsf:value="#{mensagemRetornoBean.filter.codigo}" class="form-control" />
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>UF</label>
													<select jsf:id="ufRegistros" jsf:value="#{mensagemRetornoBean.filter.uf}" class="form-control full-width uf select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.ufs}" var="i" />
													</select>
												</div>
											</div>
			
											<div class="col-lg-4">
												<div class="form-group form-group-default form-group-default-select2">
													<label>Tipo de Mensagem</label>
													<select jsf:id="tipoMensagem" jsf:value="#{mensagemRetornoBean.filter.tipoMensagem}" class="form-control full-width uf select2" size="1">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.tiposMensagem}" var="i" />
													</select>
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/mensagemretorno/list.xhtml" class="btn btn-default">Limpar</a>
												<sec:authorize ifAnyGranted="CADASTRAR_MENSAGEM_RETORNO">
													<a href="#{request.contextPath}/mensagemretorno/form-add.xhtml" class="btn btn-primary btn-cons">Novo</a>
												</sec:authorize>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{mensagemRetornoBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página <select jsf:id="registros" jsf:value="#{mensagemRetornoBean.size}" size="1" >
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{mensagemRetornoBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{mensagemRetornoBean.list}" paginator="true" rows="#{mensagemRetornoBean.size}" paginatorPosition="bottom"
										emptyMessage="Nenhum registro encontrado" currentPageReportTemplate="({currentPage} de {totalPages})"
										paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="Código">
											<a class="btn btn-link"
											   href="#{request.contextPath}/mensagemretorno/form-detail.xhtml?id=#{object.id}">#{object.codigo}</a>
										</p:column>
										<p:column headerText="Descrição">
											#{object.descricao}
										</p:column>
										<p:column headerText="UF">
											#{object.uf}
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<sec:authorize ifAnyGranted="CONSULTAR_MENSAGEM_RETORNO">
												<a class="btn btn-link" title="Visualizar" href="#{request.contextPath}/mensagemretorno/form-detail.xhtml?id=#{object.id}"> <span class="fa fa-search" />
												</a>
											</sec:authorize>
											<sec:authorize ifAnyGranted="CADASTRAR_MENSAGEM_RETORNO">
												<a class="btn btn-link" title="Editar" href="#{request.contextPath}/mensagemretorno/form-update.xhtml?id=#{object.id}"> <span class="fa fa-edit" />
												</a>
											</sec:authorize>
											<sec:authorize ifAnyGranted="EXCLUIR_MENSAGEM_RETORNO">
												<h:commandLink styleClass="btn btn-link" title="Excluir" onclick="return confirm('Confirmar a exclusão da Mensagem?')"
													action="#{mensagemRetornoBean.delete(object.id)}">
													<span class="fa fa-remove" />
												</h:commandLink>
											</sec:authorize>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>