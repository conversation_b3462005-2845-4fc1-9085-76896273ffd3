<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">

		<f:metadata>
			<f:viewParam id="id" name="id" value="#{remessaDocumentoBean.idToEdit}"/>
			<f:viewAction action="#{remessaDocumentoBean.loadDetails()}"/>	
		</f:metadata>  
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Remes<PERSON></h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Atualizar</h6>
									<h:messages id="messages" warnClass="alert alert-warning alert-dismissable" infoClass="alert alert-success alert-dismissable" errorClass="alert alert-danger alert-dismissable" />
									<form jsf:id="formRemessa" jsf:prependId="false">
									   <sec:authorize ifAllGranted="REGISTRAR_REMESSA_CHASSI">
											<ui:include src="form-inputs.xhtml">
												<ui:param name="disabled" value="disabled"></ui:param>	
											</ui:include>
										</sec:authorize>
									    <sec:authorize ifAllGranted="REGISTRAR_REMESSA_CHASSI_USUARIO">
										      <ui:include src="form-inputs-usuario.xhtml">
											     <ui:param name="disabled" value="disabled"></ui:param>	
										      </ui:include>
										</sec:authorize>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/remessadocumento/list.xhtml" class="btn btn-default">Voltar</a>

												<p:commandLink actionListener="#{remessaDocumentoBean.remessaDocumento(remessaDocumentoBean.entity)}"
													styleClass="btn btn-primary btn-cons" title="Download do Arquivo de Remessa" ajax="false"
													onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
													Download Arquivo Remessa
													<p:fileDownload value="#{remessaDocumentoBean.file}" />
												</p:commandLink>

												<button type="submit" class="btn btn-primary btn-cons" jsf:id="btnExportarXls">
													Download Retorno Remessa
													<p:dataExporter encoding="iso-8859-1" type="xls" target="dataTable" fileName="retorno-remessa" />
												</button>

												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
	
</ui:composition>
