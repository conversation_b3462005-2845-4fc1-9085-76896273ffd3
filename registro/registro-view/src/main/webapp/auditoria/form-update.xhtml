<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
		<f:metadata>
			<f:viewParam id="id" name="id" value="#{auditoriaBean.idToEdit}" />
			<f:viewAction action="#{auditoriaBean.loadDetails()}" />
		</f:metadata>
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Contrato</h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Auditar Documentos</h6>
									
									<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
										infoClass="alert alert-success alert-dismissable"
										errorClass="alert alert-danger alert-dismissable" />
											
									<div>
										<div style="position:relative; width: 60%; height: 100%; float: left;">
											<form jsf:id="form" jsf:prependId="false">
												<ui:include src="/contrato/form-inputs.xhtml">
													<ui:param name="bean" value="#{auditoriaBean}"></ui:param>
													<ui:param name="disabled" value="disabled"></ui:param>
												</ui:include>
												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
											</form>
										</div>
										<div style="position:relative; width: 39%; height: 100%; float: left; margin-left: 1%">
											<ui:repeat var="d" value="#{auditoriaBean.entity.anexos}" >
												<h:panelGroup rendered="#{d.pdf}">
													<b>#{d.nomeArquivo}</b>
													<object width="100%"  style="height: 950px;" data="#{request.contextPath}/download/pdf/#{d.referenciaArquivo}" 
														type="application/pdf">
													    <p>Seu navegador não tem um plugin pra PDF</p>
													</object>
												</h:panelGroup>	
											</ui:repeat>
											<h:panelGroup rendered="#{auditoriaBean.entity.possuiImagemAnexo}">
												<b> Imagens anexadas. Clique para expandir</b>
													<style type="text/css">
														.ui-lightbox-nav-right {
														    display: none !important;
														}
														.ui-lightbox-nav-left {
														    display: none !important;
														}
													
													</style>
													<p:lightBox >
														<ui:repeat var="d"  value="#{auditoriaBean.entity.anexos}" >
															<h:panelGroup rendered="#{!d.pdf}">
																<h:outputLink   title="#{d.nomeArquivo}" value="#{request.contextPath}/download/img/#{d.referenciaArquivo}">
																	<img style="max-width: 95%"  src="#{request.contextPath}/download/img/#{d.referenciaArquivo}" alt="#{d.nomeArquivo}"/>
																</h:outputLink>
																<br/><br/>
															</h:panelGroup>
														</ui:repeat>	
													</p:lightBox>
												
											</h:panelGroup>	
										</div>
									</div>
									<br />
									<div style="clear: left;">
										<form jsf:id="formAudit" jsf:prependId="false" class="form">
											<div class="panel panel-default">
										         <div class="panel-heading">
										         	<div class="panel-title">
										             Auditoria
										             </div>
										         </div>
										         <div class="panel-body fix-checkbox">
													<div class="row">
														<div class="col-lg-12">
															<div class="form-group form-group form-group-default form-group-default-select2 required">
																<label>Aprovado</label>
																<select jsf:id="aprovadoAuditoria" jsf:value="#{auditoriaBean.registroAuditoria.aprovado}"
																	class="form-control full-width select2" required="true" jsf:required="true" size="1"
																	jsf:label="Aprovado">
																	<f:selectItem itemLabel="Selecione" />
																	<f:selectItems var="o" value="#{helperBean.simNao}" itemLabel="#{o.descricao}"/>
																	<f:ajax execute="@this" render="mensagemAprovacaoAuditoria"/>
																</select>
															</div>
														</div>
													</div>
													<div class="row">
														<div class="col-lg-12">
															<div class="form-group form-group form-group-default">
																<label>Mensagem</label>
																<span jsf:id="mensagemAprovacaoAuditoria">
																<textarea maxlength="255" jsf:rendered="#{auditoriaBean.registroAuditoria.aprovado eq 'N'}" rows="3" jsf:label="Mensagem" required="required" 
																	jsf:value="#{auditoriaBean.registroAuditoria.mensagem}"
																	class="form-control" style="height: 80px;"/>
																<textarea maxlength="255" jsf:rendered="#{auditoriaBean.registroAuditoria.aprovado ne 'N'}" rows="3" jsf:label="Mensagem" 
																	jsf:value="#{auditoriaBean.registroAuditoria.mensagem}"
																	class="form-control" style="height: 80px;"/>	
																</span>
																
															</div>
														</div>
													</div>
												</div>
											</div>
											<div class="row text-center">
												<div class="col-lg-12">
													<hr class="buttons" />
													<a href="#{request.contextPath}/auditoria/list.xhtml"
														class="btn btn-default">Voltar</a>
													<sec:authorize ifAllGranted="AUDITAR_DOCUMENTO">
														<button type="submit" jsf:action="#{auditoriaBean.save}"
															class="btn btn-primary btn-cons">Salvar</button>
													</sec:authorize>											
													<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
												</div>
											</div>
										</form>
									</div>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
	
</ui:composition>
