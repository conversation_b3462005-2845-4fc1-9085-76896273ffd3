<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:sec="http://www.springframework.org/security/tags"
                template="/templates/blank.xhtml">

    <ui:define name="content">

        <f:metadata>
            <f:viewParam id="id" name="id" value="#{remessaBean.idToEdit}"/>
            <f:viewAction action="#{remessaBean.loadDetails()}"/>
        </f:metadata>

        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Remessas de Contrato</h4>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group form-group form-group-default">
                                    <div><b>#{remessaBean.entity.templateRemessa.descricao}</b></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12">
                                <h6 class="font-14 mt-4">Pesquisar</h6>
                                <form jsf:id="form" jsf:prependId="false">
                                    <h:messages id="messagesBusca" warnClass="alert alert-warning alert-dismissable"
                                                infoClass="alert alert-success alert-dismissable"
                                                errorClass="alert alert-danger alert-dismissable"/>
                                    <div class="row">
                                        <div class="col-lg-3">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Transmitido</label>
                                                <select jsf:id="transmitido"
                                                        jsf:value="#{remessaBean.filter.transmitida}"
                                                        class="form-control full-width select2" size="1">
                                                    <f:selectItem itemLabel="Selecione"/>
                                                    <f:selectItems value="#{helperBean.simNao}" var="i"
                                                                   itemLabel="#{i.descricao}"/>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-3">
                                            <div class="form-group form-group-default form-group-default-select2">
                                                <label>Registrado no Detran</label>
                                                <select jsf:id="aceito" jsf:value="#{remessaBean.filter.valida}"
                                                        class="form-control full-width select2" size="1">
                                                    <f:selectItem itemLabel="Selecione"/>
                                                    <f:selectItems value="#{helperBean.simNao}" var="i"
                                                                   itemLabel="#{i.descricao}"/>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group form-group-default">
                                                <label>Descrição do Erro</label>
                                                <input jsf:id="descricaoErro" type="text"
                                                       jsf:value="#{remessaBean.filter.descricaoErro}"
                                                       class="form-control first">
                                                </input>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-lg-12">
                                            <hr class="buttons"/>
                                            <a href="#{request.contextPath}/remessa/form-detail.xhtml?id=#{remessaBean.entity.id}"
                                               class="btn btn-default">Limpar</a>
                                            <button type="submit" class="btn btn-primary btn-cons"
                                                    jsf:action="#{remessaBean.search}">Pesquisar
                                            </button>
                                        </div>
                                    </div>
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                infoClass="alert alert-success alert-dismissable"
                                                errorClass="alert alert-danger alert-dismissable"/>

                                    <div jsf:id="remessa">
                                            <div class="panel-heading">
                                                <div class="panel-title">Contratos</div>
                                            </div>
                                            <div class="panel-body fix-checkbox">
                                                <form jsf:id="formDataTable" jsf:prependId="false">
                                                    <div style="min-height: 30px;">
                                                        <div style="float: right; padding: 0;">
                                                            Resultados por página
                                                            <select jsf:id="registros" jsf:value="#{remessaBean.size}" size="1">
                                                                <f:selectItem itemLabel="10" itemValue="10"/>
                                                                <f:selectItem itemLabel="25" itemValue="25"/>
                                                                <f:selectItem itemLabel="50" itemValue="50"/>
                                                                <f:selectItem itemLabel="100" itemValue="100"/>
                                                                <f:ajax execute="@this" render="formDataTable"
                                                                        listener="#{remessaBean.search}"
                                                                        onevent="function(data){$.masks();}"/>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <p:dataTable id="dataTable" var="object"
                                                                 value="#{remessaBean.entity.registros}"
                                                                 paginator="true"
                                                                 rows="#{remessaBean.size}"
                                                                 paginatorPosition="bottom"
                                                                 emptyMessage="Nenhum registro encontrado"
                                                                 currentPageReportTemplate="({currentPage} de {totalPages})"
                                                                 paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
                                                                 lazy="true" draggableColumns="true"
                                                                 resizableColumns="true"
                                                                 tableStyleClass="table table-hover m-0">
                                                        <p:column headerText="Contrato #{remessaBean.size}">
                                                            <h:outputLabel
                                                                    value="#{object.numeroContrato}" />
                                                        </p:column>
                                                        <p:column headerText="CHASSI">
                                                            <h:outputLabel value="#{object.chassi}"/>
                                                        </p:column>
                                                        <p:column headerText="Transmitido">
                                                            <h:outputLabel
                                                                    value="#{object.transmitida == 'S' ? 'Sim' : 'Não' }"/>
                                                        </p:column>
                                                        <p:column headerText="Registrado no DETRAN">
                                                            <h:outputLabel
                                                                    value="#{object.valida  == 'S' ? 'Sim' : 'Não' }"/>
                                                        </p:column>
                                                        <p:column headerText="Mensagem">
                                                            <h:outputLabel escape="false"
                                                                           value="#{object.descricaoErro}" />
                                                        </p:column>
                                                    </p:dataTable>
                                                    <input type="hidden" name="${_csrf.parameterName}"
                                                           value="${_csrf.token}"/>
                                                </form>
                                            </div>
                                    </div>

                                    <form jsf:id="formSend" jsf:prependId="false">
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/remessa/list.xhtml"
                                                   class="btn btn-default">Voltar</a>

                                                <sec:authorize ifAnyGranted="EXCLUIR_REMESSA">
                                                    <h:commandLink styleClass="btn btn-primary btn-cons" title="Excluir"
                                                                   onclick="return confirm('Confirmar a exclusão da remessa?')"
                                                                   action="#{remessaBean.delete(remessaBean.entity.id)}">
                                                        Excluir
                                                    </h:commandLink>
                                                </sec:authorize>

                                                <p:commandLink styleClass="btn btn-primary btn-cons"
                                                               actionListener="#{remessaBean.remessaArquivo(remessaBean.entity)}"
                                                               title="Download do Arquivo de Remessa" ajax="false"
                                                               onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
                                                    Download arquivo da remessa
                                                    <p:fileDownload value="#{remessaBean.file}"/>
                                                </p:commandLink>

                                                <p:commandLink styleClass="btn btn-primary btn-cons"
                                                               rendered="#{remessaBean.entity.status == 1}"
                                                               actionListener="#{remessaBean.remessaArquivo(remessaBean.entity)}"
                                                               title="Download do Retorno da Remessa" ajax="false"
                                                               onclick="PrimeFaces.monitorDownload(loading, closeLoading);">
                                                    Download retorno da remessa
                                                    <p:fileDownload value="#{remessaBean.fileReturn}"/>
                                                </p:commandLink>

                                                <input type="hidden" name="${_csrf.parameterName}"
                                                       value="${_csrf.token}"/>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>

</ui:composition>
