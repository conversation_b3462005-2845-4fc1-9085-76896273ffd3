<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank-public.xhtml">
	
	<ui:define name="local">
		<li class="breadcrumb-item active"><a href="#{request.contextPath}/public/qrcode.xhtml">Validação de Comprovante ou Certidão</a></li>
	</ui:define>

	<ui:define name="content">
		<div class="row">
			<div class="col-lg-12">
				<div class="panel panel-default">
					<div class="panel-heading">
						<div class="panel-title">Validar Comprovante com QR Code</div>
					</div>
					<div class="panel-body">
						<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
							infoClass="alert alert-success alert-dismissable"
							errorClass="alert alert-danger alert-dismissable" />
						<form jsf:id="form" jsf:prependId="false">
							<div class="12">
								<div class="col-lg-6">
									<div class="form-group form-group-default">
										<label>QR Code</label>
										<p:photoCam widgetVar="pc" listener="#{consultaQrCodeBean.oncapture}" />
									</div>
								</div>
							</div>
							<div class="col-lg-12">
								    <p:captcha label="Captcha" language="pt_BR"/>
							</div>
							<div class="row text-center">
								<div class="col-lg-12">
									<hr class="buttons" />
									<a href="#{request.contextPath}/public/qrcode.xhtml" class="btn btn-default">Limpar</a>
									<button type="submit" class="btn btn-primary btn-cons" onclick="PF('pc').capture()">Capturar</button>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>