<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/templates/blank.xhtml">

    <ui:define name="content">
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Remessa RSNG</h4>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Nova</h6>
                                    <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                infoClass="alert alert-success alert-dismissable"
                                                errorClass="alert alert-danger alert-dismissable"/>
                                    <form jsf:id="form" jsf:prependId="false" class="form">
                                        <ui:include src="form-inputs.xhtml"/>

                                        <div jsf:id="processamento" class="col-lg-12">
                                            <div class="row" jsf:rendered="#{remessaRsngBean.entity.id ne null}">
                                                <div class="table-responsive">
                                                    <form jsf:id="resultado" jsf:prependId="false">
                                                        <div style="min-height: 30px;">
                                                            <div style="float: right; padding: 0;">
                                                                Resultados por página
                                                                <select jsf:id="registro"
                                                                        jsf:value="#{remessaRsngBean.sizeItens}" size="1">
                                                                    <f:selectItem itemLabel="10" itemValue="10"/>
                                                                    <f:selectItem itemLabel="25" itemValue="25"/>
                                                                    <f:selectItem itemLabel="50" itemValue="50"/>
                                                                    <f:selectItem itemLabel="100" itemValue="100"/>
                                                                    <f:ajax execute="@this" render="formDataTable"
                                                                            listener="#{remessaRsngBean.search}"
                                                                            onevent="function(data){$.masks();}"/>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <p:dataTable id="dataTable" var="o"
                                                                     value="#{remessaRsngBean.itensRemessaB3}"
                                                                     paginator="true" rows="#{remessaRsngBean.sizeItens}"
                                                                     paginatorPosition="bottom"
                                                                     emptyMessage="Nenhum item encontrado"
                                                                     currentPageReportTemplate="({currentPage} de {totalPages})"
                                                                     paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
                                                                     lazy="true" draggableColumns="true"
                                                                     resizableColumns="true"
                                                                     tableStyleClass="table table-hover m-0">

                                                            <p:column headerText="Chassi">
                                                                #{o.chassi}
                                                            </p:column>

                                                            <p:column headerText="Status de Processamento">
                                                                #{o.status}
                                                            </p:column>

                                                            <p:column headerText="Contrato Gerado">
                                                                <a class="btn btn-link"
                                                                   href="#{request.contextPath}/contrato/form-detail.xhtml?id=#{o.contrato.id}">#{o.contrato.numeroRegistroEletronico}</a>
                                                            </p:column>

                                                            <p:column headerText="Status do Contrato">
                                                                <a class="btn btn-link"
                                                                   href="#{request.contextPath}/contrato/form-detail.xhtml?id=#{o.contrato.id}">#{o.contrato.situacao}</a>
                                                            </p:column>

                                                        </p:dataTable>
                                                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row text-center" jsf:id="buttons">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>

                                                <div style="display: contents">
                                                    <a href="#{request.contextPath}/remessarsng/list.xhtml"
                                                       class="btn btn-default">Voltar</a>
                                                </div>

                                                <div jsf:rendered="#{remessaRsngBean.entity.id == null}" style="display: contents">
                                                    <a href="#{request.contextPath}/remessarsng/form-add.xhtml"
                                                       class="btn btn-default">Cancelar</a>
                                                </div>

                                                <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>

</ui:composition>
