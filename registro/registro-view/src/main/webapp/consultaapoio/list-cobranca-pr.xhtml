<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                template="/templates/blank.xhtml">

    <ui:define name="content">

        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Cobranças PR</h4>

                        <div class="row" jsf:id="divConsultaCobrancas">
                            <div class="col-lg-12">
                                <div class="m-b-20">
                                    <h6 class="font-14 mt-4">Pesquisar</h6>
                                    <form jsf:id="form" jsf:prependId="false">
                                        <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                                                    infoClass="alert alert-success alert-dismissable"
                                                    errorClass="alert alert-danger alert-dismissable"/>
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <div class="form-group form-group-default form-group-default-select2 required">
                                                    <label>Financeira</label>
                                                    <select jsf:required="#{true}" jsf:id="financeira"
                                                            jsf:value="#{cobrancaPRBean.financeira}"
                                                            class="form-control full-width select2" size="1"
                                                            disabled="#{disabled}">
                                                        <f:selectItem itemLabel="Selecione"/>
                                                        <f:selectItems value="#{helperSessionBean.financeiras}" var="i"
                                                                       itemValue="#{i}"
                                                                       itemLabel="#{i.documento} - #{i.nome}"/>
                                                        <f:converter converterId="financeiraConverter"/>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="form-group form-group-default">
                                                    <label>Início</label>
                                                    <p:calendar styleClass="form-control" locale="pt_BR"
                                                                navigator="true" yearRange="c-10:c+10"
                                                                value="#{cobrancaPRBean.dataInicio}"
                                                                pattern="dd/MM/yyyy" mask="true"
                                                                label="Início da Vigência"
                                                    >
                                                    </p:calendar>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="form-group form-group-default">
                                                    <label>Fim</label>
                                                    <p:calendar styleClass="form-control" locale="pt_BR"
                                                                navigator="true" yearRange="c-10:c+10"
                                                                value="#{cobrancaPRBean.dataFim}" pattern="dd/MM/yyyy"
                                                                mask="true">
                                                    </p:calendar>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row text-center">
                                            <div class="col-lg-12">
                                                <hr class="buttons"/>
                                                <a href="#{request.contextPath}/consultaapoio/list-cobranca-pr.xhtml"
                                                   class="btn btn-default">Limpar</a>
                                                <button type="submit" class="btn btn-primary btn-cons"
                                                        jsf:action="#{cobrancaPRBean.consultar}">Pesquisar
                                                </button>
                                            </div>
                                        </div>
                                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div jsf:id="divRetornoCobranca">
                            <h:panelGroup rendered="#{cobrancaPRBean.retorno ne null}">

                                <form jsf:id="formDownload" jsf:prependId="false">
                                    <p:commandLink actionListener="#{cobrancaPRBean.download()}"
                                                   styleClass="btn btn-link"
                                                   title="Download do Boleto"
                                                   ajax="false">
                                        <span class="mdi mdi-download"/>
                                        <p:fileDownload value="#{cobrancaPRBean.file}"/>
                                        Download do Boleto de Cobrança
                                    </p:commandLink>
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>

                                <form jsf:id="relatorioDeCobranca" jsf:prependId="false">
                                    <p:commandLink onclick="PrimeFaces.monitorDownload(loading, closeLoading);"
                                                   styleClass="btn btn-link"
                                                   title="Relatório de Cobrança"
                                                   ajax="false"
                                                   action="#{cobrancaPRBean.excel(cobrancaPRBean.relatorioDeCobrancaResponse)}">
                                        <span class="fa fa-file-excel-o"/>
                                        Relatório de Cobrança
                                    </p:commandLink>
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>

                                </form>

                                <div class="row">
                                    <ui:repeat var="r" value="#{cobrancaPRBean.retorno}">
                                        <div class="col-lg-12">
                                            <div class="panel panel-default">
                                                <div class="panel-heading">
                                                    <div class="panel-title">Cobrança</div>
                                                </div>
                                                <div class="panel-body">
                                                    <div class="row">
                                                        <div class="col-lg-3">
                                                            <div class="form-group form-group form-group-default required">
                                                                <label>Mês</label>
                                                                <input type="text" jsf:value="#{r.mes}"
                                                                       class="form-control" disabled="disabled"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6">
                                                            <div class="form-group form-group form-group-default required">
                                                                <label>Financeira</label>
                                                                <input type="text"
                                                                       jsf:value="#{cobrancaPRBean.financeira.nome}"
                                                                       class="form-control" disabled="disabled"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <div class="form-group form-group form-group-default required">
                                                                <label>CNPJ</label>
                                                                <input type="text"
                                                                       jsf:value="#{cobrancaPRBean.financeira.documento}"
                                                                       class="form-control" disabled="disabled"/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <ui:repeat var="reg" value="#{r.registradoras}">
                                                        <ui:repeat var="a" value="#{reg.agentes}">
                                                            <div class="row">
                                                                <div class="col-lg-12">
                                                                    <div class="panel panel-default">
                                                                        <div class="panel-heading">
                                                                            <div class="panel-title">Boleto</div>
                                                                        </div>
                                                                        <div class="panel-body">
                                                                            <div class="row">
                                                                                <div class="col-lg-3">
                                                                                    <div class="form-group form-group form-group-default required">
                                                                                        <label>Situação do Boleto</label>
                                                                                        <input type="text"
                                                                                               jsf:value="#{a.situacaoBoleto}"
                                                                                               class="form-control"
                                                                                               disabled="disabled"/>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-lg-3">
                                                                                    <div class="form-group form-group form-group-default required">
                                                                                        <label>Valor do Boleto</label>
                                                                                        <input type="text"
                                                                                               jsf:value="#{a.valorBoleto}"
                                                                                               class="form-control"
                                                                                               disabled="disabled"/>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-lg-3">
                                                                                    <div class="form-group form-group form-group-default required">
                                                                                        <label>Valor Pago</label>
                                                                                        <input type="text"
                                                                                               jsf:value="#{a.valorPagto}"
                                                                                               class="form-control"
                                                                                               disabled="disabled"/>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="col-lg-3">
                                                                                    <div class="form-group form-group form-group-default required">
                                                                                        <label>Data Pagamento</label>
                                                                                        <input type="text"
                                                                                               jsf:value="#{a.dataPagamento}"
                                                                                               class="form-control"
                                                                                               disabled="disabled">
                                                                                            <f:convertDateTime
                                                                                                    type="both"/>
                                                                                        </input>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div class="row">
                                                                                <div class="col-lg-12">
                                                                                    <div class="form-group form-group form-group-default required">
                                                                                        <label>Boleto</label>
                                                                                        <input type="text"
                                                                                               jsf:value="#{a.codBoletoGRB}"
                                                                                               class="form-control"
                                                                                               disabled="disabled"/>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                        </ui:repeat>
                                                    </ui:repeat>
                                                </div>
                                            </div>
                                        </div>
                                    </ui:repeat>
                                </div>

                                <div class="row text-center">
                                    <div class="col-md-12">
                                        <hr class="buttons"/>
                                        <a href="#{request.contextPath}/consultaapoio/list-cobranca-pr.xhtml"
                                           class="btn btn-default">Limpar</a>
                                    </div>
                                </div>

                            </h:panelGroup>
                        </div>


                        <div class="row">
                            <div class="table-responsive">
                                <form jsf:id="formDataTable" jsf:prependId="false">

                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ui:define>
</ui:composition>