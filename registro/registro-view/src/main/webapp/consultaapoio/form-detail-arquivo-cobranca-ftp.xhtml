<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
	
		<f:metadata>
			<f:viewParam id="id" name="id" value="#{arquivoCobrancaBean.idToEdit}"/>
			<f:viewAction action="#{arquivoCobrancaBean.loadDetails()}"/>	
		</f:metadata> 
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Arquivo Cobranca SC</h4>
						<br />
						<div class="panel panel-default"
							jsf:rendered="#{arquivoCobrancaBean.entity != null and arquivoCobrancaBean.entity.conteudos.size() > 0}">
							<div class="panel-heading">
								<div class="panel-title">Arquivo</div>
							</div>
							<div class="panel-body fix-checkbox">
								<div class="row">
											<div class="col-lg-6">
												<div class="form-group form-group-default">
													<label>Nome</label>
													<input type="text" jsf:value="#{arquivoCobrancaBean.entity.nome}" disabled="disabled" class="form-control">
													</input>
												</div>
											</div>
							 				<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Período</label>
													<h:inputText disabled="true" styleClass="form-control" value="#{arquivoCobrancaBean.periodoFormatado}">
														<f:convertDateTime type="date"/>
													</h:inputText>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group-default">
													<label>UF</label>
													<h:inputText disabled="true" styleClass="form-control" value="#{arquivoCobrancaBean.entity.uf}">
													</h:inputText>
												</div>
											</div>
							 </div>
							 
							 
							 		
							</div>
						</div>
	            
					<div jsf:id="documentos">
						<div class="panel panel-default"
							jsf:rendered="#{arquivoCobrancaBean.entity != null and arquivoCobrancaBean.entity.conteudos.size() > 0}">
							<div class="panel-heading">
								<div class="panel-title">Cobranças</div>
							</div>
							<div class="panel-body fix-checkbox">
								<p:dataTable id="dataTable" var="object"
									value="#{arquivoCobrancaBean.entity.conteudos}" paginator="true" rows="10"
									paginatorPosition="bottom"
									emptyMessage="Nenhum registro encontrado"
									currentPageReportTemplate="({currentPage} de {totalPages})"
									paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
									lazy="false" draggableColumns="true" resizableColumns="true"
									tableStyleClass="table table-hover m-0">
									<p:column headerText="CNPJ Agente Financeiro">
										<h:outputLabel value="#{object.cnpjAgenteFinanceiro}"></h:outputLabel>
									</p:column>
									<p:column headerText="Valor">
										<h:outputText value="#{object.valor}">
												<f:convertNumber pattern="#,##0.00" locale="pt_BR" />
											</h:outputText>
									</p:column>
									
									<p:column headerText="Linha Digitável">
										<label>#{object.linhaDigitavel}</label>
									</p:column>
									<p:column headerText="Data Vencimento">
										<h:outputText value="#{object.dataVencimento}">
												<f:convertDateTime type="date"/>
										</h:outputText>
									</p:column>
									<p:column headerText="Quantidade Registros">
										<h:outputText value="#{object.totalRegistros}"/>
									</p:column>
									<p:column headerText="Válido">
										<h:outputLabel value="#{object.valida eq 'S' ? 'Sim' : 'Não' }"/>
									</p:column>
									<p:column headerText="Mensagem">
										<h:outputLabel escape="false" value="#{object.descricaoErro}"/>
									</p:column>
								</p:dataTable>
							</div>
						</div>
					</div>
					<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/consultaapoio/list-arquivo-cobranca-ftp.xhtml" class="btn btn-default">Voltar</a>
												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
											</div>
										</div>					
								</div>
							</div>
						</div>
					</div>
	
	</ui:define>
	
</ui:composition>
