<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
		<f:metadata>
			<f:viewParam id="id" name="id" value="#{contratoBean.idToEdit}"/>
			<f:viewAction action="#{contratoBean.loadDetails()}"/>	
		</f:metadata>
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">
							<i class="mdi mdi-plus-circle" style="color: #23b195;" title="Contrato Integra+" jsf:rendered="#{contratoBean.entity.idProcessoB3 != null}"></i>
							<i class="mdi mdi-plus-circle" style="color: #458BC4;" title="Contrato Send" jsf:rendered="#{contratoBean.entity.idProcessoSENDB3 != null}"></i>
							<i class="mdi mdi-plus-circle" style="color: #{contratoBean.entity.integradora.cor}"
							   title="#{contratoBean.entity.integradora.descricao}" jsf:rendered="#{contratoBean.entity.integradora != null}"></i>
							Contrato
                        </h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Visualizar</h6>
									<h:messages id="messages" warnClass="alert alert-warning alert-dismissable" infoClass="alert alert-success alert-dismissable" errorClass="alert alert-danger alert-dismissable" />
									<form jsf:id="form" jsf:prependId="false" class="form">
										<ui:include src="/contrato/form-inputs.xhtml">
											<ui:param name="disabled" value="disabled"></ui:param>
											<ui:param name="bean" value="#{contratoBean}"></ui:param>	
										</ui:include>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
									<form jsf:id="formDetail" jsf:prependId="false" class="form">
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/contrato/list.xhtml" class="btn btn-default">Voltar</a>
												<span jsf:rendered="#{contratoBean.entity.situacao != 'ERRO'}">
													<a href="#{request.contextPath}/contrato/print.xhtml?id=#{contratoBean.entity.id}" 
														class="btn btn-default" target="_blank" >Imprimir</a>
												</span>
												<sec:authorize ifAnyGranted="SOLICITAR_DOCUMENTO">
													<button jsf:action="#{contratoBean.solicitarDocumento}" jsf:rendered="#{contratoBean.entity.situacao != 'ERRO'}"
														type="submit" jsf:id="btnSolicitarDocumento" class="btn btn-primary btn-cons">
														Solicitar Documento
													</button>
												</sec:authorize>
												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
	
</ui:composition>
