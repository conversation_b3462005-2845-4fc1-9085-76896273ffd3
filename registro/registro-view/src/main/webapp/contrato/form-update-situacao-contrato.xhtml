<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="scripts">
	    <script src="#{request.contextPath}/templates/assets/js/scripts_registro.js" type="text/javascript"></script>
	</ui:define>
	
	<ui:define name="content">
		<f:metadata>
			<f:viewParam id="id" name="id" value="#{ativarContratoBean.idToEdit}" />
			<f:viewAction action="#{ativarContratoBean.loadDetails()}" />
		</f:metadata>
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Alterar Situação do Contrato</h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
                                 	<h6 class="font-14 mt-4">Contrato Pendente</h6>
									<h:messages id="messages" warnClass="alert alert-warning alert-dismissable" infoClass="alert alert-success alert-dismissable" errorClass="alert alert-danger alert-dismissable" />
									<form jsf:id="form" jsf:prependId="false" class="form">
										<div class="row">
											<div class="col-lg-12">
												<div class="form-group form-group form-group-default">
													<label>Nro Registro Eletrônico</label>
													<input type="text" jsf:value="#{ativarContratoBean.entity.numeroRegistroEletronico}" 
														class="form-control" disabled="disabled"/>
												</div>
											</div>
											<ui:repeat value="#{ativarContratoBean.entity.veiculos}" var="v">
												<div class="col-lg-12">
													<div class="form-group form-group form-group-default required">
														<label>CHASSI</label>
														<input jsf:id="chassi" type="text" maxlength="20"
															   jsf:value="#{v.numeroChassi}" required="true" jsf:required="true"
															   jsf:label="CHASSI" class="form-control chassi" disabled="disabled">
														</input>
													</div>
												</div>
											</ui:repeat>
										</div>
										<div class="row">
											<div class="col-lg-12" >
												<div class="form-group form-group form-group-default required">
													<label>Situação do Contrato</label>
													<select jsf:id="situacaoContrato" jsf:value="#{ativarContratoBean.situacaoContrato}"
															class="form-control full-width select2" jsf:label="Situação do Contrato"
															jsf:required="#{true}" required="required">
														<f:selectItem itemLabel="Selecione"/>
														<f:selectItems value="#{helperBean.situacoesAlteracao}" var="i" itemValue="#{i}"
																	   itemLabel="#{i.descricao}" />
													</select>
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/contrato/list-ativar.xhtml"
													class="btn btn-default">Voltar</a>
													<button type="submit" jsf:action="#{ativarContratoBean.atualizarSituacao}"
														class="btn btn-primary btn-cons">Alterar Situação</button>
												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
	
</ui:composition>
