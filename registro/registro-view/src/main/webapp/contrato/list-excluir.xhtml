<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Excluir Contrato</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Número do Registro Eletrônico</label>
													<input jsf:id="numeroRegistroEletronico" type="text"
														   jsf:value="#{excluirContratoBean.filter.numeroRegistroEletronico}"
														   class="form-control first integer"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Número do Contrato</label>
													<input jsf:id="numeroContrato" type="text" 
														jsf:value="#{excluirContratoBean.filter.numeroContrato}" class="form-control"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>CPF/CNPJ do Devedor</label>
													<input jsf:id="cpfCnpjDevedorFinanciado" type="text"
														   jsf:value="#{excluirContratoBean.filter.cpfCnpjDevedorFinanciado}"
														   jsf:validator="cpfCnpjValidator" class="form-control doc"/>
												</div>
											</div>
										</div>
										<div class="row">
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Gravame</label>
													<input jsf:id="numeroGravame" type="text" maxlength="8"
														   jsf:value="#{excluirContratoBean.filter.numeroGravame}"
														   class="form-control naoCola"
														   onkeypress="return /^-?[0-9]*$/.test(this.value+event.key)"/>
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Chassi</label>
													<input jsf:id="chassi" type="text" class="form-control chassi"
														   jsf:value="#{excluirContratoBean.filter.chassi}" />
												</div>
											</div>
											<div class="col-lg-4">
												<div class="form-group form-group-default">
													<label>Placa</label>
													<input jsf:id="placa" type="text" 
														jsf:value="#{excluirContratoBean.filter.placa}" class="form-control"/>
												</div>
											</div>
										</div>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/contrato/list-ativar.xhtml" class="btn btn-default">Limpar</a>
												<button type="submit" class="btn btn-primary btn-cons" jsf:action="#{excluirContratoBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									</form>
								</div>
							</div>
						</div>
						<div class="row" jsf:rendered="#{excluirContratoBean.list != null}">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<div style="min-height: 30px;">
										<div style="float: right; padding: 0;">
											Resultados por página
											<select jsf:id="registros" jsf:value="#{excluirContratoBean.size}" size="1">
												<f:selectItem itemLabel="10" itemValue="10" />
												<f:selectItem itemLabel="25" itemValue="25" />
												<f:selectItem itemLabel="50" itemValue="50" />
												<f:selectItem itemLabel="100" itemValue="100" />
												<f:ajax execute="@this" render="formDataTable" listener="#{excluirContratoBean.search}" onevent="function(data){$.masks();}" />
											</select>
										</div>
									</div>
									<p:dataTable id="dataTable" var="object" value="#{excluirContratoBean.list}" paginator="true" rows="#{excluirContratoBean.size}" paginatorPosition="bottom" emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="({currentPage} de {totalPages})" paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}" lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover m-0">
										<p:column headerText="Registro">
											#{object.numeroRegistroEletronico}
										</p:column>
										<p:column headerText="Contrato">
											#{object.numeroContrato}
										</p:column>
										<p:column headerText="Devedor">
											<h:outputText value="#{object.cpfCnpjDevedorFinanciado}" title="#{object.nomeDevedorFinanciado}">
												<f:converter converterId="cpfCnpjConverter" />
											</h:outputText>
										</p:column>
										<p:column headerText="Gravame">
											<ui:repeat var="v" value="#{object.veiculosLimitados}">
												#{v.numeroGravame} <br />
											</ui:repeat>
										</p:column>
										<p:column headerText="Chassi">
											<ui:repeat var="v" value="#{object.veiculosLimitados}">
												#{v.numeroChassi} <br />
											</ui:repeat>
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<h:commandLink styleClass="btn btn-link" title="Excluir"
														   onclick="return confirm('Confirma a exclusão?')"
														   action="#{excluirContratoBean.excluir(object)}">
												<span class="fa fa-remove" />
											</h:commandLink>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>
