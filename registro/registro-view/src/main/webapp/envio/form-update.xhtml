<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="scripts">
	    <script src="#{request.contextPath}/templates/assets/js/scripts_registro.js" type="text/javascript"></script>
	</ui:define>
	
	<ui:define name="content">
		<f:metadata>
			<f:viewParam id="id" name="id" value="#{envioContratoBean.idToEdit}" />
			<f:viewAction action="#{envioContratoBean.loadDetails()}" />
		</f:metadata>
		
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">
							<i class="mdi mdi-plus-circle" style="color: #23b195;" title="Contrato Integra+" jsf:rendered="#{envioContratoBean.entity.idProcessoB3 != null}"></i>
							<i class="mdi mdi-plus-circle" style="color: #458BC4;" title="Contrato Send" jsf:rendered="#{envioContratoBean.entity.idProcessoSENDB3 != null}"></i>
							<i class="mdi mdi-plus-circle" style="color: #{envioContratoBean.entity.integradora.cor}"
							   title="#{envioContratoBean.entity.integradora.descricao}" jsf:rendered="#{envioContratoBean.entity.integradora != null}"></i>
							Contrato
                        </h4>
                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pendência</h6>
									<h:messages id="messages" warnClass="alert alert-warning alert-dismissable" infoClass="alert alert-success alert-dismissable" errorClass="alert alert-danger alert-dismissable" />
									<form jsf:id="form" jsf:prependId="false" class="form">
										<ui:include src="/contrato/form-inputs.xhtml">
											<ui:param name="bean" value="#{envioContratoBean}"></ui:param>
											<ui:param name="aditivo" value="#{envioContratoBean.entity.tipoContrato != 'CONTRATO_PRINCIPAL'}"></ui:param>	
										</ui:include>
										<div class="row text-center">
											<div class="col-lg-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/envio/list.xhtml"
													class="btn btn-default">Voltar</a>
												<sec:authorize ifAllGranted="REGISTRAR_CONTRATO">
													<button type="submit" jsf:action="#{envioContratoBean.send}" 
														jsf:rendered="#{envioContratoBean.entity.dataConclusaoDETRAN == null}"
														class="btn btn-primary btn-cons">Enviar</button>
												</sec:authorize>	
												<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
											</div>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
	
</ui:composition>
