<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:h="http://xmlns.jcp.org/jsf/html"
                xmlns:f="http://xmlns.jcp.org/jsf/core"
                xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
                xmlns:p="http://primefaces.org/ui"
                xmlns:jsf="http://xmlns.jcp.org/jsf"
                template="/templates/blank.xhtml">
    <ui:define name="header">
        <link href="#{request.contextPath}/templates/assets/css/map/jqvmap.css" media="screen" rel="stylesheet"
              type="text/css"/>
        <link href="#{request.contextPath}/templates/assets/css/map/custom_map.css" media="screen" rel="stylesheet"
              type="text/css"/>

        <script type="text/javascript" src="#{request.contextPath}/templates/assets/js/map/jquery.vmap.js"
                charset="utf-8"></script>
        <script type="text/javascript" src="#{request.contextPath}/templates/assets/js/map/jquery.vmap.brazil.js"
                charset="utf-8"></script>
        <script type="text/javascript" src="#{request.contextPath}/templates/assets/js/map/custom_map.js"></script>

        <style>

            .input[type=checkbox] {
                margin-left: 5px;
                height: 0;
                width: 0;
                visibility: hidden;
            }

            .label {
                cursor: pointer;
                text-indent: -9999px;
                width: 50px;
                height: 25px;
                background: grey;
                display: block;
                border-radius: 100px;
                position: relative;
            }

            .label:after {
                content: '';
                position: absolute;
                top: 5px;
                left: 5px;
                width: 15px;
                height: 15px;
                background: #fff;
                border-radius: 90px;
                transition: 0.3s;
            }

            .input:checked + label {
                background: #48a1f1;
            }

            .input:checked + label:after {
                left: calc(100% - 5px);
                transform: translateX(-100%);
            }

        </style>


    </ui:define>

    <ui:define name="content">

        <f:metadata>
            <f:viewAction action="#{dashboardBean.initObservatorio()}" />
        </f:metadata>

        <div class="row">
            <div class="col-sm-12">
                <h4 class="header-title m-t-0 m-b-20">Observatório de Credenciamentos</h4>
            </div>
            <div class="col-sm-12">
                <h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
                            infoClass="alert alert-success alert-dismissable"
                            errorClass="alert alert-danger alert-dismissable"/>
            </div>
        </div>
        <div id="tab-credenciamento">
            <form jsf:id="formObservatorio">
                <div class="row justify-content-center">
                    <div class="col-lg-12">
                        <div class="form-group form-group-default form-group-default-select2">
                            <label>Financeira</label>
                            <select jsf:required="#{true}" jsf:id="financeiraObjeto"
                                    jsf:value="#{dashboardBean.financeiraObjeto}"
                                    jsf:label="Financeira"
                                    class="form-control full-width select2" size="1">
                                <f:selectItem itemLabel="Selecione"/>
                                <f:selectItems value="#{dashboardBean.financeirasObservatorio}" var="i"
                                               itemValue="#{i}" itemLabel="#{i.documento} - #{i.nome}"/>
                                <f:converter converterId="financeiraConverter"/>
                                <f:ajax execute="@this" update="tableObservatorio"/>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 d-flex justify-content-center">

                        <button type="button" jsf:action="#{dashboardBean.getObservatorioByFinanceira}"
                                class="btn btn-primary btn-cons">
                            Pesquisar
                            <f:ajax execute="@form" render="tableObservatorio"/>
                        </button>
                        <a href="javascript:window.location.reload()" class="btn btn-cons btn-default">Limpar</a>
                    </div>
                </div>
                <br>
                </br>
                <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                <h:panelGroup id="tableObservatorio">
                    <h:panelGroup rendered="#{dashboardBean.financeirasObservatorio ne null}">
                        <div class="row justify-content-center" id="situacaoCredenciamento">
                            <ui:include src="/mapa/dashboard-mapa.xhtml"/>
                        </div>
                        <div class="row justify-content-center mt-4 mb-4">
                            <div class="col-sm-10">
                                <div class="card-box">
                                    <h6 class="m-t-0 text-success">Situação do Credenciamento no DETRAN</h6>
                                    <div class="table-responsive">
                                        <table class="table table-hover mails m-0 table table-actions-bar">
                                            <ui:repeat var="object" value="#{dashboardBean.listarFinanceiras()}"
                                                       size="#{dashboardBean.sizeOfPage()}">
                                                <thead>
                                                <tr>
                                                    <th>#{object.nome}</th>
                                                    <th>Situação</th>
                                                    <th>Fim do Credenciamento</th>
                                                    <th>Dias Restantes</th>
                                                </tr>
                                                </thead>
                                                <ui:repeat var="o" value="#{object.situacoesFinanceiraEstado}">
                                                    <tbody>
                                                    <tr class="m-t-0">
                                                        <td>DETRAN #{o.uf}</td> <!--NOME/ESTADO-->
                                                        <td>#{o.situacaoFinanceira == 'S' ? 'Ativo' : 'Inativo'}</td> <!--SITUAÇÃO-->
                                                        <td> <!--CREDENCIAMENTO-->
                                                            <span jsf:rendered="#{!dashboardBean.credenciamentoVitalicio(o.uf)}"
                                                                  jsf:style="color: #{dashboardBean.getColorDiasRestantes(o.dataFimCredenciamentoDetran)}">
                                                                <h:outputText value="#{o.dataFimCredenciamentoDetran}">
                                                                    <f:convertDateTime pattern="dd/MM/yyyy"/>
                                                                </h:outputText>
                                                            </span>
                                                            <span jsf:rendered="#{dashboardBean.credenciamentoVitalicio(o.uf)}"
                                                                  style="color: #3ebc4b">
                                                               Credenciamento sem prazo de validade
                                                            </span>
                                                            <span jsf:rendered="#{o.dataInicioCredenciamentoDetran == null}"
                                                                  style="color: red">
                                                                Sem credenciamento
                                                            </span>
                                                        </td>
                                                        <td> <!--DIAS RESTANTES-->
                                                            <span jsf:rendered="#{!dashboardBean.credenciamentoVitalicio(o.uf)}">
                                                                #{dashboardBean.getStringDiasRestantes(o.dataFimCredenciamentoDetran)}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    </tbody>
                                                </ui:repeat>
                                            </ui:repeat>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </h:panelGroup>
                </h:panelGroup>
                <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
            </form>
        </div>
    </ui:define>

</ui:composition>
