function validarDataContrato() {
	var dataContrato = $("#dataContrato_input").val().replace(/(\d{2})\/(\d{2})\/(\d{4})/, "$2/$1/$3");
	var dataLiberacaoCredito = $("#dataLiberacaoCredito_input").val().replace(/(\d{2})\/(\d{2})\/(\d{4})/, "$2/$1/$3");
	if((new Date(dataContrato).getTime() > new Date(dataLiberacaoCredito).getTime())) {
		alert('A Data da Lib. do Crédito deve ser maior ou igual do que a Data do Contrato');
		$("#dataLiberacaoCredito_input").val("");
		$("#dataLiberacaoCredito_input").focus();
	}
}

function validarDataLiberacaoCredito() {
	var dataInicio = $("#dataContrato_input").val().replace(/(\d{2})\/(\d{2})\/(\d{4})/, "$2/$1/$3");
	var dataFim = $("#dataLiberacaoCredito_input").val().replace(/(\d{2})\/(\d{2})\/(\d{4})/, "$2/$1/$3");
	if((new Date(dataInicio).getTime() > new Date(dataFim).getTime())) {
		alert('A Data da Lib. do Crédito deve ser maior ou igual do que a Data do Contrato');
		$("#dataLiberacaoCredito_input").val("");
		$("#dataLiberacaoCredito_input").focus();
	}
}

function validarDataVencimentoUltimaParcela() {
	var dataInicio = $("#dataVencimentoPrimeiraParcela_input").val().replace(/(\d{2})\/(\d{2})\/(\d{4})/, "$2/$1/$3");
	var dataFim = $("#dataVencimentoUltimaParcela_input").val().replace(/(\d{2})\/(\d{2})\/(\d{4})/, "$2/$1/$3");
	if((new Date(dataInicio).getTime() > new Date(dataFim).getTime())) {
		alert('A Data de Venc. da última parcela deve ser maior que a Data de Venc. da primeira parcela');
		$("#dataVencimentoUltimaParcela_input").val("");
		$("#dataVencimentoUltimaParcela_input").focus();
	}
}

$.ufRegistro = function() {
	$('#ufRegistro').change(function() {
		definirObrigatoriedadePorEstado();
	});
	definirObrigatoriedadePorEstado();
}

$(document).ready(function() {
	$.ufRegistro();
});

function definirObrigatoriedadePorEstado() {
	if($('#ufRegistro').val() == "PR") {
		$('#numeroEnderecoDevedor').parent().addClass('required');
		$('#numeroEnderecoDevedor').attr('required', true);
	} else {
		$('#numeroEnderecoDevedor').parent().removeClass('required');
		$('#numeroEnderecoDevedor').attr('required', false);
	}
	
	if($('#ufRegistro').val() == "AP") {
		$('.tipoVeiculo').parent().addClass('required');
		$('.tipoVeiculo').attr('required', true);
	} else {
		$('.tipoVeiculo').parent().removeClass('required');
		$('.tipoVeiculo').attr('required', false);
	}
	if($('#ufRegistro').val() == "PI") {
		$('#spanAddOutroVeiculo').hide();
	} else {
		$('#spanAddOutroVeiculo').show();
	}
}
