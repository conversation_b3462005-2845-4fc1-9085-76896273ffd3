

/* Metis Menu css */
.metismenu {
  padding: 0;
  list-style: none;
  margin: 0;

  ul {
    padding: 0;
    margin: 0;

    li {
      list-style: none;
    }
  }
}

.nav-second-level li a, .nav-thrid-level li a {
  padding: 8px 20px 8px 10px;
  color: rgba($dark,0.7);
  display: block;
  font-weight: 500;
  position: relative;

  &:focus {
    background-color: $bg-leftbar;
    color: $dark;
  }

  &:hover {
    background-color: $bg-leftbar;
    color: $dark;
  }
}

.nav-second-level > li > a {
  padding-left: 58px;
}

.nav-second-level li.active {
  > a {
    color: $dark;
    background-color: $bg-leftbar;
  }
}

.nav-third-level > li > a {
  padding-left: 68px;
}

.nav-third-level li.active {
  > a {
    color: $dark;
  }
}


// User detail
.user-details {
  min-height: 80px;
  padding: 20px;
  position: relative;

  img {
    position: relative;
    z-index: 9999;
    height: 48px;
    width: 48px;
  }

  .user-info {
    color: $dark;
    margin-left: 60px;
    position: relative;
    z-index: 99999;

    p {
      margin-bottom: 0;
      font-size: 13px;
    }

    a {
      color: $dark;
      display: block;
      font-weight: 600;
      padding-top: 5px;
    }
  }
}

.topbar {
  top: 0;
  z-index: 999;
  position: sticky;
  width: 90%;
  background: darken($dark,10%);
  box-shadow: 0 0 18px 0 rgba(0, 0, 0, 0.3);

  .topbar-left {
    float: left;
    padding-left: 20px;
    height: 70px;
    position: relative;
    width: 240px;
    z-index: 999;

    .logo {
      line-height: 70px;

      i {
        display: none;
      }

      img {
        height: 26px;
      }

    }
  }
}

.navbar-custom {
  border-radius: 0;
  margin-bottom: 0;
  padding: 0 10px 0 0;
  margin-left: 240px;
  min-height: 70px;

  .nav-link {
    padding: 0;
    line-height: 70px;
    color: rgba($white,0.6);
  }

  .dropdown-toggle {
    &:after {
      content: initial;
    }
  }

  .menu-left {
    overflow: hidden;
  }

  .topbar-right-menu {
    li {
      float: left;
    }
  }
}

.logo {
  color: $dark !important;
  font-size: 20px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;

  span {
    span {
      color: $custom;
    }
  }
}

.user-box {
  text-align: center;
  padding: 30px 0 20px 0;

  .user-img {
    position: relative;
    height: 88px;
    width: 88px;
    margin: 0 auto;

  }

  h5{
    a{
      color: $dark;
    }
  }
}


.side-menu {
  position: absolute;
  background-color: $white;
  width: 240px;
  top: 70px;
  -moz-transition: margin-left 0.3s;
  -o-transition: margin-left 0.3s;
  -webkit-transition: margin-left 0.3s;
  transition: margin-left 0.3s;
  background-repeat: repeat;
  z-index: 99;
}

.enlarged .side-menu {
  position: absolute;
}

.content-page {
  margin-left: 240px;
  overflow: hidden;
  border-left: 7px solid $bg-body;

  .content {
    padding: 0 10px 50px 10px;
    margin-top: 20px;
    min-height: 1040px;
    position: relative;
  }
}


.button-menu-mobile {
  border: none;
  color: rgba($white,0.7);
  display: inline-block;
  height: 70px;
  width: 60px;
  background-color: transparent;
  font-size: 24px;
  z-index: 999;
  position: relative;
  cursor: pointer;
}

#sidebar-menu > ul > li > a {
  color: rgba($dark,0.8);
  display: block;
  padding: 12px 20px;
  margin: 2px 0;
  font-size: 14px;
  position: relative;

  &:hover,&:focus,&:active {
    color: $dark;
    text-decoration: none;
    background-color: darken($bg-leftbar,4%);
  }
}

#sidebar-menu > ul > li > a > span {
  vertical-align: middle;
}

#sidebar-menu {
  padding-top: 10px;
  .menu-arrow {
    -webkit-transition: -webkit-transform .15s;
    -o-transition: -o-transform .15s;
    transition: transform .15s;
    position: absolute;
    right: 20px;
    display: inline-block;
    font-family: 'Material Design Icons';
    text-rendering: auto;
    line-height: 28px;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);

    &:before {
      content: "\F142";
    }
  }
  .badge,.label {
    margin-top: 4px;
  }

  li.active {
    .menu-arrow {
      -ms-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      -o-transform: rotate(90deg);
      transform: rotate(90deg);
    }
  }
  ul {
    li {
      a{
        i{
          display: inline-block;
          font-size: 18px;
          line-height: 17px;
          margin: 0 10px 0 3px;
          text-align: center;
          vertical-align: middle;
          width: 20px;
        }

        .drop-arrow {
          float: right;

          i{
            margin-right: 0;
          }
        }
      }
    }
  }
}

#sidebar-menu > ul > li > a.active {
  color: $dark !important;
  background-color: darken($bg-leftbar,4%);
}

//
//.metisMenu.nav > li.active > a {
//  color: $custom;
//
//  li.active {
//    a{
//      color: $custom;
//    }
//  }
//}

.menu-title {
  padding: 12px 20px !important;
  letter-spacing: .05em;
  pointer-events: none;
  cursor: default;
  font-size: 11px;
  text-transform: uppercase;
  color: $muted;
  font-family: $font-secondary;
}

.enlarged {
  .slimScrollDiv,.slimscroll-menu {
    overflow: inherit !important;
  }
  .slimScrollBar {
    visibility: hidden;
  }
}
.enlarged #wrapper {
  .navbar-custom {
    margin-left: 70px;
  }
  #sidebar-menu {
    .menu-title, .menu-arrow, .label, .badge {
      display: none !important;
    }
    .collapse.in {
      display: none !important;
    }
    .nav.collapse {
      height: inherit !important;
    }
    ul {
      ul {
        margin-top: -2px;
        padding-bottom: 5px;
        padding-top: 5px;
        z-index: 9999;
        background-color: $light;
        height: auto !important;
      }
    }
  }
  .left.side-menu {
    width: 70px;
    z-index: 5;

    #sidebar-menu > ul > li > a {
      padding: 15px 20px;
      min-height: 56px;
      &:hover,&:active,&:focus {
        color: $dark !important;
        background-color: darken($bg-leftbar, 4%);
      }

      i {
        font-size: 18px;
        margin-right: 20px !important;
      }
    }
    .label {
        position: absolute;
        top: 5px;
        left: 35px;
        text-indent: 0;
        display: block !important;
        padding: .2em .6em .3em !important;
    }
    #sidebar-menu {
      ul > li {
        position: relative;
        white-space: nowrap;
        &:hover > a {
          position: relative;
          width: 260px;
          color: $dark !important;
          background-color: darken($bg-leftbar, 4%);
        }
        &:hover > ul {
          display: block;
          left: 70px;
          position: absolute;
          width: 190px;
          a {
            box-shadow: none;
            padding: 8px 20px;
            position: relative;
            width: 190px;
            z-index: 6;

            &:hover {
              color: $dark;
            }
          }
        }
        &:hover {
          a {
            span {
              display: inline;
            }
          }
        }
      }

      ul {
        ul {
          li {
            &:hover > ul {
              display: block;
              left: 190px;
              margin-top: -36px;
              position: absolute;
              width: 190px;
            }
          }
          li > a {
            span.pull-right {
              -ms-transform: rotate(270deg);
              -webkit-transform: rotate(270deg);
              position: absolute;
              right: 20px;
              top: 12px;
              transform: rotate(270deg);
            }
          }
          li.active {
            a{
              color: $dark;
            }
          }
        }
      }
      ul > li > a {
        span {
          display: none;
          padding-left: 10px;
        }
      }
    }
    .user-details {
      display: none;
    }
  }
  .content-page {
    margin-left: 70px;
  }
  .footer {
    left: 70px;
  }
  .topbar {
    .topbar-left {
      width: 70px !important;
      .logo {
        span {
          display: none;
          opacity: 0;
        }
        i {
          display: block;
          line-height: 70px;
          color: $custom !important;
        }
      }
    }
  }
  #sidebar-menu > ul > li {
    &:hover > a.open {
      :after {
        display: none;
      }
    }
    &:hover > a.active {
      :after {
        display: none;
      }
    }
  }
}

#wrapper.right-bar-enabled {
  .right-bar {
    right: 0;
  }
}


/* Search */
.app-search {
  position: relative;
  padding-top: 18px;

  a {
    position: absolute;
    top: 18px;
    left: 220px;
    display: block;
    height: 34px;
    line-height: 34px;
    width: 34px;
    text-align: center;
    color: rgba($white, 0.5);

    &:hover {
      color: $white;
    }
  }

  .form-control,
  .form-control:focus {
    border: 1px solid rgba($white, 0.3);
    font-size: 13px;
    height: 34px;
    color: $white;
    padding-left: 20px;
    padding-right: 40px;
    background: transparent;
    box-shadow: none;
    border-radius: 30px;
    width: 200px;
  }
}

.app-search input {
  &::-webkit-input-placeholder {
    color: rgba($white,70%);
  }
  &:-moz-placeholder {
    color: rgba($white,70%);
  }
  &::-moz-placeholder {
    color: rgba($white,70%);
  }
  &:-ms-input-placeholder {
    color: rgba($white,70%);
  }
}


/* Page titles */
.page-title {
  font-size: 18px;
  margin-bottom: 0;
  margin-top: 2px;
  font-weight: 600;
}



/* Footer */
.footer {
  border-top: 2px solid #f5f5f5;
  bottom: 0;
  left: 0;
  padding: 14px 20px;
  position: absolute;
  right: 0;
}


/* Notification */
.notification-wrapper {
  max-height: 190px;
}

.notification-list {
  margin-left: 0 !important;

  .noti-title {
    background-color: $white !important;
    padding: 7px 20px;
  }

  .noti-icon {
    font-size: 20px;
    padding: 0 15px;
    vertical-align: middle;
    color: rgba($white,0.8);
  }
  .noti-icon-badge {
    display: inline-block;
    position: absolute;
    top: 14px;
    right: 8px;
  }
  .notify-item {
    padding: 10px 20px;

    .notify-icon {
      float: left;
      height: 36px;
      width: 36px;
      line-height: 36px;
      text-align: center;
      margin-right: 10px;
      border-radius: 50%;
      color: $white;
    }
    .notify-details {
      margin-bottom: 0;
      overflow: hidden;
      margin-left: 45px;
      text-overflow: ellipsis;
      white-space: nowrap;
      b {
        font-weight: 500;
      }
      small {
        display: block;
      }
      span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px;
      }
    }
    .user-msg {
      margin-left: 45px;
      white-space: normal;
      line-height: 16px;
    }
  }
  .notify-all {
  }
  .profile-dropdown {
    .notify-item {
      padding: 7px 20px;
    }
  }
}

.profile-dropdown {
  width: 170px;
  i {
    vertical-align: middle;
    margin-right: 5px;
  }
  span {
    vertical-align: middle;
  }
}

.nav-user {
  padding: 0 12px !important;

  img {
    height:32px;
    width: 32px;
  }
}




@media (max-width: 768px) {
  #wrapper {
    width: 100% !important;
  }
  .topbar,.side-menu {
    position: fixed;
  }
  .side-menu {
    overflow: auto;
    bottom: 0;
    padding-bottom: 30px;
    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  }
  body {
    overflow-x: hidden;
  }

  .topbar-left {
    width: 70px !important;

    span {
      display: none !important;
    }
    i {
      display: block !important;
      line-height: 70px !important;
    }
  }

  .navbar-custom {
    margin-left: 70px !important;
  }

  .topbar .topbar-left {
    height: 70px;
  }

  .navbar-nav.navbar-right {
    float: right;
  }
  .content-page {
    margin-left: 0 !important;
    margin-top: 70px;
    border-left: none;
  }
  .enlarged .left.side-menu {
    margin-left: -70px;
  }
  .footer {
    left: 0 !important;
  }

  .mobile-sidebar {
    left: 0;
  }

  .mobile-content {
    left: 250px;
    right: -250px;
  }

  .dataTables_wrapper {
    .col-xs-6 {
      width: 100%;
      text-align: left;
    }
  }

  div#datatable-buttons_info {
    float: none;
  }
  .ms-container {
    width: 100%;
  }
  .m-t-sm-50 {
    margin-top: 50px !important;
  }
}

@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    background-color: $white;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
    left: auto;
    position: absolute;
    right: 0;

    li {
      display: block;
    }
  }
  .navbar-nav {
    margin: 0;
    display: inline-block;

    li {
      display: inline-block;
      line-height: 1px;
    }
  }
  .dropdown-lg {
    width: 200px !important;
  }
  .user-box {
    float: right;
  }
  .dataTables_length {
    float: none;
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .side-menu {
    z-index: 10 !important;
  }

  .navbar-custom {
    margin-left: 0 !important;
  }

  .hide-phone {
    display: none !important;
  }
}



@media (max-width: 419px) {
  .hidden-xxs {
    display: none;
  }
  .topbar-left {
    width: 70px !important;
  }
  .logo {
    .icon-c-logo {
      display: inline-block !important;
      line-height: 58px !important;
    }
    span {
      display: none !important;
    }
  }
  .content-page {
    margin-left: 70px;
  }
  .forced {
    .side-menu.left {
      box-shadow: 0 12px 12px rgba(0, 0, 0, 0.1);
      //position: absolute;
    }
  }
  .enlarged {
    .side-menu.left {
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
    }
  }
  .page-title {
    font-size: 15px;
    max-width: 250px;
    white-space: nowrap;
  }
  .navbar-default {
    padding: 0;
    .navbar-left {
      padding-left: 0 !important;

      li {
        padding: 0 5px;
      }
    }
  }
  .topbar-left {
    display: none;
  }

  .editable-responsive {
    overflow-x: auto;
  }

  .dropdown-lg {
    width: 200px !important;
  }
  .user-list .user-list-item .icon,.user-list .user-list-item .avatar {
    display: none;
  }
  .user-list .user-list-item .user-desc {
    margin-left: 0;
  }
  .footer {
    text-align: center;
  }

}


