<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:jsf="http://xmlns.jcp.org/jsf"
	xmlns:sec="http://www.springframework.org/security/tags"
	template="/templates/blank.xhtml">

	<ui:define name="content">
        <!-- Start content -->
        <div class="content">
            <div class="container-fluid">

                <div class="row">
                    <div class="col-sm-12">
                        <h4 class="header-title">Impostos</h4>

                        <div class="row">
                            <div class="col-lg-12">
								<div class="m-b-20">
	                                 <h6 class="font-14 mt-4">Pesquisar</h6>
									<form jsf:id="form" jsf:prependId="false">
										<h:messages id="messages" warnClass="alert alert-warning alert-dismissable"
											infoClass="alert alert-success alert-dismissable"
											errorClass="alert alert-danger alert-dismissable" />
										<div class="row">
											<div class="col-lg-3">
												<div class="form-group form-group form-group-default">
													<label>Imposto</label>
													<select id="imposto" jsf:value="#{impostoBean.filter.tipo}" 
														class="form-control full-width select2" jsf:label="Tipo de Imposto">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.tiposImposto}" var="i" itemValue="#{i}" itemLabel="#{i.descricao}" />
													</select>
												</div>			
											</div>
											<div class="col-lg-3">
												<div class="form-group form-group form-group-default">
													<label>Recorrência</label>
													<select id="recorrenciaImposto" jsf:value="#{impostoBean.filter.recorrencia}" 
														class="form-control full-width select2" jsf:label="Recorrência">
														<f:selectItem itemLabel="Selecione" />
														<f:selectItems value="#{helperBean.recorrenciasImposto}" var="i" itemValue="#{i}" itemLabel="#{i.descricao}" />
													</select>
												</div>			
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group form-group-default">
													<label>Início</label>
													<p:calendar  id="dataInicioVigencia" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
														value="#{impostoBean.filter.dataInicio}" pattern="dd/MM/yyyy" mask="true" label="Início da Vigência" 
														disabled="#{disabled eq 'disabled'}">
													</p:calendar>
												</div>
											</div>
											<div class="col-lg-2">
												<div class="form-group form-group form-group-default">
													<label>Fim</label>
													<p:calendar  id="dataFimVigencia" styleClass="form-control" locale="pt_BR" navigator="true" yearRange="c-10:c+10"
														value="#{impostoBean.filter.dataFim}" pattern="dd/MM/yyyy" mask="true" label="Final da Vigência" 
														disabled="#{disabled eq 'disabled'}">
													</p:calendar>
												</div>
											</div>		
											
										</div>
										<div class="row text-center">
											<div class="col-md-12">
												<hr class="buttons" />
												<a href="#{request.contextPath}/imposto/list.xhtml"
													class="btn btn-default">Limpar</a>
													<a href="#{request.contextPath}/imposto/form-add.xhtml"
														class="btn btn-primary btn-cons">Novo</a>
												<button type="submit" class="btn btn-primary btn-cons"
													jsf:action="#{impostoBean.search}">Pesquisar</button>
											</div>
										</div>
										<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
									</form>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="table-responsive">
								<form jsf:id="formDataTable" jsf:prependId="false">
									<p:dataTable id="dataTable" var="object" value="#{impostoBean.list}"
										paginator="true" rows="10" paginatorPosition="bottom"
										emptyMessage="Nenhum registro encontrado"
										currentPageReportTemplate="({currentPage} de {totalPages})"
										paginatorTemplate="{FirstPageLink} {PreviousPageLink} {CurrentPageReport} {NextPageLink} {LastPageLink}"
										lazy="true" draggableColumns="true" resizableColumns="true"
										tableStyleClass="table table-hover mails m-0 table table-actions-bar">
										<p:column headerText="Imposto">
											<a href="#{request.contextPath}/imposto/form-detail.xhtml?id=#{object.id}">
												<h:outputText value="#{object.tipoImposto.descricao}" />
											</a>
										</p:column>
										<p:column headerText="Recorrência">
											<h:outputText value="#{object.recorrencia.descricao}" />
										</p:column>
										<p:column headerText="Período">
											<h:outputText value="#{object.periodo}" />
										</p:column>
										<p:column headerText="Percentual" styleClass="text-center">
											#{object.percentualImposto}
										</p:column>
										<p:column headerText="Ações" styleClass="text-center">
											<a class="btn btn-link" title="Visualizar"
												href="#{request.contextPath}/imposto/form-detail.xhtml?id=#{object.id}">
												<span class="fa fa-search" />
											</a>
											<sec:authorize ifAnyGranted="CADASTRAR_IMPOSTO">
											<a class="btn btn-link" title="Editar"
												href="#{request.contextPath}/imposto/form-update.xhtml?id=#{object.id}">
													<span class="fa fa-edit" />
											</a>
											</sec:authorize>
										</p:column>
									</p:dataTable>
									<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</ui:define>
</ui:composition>