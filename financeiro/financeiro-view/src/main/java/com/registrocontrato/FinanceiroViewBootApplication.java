package com.registrocontrato;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.registrocontrato.infra.application.BaseBootApplication;

@Configuration
@EnableScheduling
@SpringBootApplication
@EnableAsync
public class FinanceiroViewBootApplication extends BaseBootApplication {

	@Override
	public Class<?> getBootClass() {
		return FinanceiroViewBootApplication.class;
	}

}
