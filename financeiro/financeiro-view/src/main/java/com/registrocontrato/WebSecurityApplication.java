package com.registrocontrato;

import com.registrocontrato.infra.application.InvalidSession;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.validation.Cas20ServiceTicketValidator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.cas.ServiceProperties;
import org.springframework.security.cas.authentication.CasAssertionAuthenticationToken;
import org.springframework.security.cas.authentication.CasAuthenticationProvider;
import org.springframework.security.cas.web.CasAuthenticationEntryPoint;
import org.springframework.security.cas.web.CasAuthenticationFilter;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.security.web.session.HttpSessionEventPublisher;

import com.registrocontrato.commons.seguranca.CustomUserDetailsService;

@Configuration
@EnableWebSecurity
public class WebSecurityApplication extends WebSecurityConfigurerAdapter {

	private static final String LOGOUT = "/logout";

	private static final String LOGIN = "/login";

	@Value("${cas.server}")
	private String CAS;

	@Value("${cas.local.login}")
	private String LOGIN_CAS;

	private static final String ERROR_EXPIRED_XHTML = "/error/unauthorized.xhtml";

	@Bean
	public ServiceProperties serviceProperties() {
		ServiceProperties serviceProperties = new ServiceProperties();
		serviceProperties.setService(LOGIN_CAS);
		serviceProperties.setSendRenew(false);
		return serviceProperties;
	}

	@Bean
	public Cas20ServiceTicketValidator cas20ServiceTicketValidator() {
		Cas20ServiceTicketValidator cas20ServiceTicketValidator = new Cas20ServiceTicketValidator(CAS);
		return cas20ServiceTicketValidator;
	}

	@Bean
	public CasAuthenticationProvider casAuthenticationProvider() {
		CasAuthenticationProvider casAuthenticationProvider = new CasAuthenticationProvider();
		casAuthenticationProvider.setServiceProperties(serviceProperties());
		casAuthenticationProvider.setTicketValidator(cas20ServiceTicketValidator());
		casAuthenticationProvider.setAuthenticationUserDetailsService(customUserdetailService());
		casAuthenticationProvider.setKey("an_id_for_this_auth_provider_only");
		return casAuthenticationProvider;
	}

	@Bean
	public AuthenticationUserDetailsService<CasAssertionAuthenticationToken> customUserdetailService() {
		CustomUserDetailsService customUserdetailService = new CustomUserDetailsService();
		customUserdetailService.setEnableGroups(true);
		return customUserdetailService;
	}

	@Bean
	public CasAuthenticationFilter casAuthenticationFilter() throws Exception {
		CasAuthenticationFilter casAuthenticationFilter = new CasAuthenticationFilter();
		casAuthenticationFilter.setAuthenticationManager(authenticationManager());
		return casAuthenticationFilter;
	}

	@Bean
	public CasAuthenticationEntryPoint casAuthenticationEntryPoint() {
		CasAuthenticationEntryPoint casAuthenticationEntryPoint = new CasAuthenticationEntryPoint();
		casAuthenticationEntryPoint.setLoginUrl(CAS + LOGIN);
		casAuthenticationEntryPoint.setServiceProperties(serviceProperties());
		return casAuthenticationEntryPoint;
	}

	@Bean
	public LogoutFilter requestCasGlobalLogoutFilter() {
		LogoutFilter logoutFilter = new LogoutFilter(CAS + LOGOUT, new SecurityContextLogoutHandler());
		logoutFilter.setFilterProcessesUrl("/logout/cas");
		return logoutFilter;
	}

	@Bean
	public SingleSignOutFilter singleSignOutFilter() {
		SingleSignOutFilter singleSignOutFilter = new SingleSignOutFilter();
		singleSignOutFilter.setIgnoreInitConfiguration(true);
		singleSignOutFilter.setCasServerUrlPrefix(CAS + LOGOUT);
		return singleSignOutFilter;
	}

	@Override
	protected void configure(HttpSecurity http) throws Exception {
		http.addFilter(casAuthenticationFilter());
		http.exceptionHandling().authenticationEntryPoint(casAuthenticationEntryPoint());
		http.addFilterBefore(singleSignOutFilter(), CasAuthenticationFilter.class);
		http.addFilterBefore(requestCasGlobalLogoutFilter(), LogoutFilter.class);
		http.headers().frameOptions().sameOrigin();
		
		//APOIO
		http.authorizeRequests().antMatchers("/consultaapoio/list-cobranca-pr.xhtml").hasRole("CONSULTAR_COBRANCA_PR");
		http.authorizeRequests().antMatchers("/consultaapoio/list-contrato.xhtml").hasRole("CONSULTAR_CONTRATO_DETRAN");
		http.authorizeRequests().antMatchers("/consultaapoio/list-sequencial-sc.xhtml").hasRole("CONSULTAR_NUMERO_DETRAN");
		http.authorizeRequests().antMatchers("/consultaapoio/alterar-senha-sc.xhtml").hasRole("ALTERAR_SENHA_DETRAN");
		http.authorizeRequests().antMatchers("/consultaapoio/consulta-boleto.xhtml").hasRole("CONSULTAR_BOLETO_DETRAN");
		http.authorizeRequests().antMatchers("/consultaapoio/form-detail-arquivo-cobranca-ftp.xhtml").hasAnyRole("CONSULTAR_ARQUIVO_COBRANCA_SC, CONSULTAR_ARQUIVO_COBRANCA_FTP");
		http.authorizeRequests().antMatchers("/consultaapoio/list-arquivo-cobranca-ftp.xhtml").hasAnyRole("CONSULTAR_ARQUIVO_COBRANCA_SC", "CONSULTAR_ARQUIVO_COBRANCA_FTP");

		http.authorizeRequests().antMatchers("/credenciamento/list.xhtml").hasRole("CONSULTAR_CREDENCIAMENTO");
		http.authorizeRequests().antMatchers("/credenciamento/form-detail.xhtml").hasRole("CONSULTAR_CREDENCIAMENTO");
		http.authorizeRequests().antMatchers("/credenciamento/form**").hasRole("CADASTRAR_CREDENCIAMENTO");

		http.authorizeRequests().antMatchers("/cobranca/upload-categoria.xhtml").hasRole("UPLOAD_CATEGORIA_VEICULO_CONTRATO");
		
		http.authorizeRequests().antMatchers("/cobranca/list.xhtml").hasRole("CONSULTAR_COBRANCA");
		http.authorizeRequests().antMatchers("/cobranca/form-detail.xhtml").hasRole("CONSULTAR_COBRANCA");
		http.authorizeRequests().antMatchers("/cobranca/form**").hasAnyRole("CADASTRAR_COBRANCA");

		http.authorizeRequests().antMatchers("/desconto/list.xhtml").hasRole("CONSULTAR_CUPOM_DESCONTO");
		http.authorizeRequests().antMatchers("/desconto/form-detail.xhtml").hasRole("CONSULTAR_CUPOM_DESCONTO");
		http.authorizeRequests().antMatchers("/desconto/form**").hasAnyRole("CADASTRAR_CUPOM_DESCONTO");
		
		http.authorizeRequests().antMatchers("/imposto/**").hasAnyRole("CADASTRAR_IMPOSTO");
		
		
		//CONSULTA SITUACAO
		http.authorizeRequests().antMatchers("/consulta/consulta-situacao.xhtml").hasRole("CONSULTAR_SITUACAO");
		
		//IMPRIMIR CERTIDAO
		http.authorizeRequests().antMatchers("/certidao/list.xhtml").hasRole("IMPRIMIR_CERTIDAO");
		http.authorizeRequests().antMatchers("/certidao/print-complete.xhtml").hasRole("IMPRIMIR_CERTIDAO");
		http.authorizeRequests().antMatchers("/certidao/print-simple.xhtml").hasRole("IMPRIMIR_CERTIDAO");
		
		// DOC ARRECADACAO
		http.authorizeRequests().antMatchers("/documentoarrecadacao/list.xhtml").hasRole("CONSULTAR_DOCUMENTO_ARRECADACAO");
		http.authorizeRequests().antMatchers("/documentoarrecadacao/form-detail.xhtml").hasRole("CONSULTAR_DOCUMENTO_ARRECADACAO");
		http.authorizeRequests().antMatchers("/documentoarrecadacao/form**").hasAnyRole("CADASTRAR_DOCUMENTO_ARRECADACAO");

		//CONTA AZUL
		http.authorizeRequests().antMatchers("/contaazul/**").hasRole("CONTA_AZUL");
		
		//INTEGRA +
		http.authorizeRequests().antMatchers("/integramais/**").hasRole("INTEGRA_MAIS");
		
		// GASTOS MENSAIS
		http.authorizeRequests().antMatchers("/gastoMensal/list.xhtml").hasRole("CONSULTAR_GASTO_MENSAL");
		http.authorizeRequests().antMatchers("/gastoMensal/form-detail.xhtml").hasRole("CONSULTAR_GASTO_MENSAL");
		http.authorizeRequests().antMatchers("/gastoMensal/form**").hasAnyRole("CADASTRAR_GASTO_MENSAL");

		// DASHBOARD API BB
		http.authorizeRequests().antMatchers("/cobrancabb/list.xhtml").hasAnyRole("GERENCIAR_PAGAMENTOS_BB");
		http.authorizeRequests().antMatchers("/gastoMensal/form**").hasAnyRole("GERENCIAR_PAGAMENTOS_BB");

		// RELATÓRIOS
		http.authorizeRequests().antMatchers("/relatorio/list.xhtml").hasAnyRole("CONSULTAR_RELATORIO_PRODUCAO_FINANCEIRAS");
		http.authorizeRequests().antMatchers("/relatorioreembolso/list.xhtml").hasAnyRole("CONSULTAR_FINANCEIRAS_REEMBOLSO");

		http.authorizeRequests().anyRequest().authenticated();

		http.exceptionHandling().accessDeniedPage("/error/forbidden.xhtml");
			
		//estrategia para sessao expirada
		http.sessionManagement().invalidSessionUrl(ERROR_EXPIRED_XHTML);
		http.sessionManagement().invalidSessionStrategy(new InvalidSession(ERROR_EXPIRED_XHTML));
	}

	@Bean
	public HttpSessionEventPublisher httpSessionEventPublisher() {
	    return new HttpSessionEventPublisher();
	}
	
	@Override
	protected void configure(AuthenticationManagerBuilder auth) throws Exception {
		auth.authenticationProvider(casAuthenticationProvider());
	}

	@Override
	public void configure(WebSecurity web) throws Exception {
		web.ignoring().antMatchers("/javax.faces.resource/**")
		.antMatchers("/public/**")
		.antMatchers("/assets/**")
		.antMatchers("/qrcode/**")
		.antMatchers("/error/**")
		.antMatchers("/templates/**")
		.antMatchers(LOGOUT);
	}

}
