package com.registrocontrato.financeiro.service.detran.pr;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.registrocontrato.financeiro.service.DTO.MensagemRetornoDTO;
import com.registrocontrato.infra.email.Email;
import com.registrocontrato.infra.email.EnviaEmail;
import com.registrocontrato.infra.entity.Uf;
import com.registrocontrato.registro.entity.Cobranca;
import com.registrocontrato.registro.entity.MensagemRetorno;
import com.registrocontrato.registro.repository.MensagemRetornoRepository;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;

import javax.mail.internet.InternetAddress;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import java.io.InputStream;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public interface HandlerWsDetranClient {

	default void enviarEmailSuporte(EnviaEmail enviaEmail, String mensagem) {
		try {
			HashMap<Character, List<InternetAddress>> hash = new HashMap<Character, List<InternetAddress>>();
			List<InternetAddress> emails = new ArrayList<>();
			emails.add(new InternetAddress("<EMAIL>"));
			hash.put(Email.TIPO_PARA, emails);
			Email email = new Email(enviaEmail);
			email.enviarEmail("Erro no envio de Contrato", hash, mensagem);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	default Log getLogger() {
		return LogFactory.getLog(getClass());
	}
	
	default Object getJSON(Object object) {
		try {
			return new ObjectMapper().writeValueAsString(object);
		} catch (JsonProcessingException e) {
			getLogger().error(e.getMessage());
		}

		return null;
	}
	
	default Object getXml(Object object) {
		try {
			StringWriter writer = new StringWriter();
			JAXBContext context = JAXBContext.newInstance(object.getClass());
			Marshaller m = context.createMarshaller();
			m.marshal(object, writer);
			return writer.toString();
		} catch (JAXBException e) {
			getLogger().error(e.getMessage());
		}
		return null;
	}

	default InputStream downloadBoleto(Cobranca cobranca) {
		return null;
	}
	
	MensagemRetornoRepository getMensagemRetrnoRepository();

	default MensagemRetornoDTO getErroPadrao(Uf uf) {
		MensagemRetorno mensagem = getMensagemRetornoRepository().findTop1ByErroPadraoAndUf(true, uf);
		MensagemRetornoDTO retorno = new MensagemRetornoDTO();
		BeanUtils.copyProperties(mensagem, retorno);
		return retorno;
	}
	
	default MensagemRetornoDTO getErro(Uf uf, String codigo) {
		MensagemRetorno mensagem = getMensagemRetornoRepository().findTop1ByCodigoAndUf(codigo, uf);
		MensagemRetornoDTO retorno = new MensagemRetornoDTO();
		BeanUtils.copyProperties(mensagem, retorno);
		retorno.setCodigo(codigo);
		retorno.setSucesso(false);
		return retorno;
	}
	
	default MensagemRetornoDTO getSucesso(Uf uf) {
		MensagemRetorno mensagem = getMensagemRetornoRepository().findTop1BySucessoAndUf(true, uf);
		MensagemRetornoDTO retorno = new MensagemRetornoDTO();
		BeanUtils.copyProperties(mensagem, retorno);
		return retorno;
	}

	MensagemRetornoRepository getMensagemRetornoRepository();

	default void setAcessoInformacoesAcesso(String[] acesso) {
    }


}
